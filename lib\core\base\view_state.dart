/// Enum để định nghĩa các trạng thái của View
enum ViewState {
  idle,
  loading,
  success,
  error,
}

/// Class để wrap data với state
class ViewStateData<T> {
  final ViewState state;
  final T? data;
  final String? errorMessage;

  const ViewStateData({
    required this.state,
    this.data,
    this.errorMessage,
  });

  factory ViewStateData.idle() => const ViewStateData(state: ViewState.idle);
  
  factory ViewStateData.loading() => const ViewStateData(state: ViewState.loading);
  
  factory ViewStateData.success(T data) => ViewStateData(
    state: ViewState.success,
    data: data,
  );
  
  factory ViewStateData.error(String message) => ViewStateData(
    state: ViewState.error,
    errorMessage: message,
  );

  bool get isIdle => state == ViewState.idle;
  bool get isLoading => state == ViewState.loading;
  bool get isSuccess => state == ViewState.success;
  bool get isError => state == ViewState.error;

  ViewStateData<T> copyWith({
    ViewState? state,
    T? data,
    String? errorMessage,
  }) {
    return ViewStateData<T>(
      state: state ?? this.state,
      data: data ?? this.data,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  String toString() {
    return 'ViewStateData(state: $state, data: $data, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ViewStateData<T> &&
        other.state == state &&
        other.data == data &&
        other.errorMessage == errorMessage;
  }

  @override
  int get hashCode => state.hashCode ^ data.hashCode ^ errorMessage.hashCode;
}
