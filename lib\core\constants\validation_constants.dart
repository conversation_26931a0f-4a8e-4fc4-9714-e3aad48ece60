
class ValidationConstants {
  // Password validation
  static const int minPasswordLength = 6;
  static const String passwordLengthMessage = 'Mật khẩu phải có ít nhất 6 ký tự';
  
  // OTP validation
  static const int otpLength = 6;
  static const String otpLengthMessage = 'Mã OTP phải có 6 chữ số';
  
  // Email validation
  static const String emailPattern = r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$';
  static const String emailInvalidMessage = 'Email không hợp lệ';
  
  // Phone validation
  static const String phonePattern = r'^[+]?[0-9]{10,15}$';
  static const String phoneInvalidMessage = 'Số điện thoại không hợp lệ';
  
  // Common messages
  static const String requiredFieldMessage = 'Trường này là bắt buộc';
  static const String passwordMismatchMessage = '<PERSON>ật khẩu xác nhận không khớp';
}
