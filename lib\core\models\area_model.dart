import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'device_model.dart';

part 'area_model.g.dart';

@JsonSerializable()
class Area extends Equatable {
  @Json<PERSON>ey(name: 'area_id')
  final int areaId;

  final String? name;
  final String? description;

  @JsonKey(name: 'home_id')
  final int? homeId;

  @Json<PERSON>ey(name: 'created_at')
  final String? createdAt;

  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  // Thêm trường devices để hỗ trợ API response có devices
  @JsonKey(includeFromJson: false, includeToJson: false)
  final List<Device>? devices;

  const Area({
    required this.areaId,
    this.name,
    this.description,
    this.homeId,
    this.createdAt,
    this.updatedAt,
    this.devices,
  });

  // Custom fromJson với error handling
  factory Area.fromJson(Map<String, dynamic> json) {
    try {
      print('🔍 DEBUG - Parsing Area from JSON: $json');

      // Parse devices array if present
      List<Device>? devicesList;
      if (json.containsKey('devices') && json['devices'] is List) {
        print('🔍 DEBUG - Found devices array, parsing...');
        final devicesData = json['devices'] as List;
        devicesList = devicesData
            .where((deviceJson) => deviceJson != null)
            .map((deviceJson) {
              try {
                print('🔍 DEBUG - Parsing device: $deviceJson');
                // Clean device data - remove nested objects that cause parsing issues
                final deviceMap = Map<String, dynamic>.from(deviceJson as Map<String, dynamic>);
                deviceMap.remove('device_type');
                deviceMap.remove('area');

                final device = Device.fromJson(deviceMap);
                print('✅ DEBUG - Successfully parsed device: ${device.name}');
                return device;
              } catch (e) {
                print('❌ Error parsing device in Area: $e');
                print('Device JSON: $deviceJson');
                return null;
              }
            })
            .where((device) => device != null)
            .cast<Device>()
            .toList();
        print('✅ DEBUG - Parsed ${devicesList.length} devices');
      }

      print('DEBUG - Parsing Area fields...');
      print('area_id: ${json['area_id']}');
      print('name: ${json['name']}');
      print('home_id: ${json['home_id']}');

      final area = Area(
        areaId: _parseId(json['area_id']),
        name: _parseString(json['name']),
        description: _parseString(json['description']),
        homeId: _parseNullableId(json['home_id']),
        createdAt: _parseString(json['created_at']),
        updatedAt: _parseString(json['updated_at']),
        devices: devicesList,
      );

      print('DEBUG - Successfully parsed Area: ${area.displayName}');
      return area;
    } catch (e) {
      print('Error parsing Area: $e');
      print('Problematic json: $json');
      rethrow;
    }
  }

  // Helper methods để parse safely
  static int _parseId(dynamic value) {
    if (value == null) throw ArgumentError('ID cannot be null');
    if (value is int) return value;
    if (value is String) return int.parse(value);
    throw ArgumentError('Invalid ID format: $value');
  }

  static int? _parseNullableId(dynamic value) {
    if (value == null) return null;
    if (value is int) return value;
    if (value is String) return int.parse(value);
    throw ArgumentError('Invalid ID format: $value');
  }

  static String? _parseString(dynamic value) {
    if (value == null) return null;
    return value.toString();
  }

  Map<String, dynamic> toJson() => _$AreaToJson(this);

  @override
  List<Object?> get props => [areaId, name, description, homeId, createdAt, updatedAt, devices];

  // Utility getters
  int get id => areaId; // Getter for compatibility
  String get displayName => name ?? 'Unnamed Area';
  String get safeDescription => description ?? '';
  List<Device> get devicesList => devices ?? [];
  int get deviceCount => devicesList.length;

  // Formatted date getters
  String get formattedCreatedAt {
    if (createdAt == null) return 'Unknown';
    try {
      final date = DateTime.parse(createdAt!);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return createdAt!;
    }
  }

  String get formattedUpdatedAt {
    if (updatedAt == null) return 'Unknown';
    try {
      final date = DateTime.parse(updatedAt!);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return updatedAt!;
    }
  }

  // Copy method
  Area copyWith({
    int? areaId,
    String? name,
    String? description,
    int? homeId,
    String? createdAt,
    String? updatedAt,
    List<Device>? devices,
  }) {
    return Area(
      areaId: areaId ?? this.areaId,
      name: name ?? this.name,
      description: description ?? this.description,
      homeId: homeId ?? this.homeId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      devices: devices ?? this.devices,
    );
  }

  @override
  String toString() {
    return 'Area(areaId: $areaId, name: $displayName, homeId: $homeId)';
  }
}

@JsonSerializable()
class AreaRequest extends Equatable {
  final String name;
  final String? description;

  const AreaRequest({
    required this.name,
    this.description,
  });

  factory AreaRequest.fromJson(Map<String, dynamic> json) => _$AreaRequestFromJson(json);
  Map<String, dynamic> toJson() => _$AreaRequestToJson(this);

  @override
  List<Object?> get props => [name, description];

  // Validation method
  bool get isValid => name.trim().isNotEmpty;

  @override
  String toString() => 'AreaRequest(name: $name, description: $description)';
}

// Response wrapper để handle API responses
@JsonSerializable()
class AreaResponse extends Equatable {
  final bool success;
  final String? message;
  final List<Area>? data;
  final Area? area; // For single area response

  const AreaResponse({
    required this.success,
    this.message,
    this.data,
    this.area,
  });

  factory AreaResponse.fromJson(Map<String, dynamic> json) {
    try {
      // Handle different response structures
      if (json.containsKey('data') && json['data'] is List) {
        // Multiple areas response
        final List<Area> areas = (json['data'] as List)
            .map((item) => Area.fromJson(item as Map<String, dynamic>))
            .toList();
        
        return AreaResponse(
          success: json['success'] ?? true,
          message: json['message'],
          data: areas,
        );
      } else if (json.containsKey('area')) {
        // Single area response
        return AreaResponse(
          success: json['success'] ?? true,
          message: json['message'],
          area: Area.fromJson(json['area'] as Map<String, dynamic>),
        );
      } else {
        // Direct area object
        return AreaResponse(
          success: true,
          area: Area.fromJson(json),
        );
      }
    } catch (e) {
      print('❌ Error parsing AreaResponse: $e');
      return AreaResponse(
        success: false,
        message: 'Failed to parse response: $e',
      );
    }
  }

  Map<String, dynamic> toJson() => _$AreaResponseToJson(this);

  @override
  List<Object?> get props => [success, message, data, area];

  // Helper getters
  List<Area> get areas => data ?? (area != null ? [area!] : []);
  bool get hasData => areas.isNotEmpty;
  String get errorMessage => message ?? 'Unknown error';
}