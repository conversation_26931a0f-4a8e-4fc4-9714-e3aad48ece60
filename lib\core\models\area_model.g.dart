// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'area_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Area _$AreaFromJson(Map<String, dynamic> json) => Area(
      areaId: (json['area_id'] as num).toInt(),
      name: json['name'] as String?,
      description: json['description'] as String?,
      homeId: (json['home_id'] as num?)?.toInt(),
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
    );

Map<String, dynamic> _$AreaToJson(Area instance) => <String, dynamic>{
      'area_id': instance.areaId,
      'name': instance.name,
      'description': instance.description,
      'home_id': instance.homeId,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

AreaRequest _$AreaRequestFromJson(Map<String, dynamic> json) => AreaRequest(
      name: json['name'] as String,
      description: json['description'] as String?,
    );

Map<String, dynamic> _$AreaRequestToJson(AreaRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'description': instance.description,
    };

AreaResponse _$AreaResponseFromJson(Map<String, dynamic> json) => AreaResponse(
      success: json['success'] as bool,
      message: json['message'] as String?,
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => Area.fromJson(e as Map<String, dynamic>))
          .toList(),
      area: json['area'] == null
          ? null
          : Area.fromJson(json['area'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$AreaResponseToJson(AreaResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
      'area': instance.area,
    };
