import 'package:fe_flutter/core/models/user_model.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:equatable/equatable.dart';


part 'auth_response.g.dart';

@JsonSerializable()
class AuthResponse extends Equatable {
  @J<PERSON><PERSON>ey(name: 'token')
  final String token;

  final bool success;

  final String message;

  final User user;

  const AuthResponse({
    required this.token,
    required this.success,
    required this.message,
    required this.user,
  });

  // Getter for backward compatibility
  String get accessToken => token;

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    try {
      return _$AuthResponseFromJson(json);
    } catch (e) {
      throw FormatException('Lỗi định dạng JSON: $e');
    }
  }

  Map<String, dynamic> toJson() => _$AuthResponseToJson(this);

  @override
  List<Object?> get props => [token, user, success, message];

  bool get isValid => token.isNotEmpty;
}