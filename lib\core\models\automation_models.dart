class AutomationRule {
  final int? ruleId;
  final String name;
  final String description;
  final int homeId;
  final String ruleType; // 'schedule' or 'condition'
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? lastExecuted;
  final int? executionCount;
  final AutomationSchedule? schedule;
  final List<AutomationCondition>? conditions;
  final String? conditionLogic; // 'AND' or 'OR'
  final List<AutomationAction> actions;

  AutomationRule({
    this.ruleId,
    required this.name,
    required this.description,
    required this.homeId,
    required this.ruleType,
    required this.isActive,
    this.createdAt,
    this.lastExecuted,
    this.executionCount,
    this.schedule,
    this.conditions,
    this.conditionLogic,
    required this.actions,
  });

  factory AutomationRule.fromJson(Map<String, dynamic> json) {
    return AutomationRule(
      ruleId: json['rule_id'],
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      homeId: json['home_id'] ?? 0,
      ruleType: json['rule_type'] ?? '',
      isActive: json['is_active'] ?? false,
      createdAt: json['created_at'] != null 
          ? DateTime.parse(json['created_at']) 
          : null,
      lastExecuted: json['last_executed'] != null 
          ? DateTime.parse(json['last_executed']) 
          : null,
      executionCount: json['execution_count'],
      schedule: json['schedule'] != null 
          ? AutomationSchedule.fromJson(json['schedule']) 
          : null,
      conditions: json['conditions'] != null
          ? (json['conditions'] as List)
              .map((c) => AutomationCondition.fromJson(c))
              .toList()
          : null,
      conditionLogic: json['condition_logic'],
      actions: json['actions'] != null
          ? (json['actions'] as List)
              .map((a) => AutomationAction.fromJson(a))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'name': name,
      'description': description,
      'home_id': homeId,
      'rule_type': ruleType,
      'is_active': isActive,
      'actions': actions.map((a) => a.toJson()).toList(),
    };

    if (ruleId != null) data['rule_id'] = ruleId;
    if (schedule != null) data['schedule'] = schedule!.toJson();
    if (conditions != null) {
      data['conditions'] = conditions!.map((c) => c.toJson()).toList();
    }
    if (conditionLogic != null) data['condition_logic'] = conditionLogic;

    return data;
  }

  AutomationRule copyWith({
    int? ruleId,
    String? name,
    String? description,
    int? homeId,
    String? ruleType,
    bool? isActive,
    DateTime? createdAt,
    DateTime? lastExecuted,
    int? executionCount,
    AutomationSchedule? schedule,
    List<AutomationCondition>? conditions,
    String? conditionLogic,
    List<AutomationAction>? actions,
  }) {
    return AutomationRule(
      ruleId: ruleId ?? this.ruleId,
      name: name ?? this.name,
      description: description ?? this.description,
      homeId: homeId ?? this.homeId,
      ruleType: ruleType ?? this.ruleType,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      lastExecuted: lastExecuted ?? this.lastExecuted,
      executionCount: executionCount ?? this.executionCount,
      schedule: schedule ?? this.schedule,
      conditions: conditions ?? this.conditions,
      conditionLogic: conditionLogic ?? this.conditionLogic,
      actions: actions ?? this.actions,
    );
  }
}

class AutomationSchedule {
  final String time; // HH:MM format
  final List<String> days; // ['monday', 'tuesday', ...]
  final String timezone;
  final String? repeat; // 'daily', 'weekly', 'monthly'

  AutomationSchedule({
    required this.time,
    required this.days,
    required this.timezone,
    this.repeat,
  });

  factory AutomationSchedule.fromJson(Map<String, dynamic> json) {
    return AutomationSchedule(
      time: json['time'] ?? '',
      days: json['days'] != null 
          ? List<String>.from(json['days']) 
          : [],
      timezone: json['timezone'] ?? 'Asia/Ho_Chi_Minh',
      repeat: json['repeat'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'time': time,
      'days': days,
      'timezone': timezone,
    };
    if (repeat != null) data['repeat'] = repeat;
    return data;
  }

  AutomationSchedule copyWith({
    String? time,
    List<String>? days,
    String? timezone,
    String? repeat,
  }) {
    return AutomationSchedule(
      time: time ?? this.time,
      days: days ?? this.days,
      timezone: timezone ?? this.timezone,
      repeat: repeat ?? this.repeat,
    );
  }
}

class AutomationCondition {
  int deviceId;
  int? propertyId;
  String propertyName;
  String operator;
  String value;
  String? logicalOperator; // AND/OR cho module 2

  AutomationCondition({
    required this.deviceId,
    this.propertyId,
    required this.propertyName,
    required this.operator,
    required this.value,
    this.logicalOperator,
  });

  factory AutomationCondition.fromJson(Map<String, dynamic> json) {
    return AutomationCondition(
      deviceId: json['device_id'] ?? 0,
      propertyId: json['property_id'],
      propertyName: json['property_name'] ?? '',
      operator: json['operator'] ?? '',
      value: json['value'] ?? '',
      logicalOperator: json['logical_operator'],
    );
  }

  Map<String, dynamic> toJson() => {
    "device_id": deviceId,
    "property_id": propertyId,
    "property_name": propertyName,
    "operator": operator,
    "value": value,
    if (logicalOperator != null) "logical_operator": logicalOperator,
  };
}

class AutomationAction {
  final int deviceId;
  final String? deviceName;
  final String command;
  final String value;
  final int? delay; // seconds

  AutomationAction({
    required this.deviceId,
    this.deviceName,
    required this.command,
    required this.value,
    this.delay,
  });

  factory AutomationAction.fromJson(Map<String, dynamic> json) {
    return AutomationAction(
      deviceId: json['device_id'] ?? 0,
      deviceName: json['device_name'],
      command: json['command'] ?? '',
      value: json['value'] ?? '',
      delay: json['delay'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'device_id': deviceId,
      'command': command,
      'value': value,
    };
    if (delay != null) data['delay'] = delay;
    return data;
  }

  AutomationAction copyWith({
    int? deviceId,
    String? deviceName,
    String? command,
    String? value,
    int? delay,
  }) {
    return AutomationAction(
      deviceId: deviceId ?? this.deviceId,
      deviceName: deviceName ?? this.deviceName,
      command: command ?? this.command,
      value: value ?? this.value,
      delay: delay ?? this.delay,
    );
  }
}

class AutomationExecution {
  final String executionId;
  final int ruleId;
  final String ruleName;
  final DateTime executedAt;
  final String triggerType; // 'schedule', 'condition', 'manual'
  final bool success;
  final List<AutomationExecutionResult> results;

  AutomationExecution({
    required this.executionId,
    required this.ruleId,
    required this.ruleName,
    required this.executedAt,
    required this.triggerType,
    required this.success,
    required this.results,
  });

  factory AutomationExecution.fromJson(Map<String, dynamic> json) {
    return AutomationExecution(
      executionId: json['execution_id'] ?? '',
      ruleId: json['rule_id'] ?? 0,
      ruleName: json['rule_name'] ?? '',
      executedAt: json['executed_at'] != null
          ? DateTime.parse(json['executed_at'])
          : DateTime.now(),
      triggerType: json['trigger_type'] ?? '',
      success: json['success'] ?? false,
      results: json['results'] != null
          ? (json['results'] as List)
              .map((r) => AutomationExecutionResult.fromJson(r))
              .toList()
          : [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'execution_id': executionId,
      'rule_id': ruleId,
      'rule_name': ruleName,
      'executed_at': executedAt.toIso8601String(),
      'trigger_type': triggerType,
      'success': success,
      'results': results.map((r) => r.toJson()).toList(),
    };
  }
}

class AutomationExecutionResult {
  final int deviceId;
  final String? deviceName;
  final String command;
  final bool success;
  final String? message;

  AutomationExecutionResult({
    required this.deviceId,
    this.deviceName,
    required this.command,
    required this.success,
    this.message,
  });

  factory AutomationExecutionResult.fromJson(Map<String, dynamic> json) {
    return AutomationExecutionResult(
      deviceId: json['device_id'] ?? 0,
      deviceName: json['device_name'],
      command: json['command'] ?? '',
      success: json['success'] ?? false,
      message: json['message'],
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = {
      'device_id': deviceId,
      'command': command,
      'success': success,
    };
    if (deviceName != null) data['device_name'] = deviceName;
    if (message != null) data['message'] = message;
    return data;
  }
}

// Enums and Constants
class AutomationRuleType {
  static const String schedule = 'schedule';
  static const String condition = 'condition';
  static const String ifThen = 'if_then';
}

class AutomationOperator {
  static const String equals = '=';
  static const String notEquals = '!=';
  static const String greaterThan = '>';
  static const String lessThan = '<';
  static const String greaterThanOrEqual = '>=';
  static const String lessThanOrEqual = '<=';

  static List<String> get all => [
    equals, notEquals, greaterThan, lessThan,
    greaterThanOrEqual, lessThanOrEqual
  ];

  static String getDisplayName(String operator) {
    switch (operator) {
      case equals: return 'Bằng';
      case notEquals: return 'Khác';
      case greaterThan: return 'Lớn hơn';
      case lessThan: return 'Nhỏ hơn';
      case greaterThanOrEqual: return 'Lớn hơn hoặc bằng';
      case lessThanOrEqual: return 'Nhỏ hơn hoặc bằng';
      default: return operator;
    }
  }
}

class AutomationConditionLogic {
  static const String and = 'AND';
  static const String or = 'OR';
}

class AutomationTriggerType {
  static const String schedule = 'schedule';
  static const String condition = 'condition';
  static const String manual = 'manual';
}

class AutomationDays {
  static const String monday = 'monday';
  static const String tuesday = 'tuesday';
  static const String wednesday = 'wednesday';
  static const String thursday = 'thursday';
  static const String friday = 'friday';
  static const String saturday = 'saturday';
  static const String sunday = 'sunday';

  static List<String> get all => [
    monday, tuesday, wednesday, thursday, friday, saturday, sunday
  ];

  static String getDisplayName(String day) {
    switch (day) {
      case monday: return 'Thứ 2';
      case tuesday: return 'Thứ 3';
      case wednesday: return 'Thứ 4';
      case thursday: return 'Thứ 5';
      case friday: return 'Thứ 6';
      case saturday: return 'Thứ 7';
      case sunday: return 'Chủ nhật';
      default: return day;
    }
  }

  static String getShortDisplayName(String day) {
    switch (day) {
      case monday: return 'T2';
      case tuesday: return 'T3';
      case wednesday: return 'T4';
      case thursday: return 'T5';
      case friday: return 'T6';
      case saturday: return 'T7';
      case sunday: return 'CN';
      default: return day;
    }
  }
}

// AutomationAlert removed - no longer needed
