import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'device_model.g.dart';

@JsonSerializable()
class NetworkInfo extends Equatable {
  @JsonKey(name: 'device_id')
  final int deviceId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'ip_address')
  final String? ipAddress;

  @Json<PERSON>ey(name: 'mac_address')
  final String? macAddress;

  @JsonKey(name: 'subnet_mask')
  final String? subnetMask;

  final String? gateway;

  @JsonKey(name: 'dns_server')
  final String? dnsServer;

  @JsonKey(name: 'last_connected')
  final String? lastConnected;

  const NetworkInfo({
    required this.deviceId,
    this.ipAddress,
    this.macAddress,
    this.subnetMask,
    this.gateway,
    this.dnsServer,
    this.lastConnected,
  });

  factory NetworkInfo.fromJson(Map<String, dynamic> json) => _$NetworkInfoFromJson(json);
  Map<String, dynamic> toJson() => _$NetworkInfoToJson(this);

  @override
  List<Object?> get props => [
    deviceId,
    ipAddress,
    macAddress,
    subnetMask,
    gateway,
    dnsServer,
    lastConnected,
  ];
}

@JsonSerializable()
class Device extends Equatable {
  @JsonKey(name: 'device_id')
  final int deviceId;

  final String name;

  @JsonKey(name: 'device_type_id')
  final int deviceTypeId;

  @JsonKey(name: 'home_id')
  final int? homeId;

  @JsonKey(name: 'area_id')
  final int? areaId;

  @JsonKey(name: 'connection_type_id')
  final int connectionTypeId;

  @JsonKey(name: 'unique_identifier')
  final String uniqueIdentifier;

  @JsonKey(name: 'firmware_version_id')
  final int? firmwareVersionId;

  @JsonKey(name: 'ip_address')
  final String? ipAddress;

  @JsonKey(name: 'network_info')
  final NetworkInfo? networkInfo;

  @JsonKey(name: 'is_online')
  final bool isOnline;

  final String? status;

  final String? description;

  @JsonKey(name: 'created_at')
  final String createdAt;

  @JsonKey(name: 'updated_at')
  final String updatedAt;

  const Device({
    required this.deviceId,
    required this.name,
    required this.deviceTypeId,
    this.homeId,
    this.areaId,
    required this.connectionTypeId,
    required this.uniqueIdentifier,
    this.firmwareVersionId,
    this.ipAddress,
    this.networkInfo,
    required this.isOnline,
    this.status,
    this.description,
    required this.createdAt,
    required this.updatedAt,
  });

  int get id => deviceId;

  String? get type {
    switch (deviceTypeId) {
      case 1:
        return 'Smart Light';
      case 2:
        return 'Light Sensor';
      case 3:
        return 'Camera';
      case 4:
        return 'Temperature Sensor';
      case 5:
        return 'Smart AC';
      case 24:
        return 'smart Switch';
      default:
        return 'Unknown Device';
    }
  }

  /// Check if device can be controlled (not a sensor)
  bool get isControllable {
    switch (deviceTypeId) {
      case 1: // Smart Light
      case 3: // Camera (có thể bật/tắt)
      case 5: // Smart AC
      case 24: // Smart Switch
        return true;
      case 2: // Light Sensor
      case 4: // Temperature Sensor
        return false; // Sensors chỉ đọc dữ liệu
      default:
        return false; // Unknown devices không điều khiển được
    }
  }

  factory Device.fromJson(Map<String, dynamic> json) {
    try {
      final deviceId = json['device_id'];
      final deviceName = json['name'];

      if (deviceId == null) {
        throw ArgumentError('device_id cannot be null');
      }

      if (deviceName == null) {
        throw ArgumentError('name cannot be null');
      }

      NetworkInfo? networkInfo;
      if (json['network_info'] != null) {
        try {
          networkInfo = NetworkInfo.fromJson(json['network_info'] as Map<String, dynamic>);
        } catch (e) {
          print('Error parsing NetworkInfo: $e');
        }
      }

      return Device(
        deviceId: (deviceId as num).toInt(),
        name: deviceName.toString(),
        deviceTypeId: (json['device_type_id'] as num?)?.toInt() ?? 1,
        homeId: (json['home_id'] as num?)?.toInt(),
        areaId: (json['area_id'] as num?)?.toInt(),
        connectionTypeId: (json['connection_type_id'] as num?)?.toInt() ?? 1,
        uniqueIdentifier: json['unique_identifier']?.toString() ?? '',
        firmwareVersionId: (json['firmware_version_id'] as num?)?.toInt(),
        ipAddress: json['ip_address']?.toString() ?? networkInfo?.ipAddress,
        networkInfo: networkInfo,
        isOnline: json['is_online'] as bool? ?? false,
        status: json['status']?.toString(),
        description: json['description']?.toString(),
        createdAt: json['created_at']?.toString() ?? '',
        updatedAt: json['updated_at']?.toString() ?? '',
      );
    } catch (e) {
      print('Error parsing Device from JSON: $e');
      print('Problematic JSON: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() => _$DeviceToJson(this);

  @override
  List<Object?> get props => [
    deviceId,
    name,
    deviceTypeId,
    homeId,
    areaId,
    connectionTypeId,
    uniqueIdentifier,
    firmwareVersionId,
    ipAddress,
    networkInfo,
    isOnline,
    status,
    description,
    createdAt,
    updatedAt
  ];
}

@JsonSerializable()
class DeviceRequest extends Equatable {
  final String name;
  final String? type;
  final String? description;
  @JsonKey(name: 'area_id')
  final int? areaId;

  const DeviceRequest({
    required this.name,
    this.type,
    this.description,
    this.areaId,
  });

  factory DeviceRequest.fromJson(Map<String, dynamic> json) => _$DeviceRequestFromJson(json);
  Map<String, dynamic> toJson() => _$DeviceRequestToJson(this);

  @override
  List<Object?> get props => [name, type, description, areaId];
}
