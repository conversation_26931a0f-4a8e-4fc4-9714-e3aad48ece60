// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'device_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

NetworkInfo _$NetworkInfoFromJson(Map<String, dynamic> json) => NetworkInfo(
      deviceId: (json['device_id'] as num).toInt(),
      ipAddress: json['ip_address'] as String?,
      macAddress: json['mac_address'] as String?,
      subnetMask: json['subnet_mask'] as String?,
      gateway: json['gateway'] as String?,
      dnsServer: json['dns_server'] as String?,
      lastConnected: json['last_connected'] as String?,
    );

Map<String, dynamic> _$NetworkInfoToJson(NetworkInfo instance) =>
    <String, dynamic>{
      'device_id': instance.deviceId,
      'ip_address': instance.ipAddress,
      'mac_address': instance.macAddress,
      'subnet_mask': instance.subnetMask,
      'gateway': instance.gateway,
      'dns_server': instance.dnsServer,
      'last_connected': instance.lastConnected,
    };

Device _$DeviceFromJson(Map<String, dynamic> json) => Device(
      deviceId: (json['device_id'] as num).toInt(),
      name: json['name'] as String,
      deviceTypeId: (json['device_type_id'] as num).toInt(),
      homeId: (json['home_id'] as num?)?.toInt(),
      areaId: (json['area_id'] as num?)?.toInt(),
      connectionTypeId: (json['connection_type_id'] as num).toInt(),
      uniqueIdentifier: json['unique_identifier'] as String,
      firmwareVersionId: (json['firmware_version_id'] as num?)?.toInt(),
      ipAddress: json['ip_address'] as String?,
      networkInfo: json['network_info'] == null
          ? null
          : NetworkInfo.fromJson(json['network_info'] as Map<String, dynamic>),
      isOnline: json['is_online'] as bool,
      status: json['status'] as String?,
      description: json['description'] as String?,
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
    );

Map<String, dynamic> _$DeviceToJson(Device instance) => <String, dynamic>{
      'device_id': instance.deviceId,
      'name': instance.name,
      'device_type_id': instance.deviceTypeId,
      'home_id': instance.homeId,
      'area_id': instance.areaId,
      'connection_type_id': instance.connectionTypeId,
      'unique_identifier': instance.uniqueIdentifier,
      'firmware_version_id': instance.firmwareVersionId,
      'ip_address': instance.ipAddress,
      'network_info': instance.networkInfo,
      'is_online': instance.isOnline,
      'status': instance.status,
      'description': instance.description,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
    };

DeviceRequest _$DeviceRequestFromJson(Map<String, dynamic> json) =>
    DeviceRequest(
      name: json['name'] as String,
      type: json['type'] as String?,
      description: json['description'] as String?,
      areaId: (json['area_id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$DeviceRequestToJson(DeviceRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'type': instance.type,
      'description': instance.description,
      'area_id': instance.areaId,
    };
