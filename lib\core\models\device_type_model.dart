import 'package:flutter/material.dart';

/// Model cho Device Type
class DeviceType {
  final int? deviceTypeId;
  final String name;
  final String description;
  final String? iconName;
  final String? colorCode;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<String>? supportedCommands;
  final Map<String, dynamic>? defaultProperties;

  const DeviceType({
    this.deviceTypeId,
    required this.name,
    required this.description,
    this.iconName,
    this.colorCode,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
    this.supportedCommands,
    this.defaultProperties,
  });

  /// Create DeviceType from JSON
  factory DeviceType.fromJson(Map<String, dynamic> json) {
    return DeviceType(
      deviceTypeId: json['device_type_id'],
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      iconName: json['icon_name'],
      colorCode: json['color_code'],
      isActive: json['is_active'] ?? true,
      createdAt: json['created_at'] != null 
          ? DateTime.tryParse(json['created_at']) 
          : null,
      updatedAt: json['updated_at'] != null 
          ? DateTime.tryParse(json['updated_at']) 
          : null,
      supportedCommands: json['supported_commands'] != null
          ? List<String>.from(json['supported_commands'])
          : null,
      defaultProperties: json['default_properties'],
    );
  }

  /// Convert DeviceType to JSON
  Map<String, dynamic> toJson() {
    return {
      if (deviceTypeId != null) 'device_type_id': deviceTypeId,
      'name': name,
      'description': description,
      if (iconName != null) 'icon_name': iconName,
      if (colorCode != null) 'color_code': colorCode,
      'is_active': isActive,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
      if (supportedCommands != null) 'supported_commands': supportedCommands,
      if (defaultProperties != null) 'default_properties': defaultProperties,
    };
  }

  /// Create a copy with updated fields
  DeviceType copyWith({
    int? deviceTypeId,
    String? name,
    String? description,
    String? iconName,
    String? colorCode,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? supportedCommands,
    Map<String, dynamic>? defaultProperties,
  }) {
    return DeviceType(
      deviceTypeId: deviceTypeId ?? this.deviceTypeId,
      name: name ?? this.name,
      description: description ?? this.description,
      iconName: iconName ?? this.iconName,
      colorCode: colorCode ?? this.colorCode,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      supportedCommands: supportedCommands ?? this.supportedCommands,
      defaultProperties: defaultProperties ?? this.defaultProperties,
    );
  }

  /// Get icon for device type
  IconData get icon {
    // First try by iconName if available
    if (iconName != null) {
      switch (iconName!.toLowerCase()) {
        case 'light':
        case 'lightbulb':
          return Icons.lightbulb;
        case 'fan':
          return Icons.air;
        case 'switch':
          return Icons.toggle_on;
        case 'sensor':
          return Icons.sensors;
        case 'camera':
          return Icons.camera_alt;
        case 'lock':
          return Icons.lock;
        case 'thermostat':
          return Icons.thermostat;
        case 'speaker':
          return Icons.speaker;
        case 'tv':
          return Icons.tv;
        case 'router':
          return Icons.router;
        case 'security':
          return Icons.security;
        case 'door':
          return Icons.door_front_door;
        case 'window':
          return Icons.window;
        case 'garage':
          return Icons.garage;
      }
    }

    // Fallback to name-based mapping for your database
    switch (name.toLowerCase()) {
      case 'smart light':
      case 'light dimmer':
        return Icons.lightbulb;
      case 'thermostat':
      case 'smart thermometer':
        return Icons.thermostat;
      case 'security camera':
      case 'doorbell camera':
        return Icons.camera_alt;
      case 'smart plug':
        return Icons.power;
      case 'motion sensor':
      case 'motion detector':
        return Icons.sensors;
      case 'air purifier':
        return Icons.air;
      case 'smart lock':
        return Icons.lock;
      case 'window sensor':
      case 'door sensor':
        return Icons.sensor_door;
      case 'smoke detector':
        return Icons.smoke_free;
      case 'gas leak sensor':
        return Icons.local_gas_station;
      case 'irrigation controller':
        return Icons.water_drop;
      case 'temperature sensor':
        return Icons.thermostat;
      case 'humidity sensor':
        return Icons.opacity;
      case 'heart rate monitor':
        return Icons.favorite;
      case 'key tracker':
        return Icons.key;
      case 'remote button':
        return Icons.radio_button_checked;
      case 'indoor beacon':
        return Icons.bluetooth_searching;
      case 'smart scale':
        return Icons.scale;
      default:
        return Icons.device_hub;
    }
  }

  /// Get color for device type
  Color get color {
    if (colorCode != null) {
      try {
        return Color(int.parse(colorCode!.replaceFirst('#', '0xFF')));
      } catch (e) {
        // Fallback to default color if parsing fails
      }
    }
    
    // Default colors based on device type from your database
    switch (name.toLowerCase()) {
      case 'smart light':
      case 'light dimmer':
        return Colors.amber;
      case 'thermostat':
      case 'smart thermometer':
        return Colors.blue;
      case 'security camera':
      case 'doorbell camera':
        return Colors.purple;
      case 'smart plug':
        return Colors.green;
      case 'motion sensor':
      case 'motion detector':
      case 'temperature sensor':
      case 'humidity sensor':
      case 'window sensor':
      case 'door sensor':
        return Colors.orange;
      case 'air purifier':
        return Colors.cyan;
      case 'smart lock':
        return Colors.red;
      case 'smoke detector':
        return Colors.deepOrange;
      case 'gas leak sensor':
        return Colors.yellow;
      case 'irrigation controller':
        return Colors.lightBlue;
      case 'heart rate monitor':
        return Colors.pink;
      case 'key tracker':
        return Colors.brown;
      case 'remote button':
        return Colors.indigo;
      case 'indoor beacon':
        return Colors.teal;
      case 'smart scale':
        return Colors.grey;
      default:
        return Colors.blueGrey;
    }
  }

  @override
  String toString() {
    return 'DeviceType(id: $deviceTypeId, name: $name, description: $description)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DeviceType &&
        other.deviceTypeId == deviceTypeId &&
        other.name == name;
  }

  @override
  int get hashCode => deviceTypeId.hashCode ^ name.hashCode;
}

/// Request model for creating/updating device type
class DeviceTypeRequest {
  final String name;
  final String description;
  final String? iconName;
  final String? colorCode;
  final bool isActive;
  final List<String>? supportedCommands;
  final Map<String, dynamic>? defaultProperties;

  const DeviceTypeRequest({
    required this.name,
    required this.description,
    this.iconName,
    this.colorCode,
    this.isActive = true,
    this.supportedCommands,
    this.defaultProperties,
  });

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      if (iconName != null) 'icon_name': iconName,
      if (colorCode != null) 'color_code': colorCode,
      'is_active': isActive,
      if (supportedCommands != null) 'supported_commands': supportedCommands,
      if (defaultProperties != null) 'default_properties': defaultProperties,
    };
  }
}

/// Device type templates - removed mock data, use API data only
class DeviceTypeTemplates {
  // No more static templates - all device types should come from API
  static const List<Map<String, dynamic>> templates = [];

  static List<DeviceType> getTemplateDeviceTypes() {
    return []; // Return empty list - use API data only
  }
}
