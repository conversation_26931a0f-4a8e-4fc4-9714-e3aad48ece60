import 'package:json_annotation/json_annotation.dart';

part 'esp32_device_model.g.dart';

/// Model for ESP32 device discovered in LAN
@JsonSerializable()
class ESP32Device {
  @Json<PERSON><PERSON>(name: 'ip')
  final String ipAddress;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'port')
  final int port;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'name')
  final String deviceName;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'type')
  final String deviceType;
  
  @Json<PERSON>ey(name: 'mac_address')
  final String? macAddress;
  
  @Json<PERSON>ey(name: 'firmware_version')
  final String? firmwareVersion;
  
  @Json<PERSON>ey(name: 'chip_id')
  final String? chipId;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'free_heap')
  final int? freeHeap;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'total_heap')
  final int? totalHeap;
  
  @Json<PERSON>ey(name: 'uptime')
  final int? uptime;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'wifi_ssid')
  final String? wifiSSID;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'wifi_rssi')
  final int? wifiRSSI;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_online')
  final bool isOnline;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_seen')
  final DateTime lastSeen;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'response_time')
  final int? responseTime; // in milliseconds
  
  @JsonKey(name: 'endpoints')
  final List<String>? availableEndpoints;
  
  @JsonKey(name: 'capabilities')
  final List<String>? capabilities;
  
  @JsonKey(name: 'additional_info')
  final Map<String, dynamic>? additionalInfo;
  
  ESP32Device({
    required this.ipAddress,
    required this.port,
    required this.deviceName,
    required this.deviceType,
    this.macAddress,
    this.firmwareVersion,
    this.chipId,
    this.freeHeap,
    this.totalHeap,
    this.uptime,
    this.wifiSSID,
    this.wifiRSSI,
    required this.isOnline,
    required this.lastSeen,
    this.responseTime,
    this.availableEndpoints,
    this.capabilities,
    this.additionalInfo,
  });
  
  /// Full address (IP:Port)
  String get fullAddress => '$ipAddress:$port';
  
  /// Base URL for HTTP requests
  String get baseUrl => 'http://$ipAddress:$port';
  
  /// Memory usage percentage
  double? get memoryUsage {
    if (freeHeap != null && totalHeap != null && totalHeap! > 0) {
      return ((totalHeap! - freeHeap!) / totalHeap!) * 100;
    }
    return null;
  }
  
  /// WiFi signal strength description
  String get wifiSignalStrength {
    if (wifiRSSI == null) return 'Unknown';
    
    if (wifiRSSI! >= -30) return 'Excellent';
    if (wifiRSSI! >= -50) return 'Good';
    if (wifiRSSI! >= -60) return 'Fair';
    if (wifiRSSI! >= -70) return 'Weak';
    return 'Very Weak';
  }
  
  /// Device status description
  String get statusDescription {
    if (!isOnline) return 'Offline';
    
    final now = DateTime.now();
    final diff = now.difference(lastSeen);
    
    if (diff.inMinutes < 1) return 'Online';
    if (diff.inMinutes < 5) return 'Recently seen';
    if (diff.inHours < 1) return 'Seen ${diff.inMinutes}m ago';
    if (diff.inDays < 1) return 'Seen ${diff.inHours}h ago';
    return 'Seen ${diff.inDays}d ago';
  }
  
  /// Check if device supports specific capability
  bool hasCapability(String capability) {
    return capabilities?.contains(capability) ?? false;
  }
  
  /// Check if device has specific endpoint
  bool hasEndpoint(String endpoint) {
    return availableEndpoints?.contains(endpoint) ?? false;
  }
  
  /// Get formatted uptime
  String get formattedUptime {
    if (uptime == null) return 'Unknown';
    
    final seconds = uptime!;
    final days = seconds ~/ 86400;
    final hours = (seconds % 86400) ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final secs = seconds % 60;
    
    if (days > 0) {
      return '${days}d ${hours}h ${minutes}m';
    } else if (hours > 0) {
      return '${hours}h ${minutes}m ${secs}s';
    } else if (minutes > 0) {
      return '${minutes}m ${secs}s';
    } else {
      return '${secs}s';
    }
  }
  
  /// Get formatted memory info
  String get formattedMemory {
    if (freeHeap == null) return 'Unknown';
    
    final free = _formatBytes(freeHeap!);
    if (totalHeap != null) {
      final total = _formatBytes(totalHeap!);
      final usage = memoryUsage?.toStringAsFixed(1) ?? '0';
      return '$free / $total (${usage}% used)';
    }
    
    return '$free free';
  }
  
  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
  }
  
  /// Create copy with updated fields
  ESP32Device copyWith({
    String? ipAddress,
    int? port,
    String? deviceName,
    String? deviceType,
    String? macAddress,
    String? firmwareVersion,
    String? chipId,
    int? freeHeap,
    int? totalHeap,
    int? uptime,
    String? wifiSSID,
    int? wifiRSSI,
    bool? isOnline,
    DateTime? lastSeen,
    int? responseTime,
    List<String>? availableEndpoints,
    List<String>? capabilities,
    Map<String, dynamic>? additionalInfo,
  }) {
    return ESP32Device(
      ipAddress: ipAddress ?? this.ipAddress,
      port: port ?? this.port,
      deviceName: deviceName ?? this.deviceName,
      deviceType: deviceType ?? this.deviceType,
      macAddress: macAddress ?? this.macAddress,
      firmwareVersion: firmwareVersion ?? this.firmwareVersion,
      chipId: chipId ?? this.chipId,
      freeHeap: freeHeap ?? this.freeHeap,
      totalHeap: totalHeap ?? this.totalHeap,
      uptime: uptime ?? this.uptime,
      wifiSSID: wifiSSID ?? this.wifiSSID,
      wifiRSSI: wifiRSSI ?? this.wifiRSSI,
      isOnline: isOnline ?? this.isOnline,
      lastSeen: lastSeen ?? this.lastSeen,
      responseTime: responseTime ?? this.responseTime,
      availableEndpoints: availableEndpoints ?? this.availableEndpoints,
      capabilities: capabilities ?? this.capabilities,
      additionalInfo: additionalInfo ?? this.additionalInfo,
    );
  }
  
  factory ESP32Device.fromJson(Map<String, dynamic> json) => _$ESP32DeviceFromJson(json);
  Map<String, dynamic> toJson() => _$ESP32DeviceToJson(this);
  
  @override
  String toString() {
    return 'ESP32Device(name: $deviceName, ip: $ipAddress:$port, online: $isOnline)';
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ESP32Device &&
        other.ipAddress == ipAddress &&
        other.port == port;
  }
  
  @override
  int get hashCode => ipAddress.hashCode ^ port.hashCode;
}

/// Model for LAN scan result
@JsonSerializable()
class LanScanResult {
  @JsonKey(name: 'subnet')
  final String subnet;
  
  @JsonKey(name: 'scan_duration')
  final int scanDurationMs;
  
  @JsonKey(name: 'devices_found')
  final int devicesFound;
  
  @JsonKey(name: 'esp32_devices')
  final List<ESP32Device> esp32Devices;
  
  @JsonKey(name: 'other_devices')
  final List<Map<String, dynamic>> otherDevices;
  
  @JsonKey(name: 'scan_timestamp')
  final DateTime scanTimestamp;
  
  @JsonKey(name: 'scan_range')
  final String scanRange;
  
  LanScanResult({
    required this.subnet,
    required this.scanDurationMs,
    required this.devicesFound,
    required this.esp32Devices,
    required this.otherDevices,
    required this.scanTimestamp,
    required this.scanRange,
  });
  
  /// Get scan duration in seconds
  double get scanDurationSeconds => scanDurationMs / 1000.0;
  
  /// Get formatted scan duration
  String get formattedScanDuration {
    if (scanDurationMs < 1000) {
      return '${scanDurationMs}ms';
    } else {
      return '${scanDurationSeconds.toStringAsFixed(1)}s';
    }
  }
  
  factory LanScanResult.fromJson(Map<String, dynamic> json) => _$LanScanResultFromJson(json);
  Map<String, dynamic> toJson() => _$LanScanResultToJson(this);
}

/// ESP32 device capabilities
class ESP32Capabilities {
  static const String wifi = 'wifi';
  static const String bluetooth = 'bluetooth';
  static const String sensors = 'sensors';
  static const String actuators = 'actuators';
  static const String gpio = 'gpio';
  static const String pwm = 'pwm';
  static const String adc = 'adc';
  static const String i2c = 'i2c';
  static const String spi = 'spi';
  static const String uart = 'uart';
  static const String ota = 'ota';
  static const String webserver = 'webserver';
  static const String mqtt = 'mqtt';

  static const List<String> all = [
    wifi, bluetooth, sensors, actuators, gpio, pwm, adc,
    i2c, spi, uart, ota, webserver, mqtt,
  ];
}
