// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'esp32_device_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ESP32Device _$ESP32DeviceFromJson(Map<String, dynamic> json) => ESP32Device(
      ipAddress: json['ip'] as String,
      port: (json['port'] as num).toInt(),
      deviceName: json['name'] as String,
      deviceType: json['type'] as String,
      macAddress: json['mac_address'] as String?,
      firmwareVersion: json['firmware_version'] as String?,
      chipId: json['chip_id'] as String?,
      freeHeap: (json['free_heap'] as num?)?.toInt(),
      totalHeap: (json['total_heap'] as num?)?.toInt(),
      uptime: (json['uptime'] as num?)?.toInt(),
      wifiSSID: json['wifi_ssid'] as String?,
      wifiRSSI: (json['wifi_rssi'] as num?)?.toInt(),
      isOnline: json['is_online'] as bool,
      lastSeen: DateTime.parse(json['last_seen'] as String),
      responseTime: (json['response_time'] as num?)?.toInt(),
      availableEndpoints: (json['endpoints'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      capabilities: (json['capabilities'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList(),
      additionalInfo: json['additional_info'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$ESP32DeviceToJson(ESP32Device instance) =>
    <String, dynamic>{
      'ip': instance.ipAddress,
      'port': instance.port,
      'name': instance.deviceName,
      'type': instance.deviceType,
      'mac_address': instance.macAddress,
      'firmware_version': instance.firmwareVersion,
      'chip_id': instance.chipId,
      'free_heap': instance.freeHeap,
      'total_heap': instance.totalHeap,
      'uptime': instance.uptime,
      'wifi_ssid': instance.wifiSSID,
      'wifi_rssi': instance.wifiRSSI,
      'is_online': instance.isOnline,
      'last_seen': instance.lastSeen.toIso8601String(),
      'response_time': instance.responseTime,
      'endpoints': instance.availableEndpoints,
      'capabilities': instance.capabilities,
      'additional_info': instance.additionalInfo,
    };

LanScanResult _$LanScanResultFromJson(Map<String, dynamic> json) =>
    LanScanResult(
      subnet: json['subnet'] as String,
      scanDurationMs: (json['scan_duration'] as num).toInt(),
      devicesFound: (json['devices_found'] as num).toInt(),
      esp32Devices: (json['esp32_devices'] as List<dynamic>)
          .map((e) => ESP32Device.fromJson(e as Map<String, dynamic>))
          .toList(),
      otherDevices: (json['other_devices'] as List<dynamic>)
          .map((e) => e as Map<String, dynamic>)
          .toList(),
      scanTimestamp: DateTime.parse(json['scan_timestamp'] as String),
      scanRange: json['scan_range'] as String,
    );

Map<String, dynamic> _$LanScanResultToJson(LanScanResult instance) =>
    <String, dynamic>{
      'subnet': instance.subnet,
      'scan_duration': instance.scanDurationMs,
      'devices_found': instance.devicesFound,
      'esp32_devices': instance.esp32Devices,
      'other_devices': instance.otherDevices,
      'scan_timestamp': instance.scanTimestamp.toIso8601String(),
      'scan_range': instance.scanRange,
    };
