import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'area_model.dart';

part 'home_model.g.dart';

@JsonSerializable()
class Home extends Equatable {
  @Json<PERSON>ey(name: 'home_id')
  final int homeId;
 
  final String name;
  final String address;
 
  @JsonKey(name: 'created_at')
  final String createdAt;

  @JsonKey(name: 'updated_at')
  final String updatedAt;
 
  final List<Area>? areas;
  
  const Home({
    required this.homeId,
    required this.name,
    required this.address,
    required this.createdAt,
    required this.updatedAt,
    this.areas,
  });

  // Getter for compatibility
  int get id => homeId;

  factory Home.fromJson(Map<String, dynamic> json) => _$HomeFromJson(json);
  Map<String, dynamic> toJson() => _$HomeToJson(this);

  @override
  List<Object?> get props => [homeId, name, address, createdAt, updatedAt, areas];
}



@JsonSerializable()
class HomeRequest extends Equatable {
  final String name;
  final String address;
  
  const HomeRequest({
    required this.name,
    required this.address,
  });
  
  factory HomeRequest.fromJson(Map<String, dynamic> json) => _$HomeRequestFromJson(json);
  Map<String, dynamic> toJson() => _$HomeRequestToJson(this);
  
  @override
  List<Object> get props => [name, address];
}

@JsonSerializable()
class HomeUserRequest extends Equatable {
  @JsonKey(name: 'email')
  final String userEmail;

  const HomeUserRequest({
    required this.userEmail,
  });

  // Getter cho role - mặc định là MEMBER
  String get role => 'MEMBER';

  factory HomeUserRequest.fromJson(Map<String, dynamic> json) => _$HomeUserRequestFromJson(json);
  Map<String, dynamic> toJson() => {
    'email': userEmail,
    'role': role,
  };

  @override
  List<Object> get props => [userEmail];
}

@JsonSerializable()
class HomeUser extends Equatable {
  @JsonKey(name: 'user_id')
  final int userId;

  final String email;

  @JsonKey(name: 'full_name')
  final String fullName;

  @JsonKey(name: 'phone_number')
  final String? phoneNumber;

  @JsonKey(name: 'avatar_url')
  final String? avatarUrl;

  @JsonKey(name: 'is_verified')
  final bool? isVerified;

  @JsonKey(name: 'created_at')
  final String? createdAt;

  @JsonKey(name: 'updated_at')
  final String? updatedAt;

  @JsonKey(name: 'last_login')
  final String? lastLogin;

  // Thêm role field để nhận từ API
  final String? role;

  @JsonKey(name: 'role_name')
  final String? roleName;

  const HomeUser({
    required this.userId,
    required this.email,
    required this.fullName,
    this.phoneNumber,
    this.avatarUrl,
    this.isVerified,
    this.createdAt,
    this.updatedAt,
    this.lastLogin,
    this.role,
    this.roleName,
  });

  // Getter cho role với fallback
  String get userRole => role ?? roleName ?? 'MEMBER';

  factory HomeUser.fromJson(Map<String, dynamic> json) => _$HomeUserFromJson(json);
  Map<String, dynamic> toJson() => _$HomeUserToJson(this);

  @override
  List<Object?> get props => [userId, email, fullName, phoneNumber, avatarUrl, isVerified, createdAt, updatedAt, lastLogin, role, roleName];
}

class PromoteUserRequest extends Equatable {
  final int userId;
  final int roleId;

  const PromoteUserRequest({
    required this.userId,
    required this.roleId,
  });

  Map<String, dynamic> toJson() {
    return {
      'user_id': userId,
      'role_id': roleId,
    };
  }

  @override
  List<Object> get props => [userId, roleId];
}

class TransferOwnershipRequest extends Equatable {
  final int newOwnerId;

  const TransferOwnershipRequest({
    required this.newOwnerId,
  });

  Map<String, dynamic> toJson() {
    return {
      'new_owner_id': newOwnerId,
    };
  }

  @override
  List<Object> get props => [newOwnerId];
}

class HomeDetail extends Equatable {
  @JsonKey(name: 'home_id')
  final int homeId;

  final String name;
  final String address;

  @JsonKey(name: 'created_at')
  final String createdAt;

  @JsonKey(name: 'updated_at')
  final String updatedAt;

  final List<Area> areas;
  final HomeStatistics statistics;

  @JsonKey(name: 'user_role')
  final int userRole;

  @JsonKey(name: 'user_role_name')
  final String? userRoleName;

  const HomeDetail({
    required this.homeId,
    required this.name,
    required this.address,
    required this.createdAt,
    required this.updatedAt,
    required this.areas,
    required this.statistics,
    required this.userRole,
    this.userRoleName,
  });

  // Getter for compatibility
  int get id => homeId;

  factory HomeDetail.fromJson(Map<String, dynamic> json) {
    try {
      print('HomeDetail.fromJson - Raw JSON:');
      print('  home_id: ${json['home_id']} (${json['home_id']?.runtimeType})');
      print('  name: ${json['name']} (${json['name']?.runtimeType})');
      print('  areas: ${json['areas']?.runtimeType} - length: ${json['areas'] is List ? (json['areas'] as List).length : 'N/A'}');
      print('  statistics: ${json['statistics']?.runtimeType}');
      print('  user_role: ${json['user_role']} (${json['user_role']?.runtimeType})');
      print('  user_role_name: ${json['user_role_name']} (${json['user_role_name']?.runtimeType})');

      // Parse areas safely
      List<Area> areasList = [];
      if (json['areas'] != null) {
        if (json['areas'] is List) {
          final areasData = json['areas'] as List<dynamic>;
          areasList = areasData
              .where((area) => area != null)
              .map((area) {
                try {
                  return Area.fromJson(area as Map<String, dynamic>);
                } catch (e) {
                  print('❌ Error parsing area: $e');
                  print('Area data: $area');
                  return null;
                }
              })
              .where((area) => area != null)
              .cast<Area>()
              .toList();
        } else {
          print('⚠️ Warning: areas field is not a List, got: ${json['areas'].runtimeType}');
        }
      }

      // Parse statistics safely
      HomeStatistics? statistics;
      if (json['statistics'] != null && json['statistics'] is Map<String, dynamic>) {
        try {
          statistics = HomeStatistics.fromJson(json['statistics'] as Map<String, dynamic>);
        } catch (e) {
          print('Error parsing statistics: $e');
          print('Statistics data: ${json['statistics']}');
          statistics = const HomeStatistics(
            totalAreas: 0,
            totalDevices: 0,
            onlineDevices: 0,
            offlineDevices: 0,
            totalUsers: 0,
          );
        }
      } else {
        print('Warning: statistics field is missing or not a Map');
        statistics = const HomeStatistics(
          totalAreas: 0,
          totalDevices: 0,
          onlineDevices: 0,
          offlineDevices: 0,
          totalUsers: 0,
        );
      }

      return HomeDetail(
        homeId: json['home_id'] as int,
        name: json['name'] as String,
        address: json['address'] as String,
        createdAt: json['created_at'] as String,
        updatedAt: json['updated_at'] as String,
        areas: areasList,
        statistics: statistics,
        userRole: json['user_role'] as int,
        userRoleName: (json['user_role_name'] as String?) ?? 'UNKNOWN',
      );
    } catch (e, stackTrace) {
      print('Error in HomeDetail.fromJson: $e');
      print('Stack trace: $stackTrace');
      print('JSON data: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'home_id': homeId,
      'name': name,
      'address': address,
      'created_at': createdAt,
      'updated_at': updatedAt,
      'areas': areas.map((area) => area.toJson()).toList(),
      'statistics': statistics.toJson(),
      'user_role': userRole,
      'user_role_name': userRoleName,
    };
  }

  @override
  List<Object?> get props => [homeId, name, address, createdAt, updatedAt, areas, statistics, userRole, userRoleName];
}

class HomeStatistics extends Equatable {
  @JsonKey(name: 'total_areas')
  final int totalAreas;

  @JsonKey(name: 'total_devices')
  final int totalDevices;

  @JsonKey(name: 'online_devices')
  final int onlineDevices;

  @JsonKey(name: 'offline_devices')
  final int offlineDevices;

  @JsonKey(name: 'total_users')
  final int totalUsers;

  @JsonKey(name: 'devices_by_type')
  final List<DeviceTypeStatistic>? devicesByType;

  @JsonKey(name: 'devices_by_area')
  final List<DeviceAreaStatistic>? devicesByArea;

  @JsonKey(name: 'recent_activities')
  final List<dynamic>? recentActivities;

  const HomeStatistics({
    required this.totalAreas,
    required this.totalDevices,
    required this.onlineDevices,
    required this.offlineDevices,
    required this.totalUsers,
    this.devicesByType,
    this.devicesByArea,
    this.recentActivities,
  });

  factory HomeStatistics.fromJson(Map<String, dynamic> json) {
    try {
      print('HomeStatistics.fromJson - Raw JSON: $json');

      return HomeStatistics(
        totalAreas: json['total_areas'] as int,
        totalDevices: json['total_devices'] as int,
        onlineDevices: json['online_devices'] as int,
        offlineDevices: json['offline_devices'] as int,
        totalUsers: json['total_users'] as int,
        devicesByType: json['devices_by_type'] != null
            ? (json['devices_by_type'] as List<dynamic>)
                .where((item) => item != null)
                .map((item) {
                  try {
                    return DeviceTypeStatistic.fromJson(item as Map<String, dynamic>);
                  } catch (e) {
                    print('Error parsing DeviceTypeStatistic: $e');
                    print('Item: $item');
                    return null;
                  }
                })
                .where((item) => item != null)
                .cast<DeviceTypeStatistic>()
                .toList()
            : null,
        devicesByArea: json['devices_by_area'] != null
            ? (json['devices_by_area'] as List<dynamic>)
                .where((item) => item != null)
                .map((item) {
                  try {
                    return DeviceAreaStatistic.fromJson(item as Map<String, dynamic>);
                  } catch (e) {
                    print('Error parsing DeviceAreaStatistic: $e');
                    print('Item: $item');
                    return null;
                  }
                })
                .where((item) => item != null)
                .cast<DeviceAreaStatistic>()
                .toList()
            : null,
        recentActivities: json['recent_activities'] != null
            ? (json['recent_activities'] as List<dynamic>?)
            : null,
      );
    } catch (e, stackTrace) {
      print('Error in HomeStatistics.fromJson: $e');
      print('Stack trace: $stackTrace');
      print('JSON: $json');
      rethrow;
    }
  }

  Map<String, dynamic> toJson() {
    return {
      'total_areas': totalAreas,
      'total_devices': totalDevices,
      'online_devices': onlineDevices,
      'offline_devices': offlineDevices,
      'total_users': totalUsers,
      'devices_by_type': devicesByType?.map((item) => item.toJson()).toList(),
      'devices_by_area': devicesByArea?.map((item) => item.toJson()).toList(),
      'recent_activities': recentActivities,
    };
  }

  @override
  List<Object?> get props => [
    totalAreas, totalDevices, onlineDevices, offlineDevices, totalUsers,
    devicesByType, devicesByArea, recentActivities
  ];
}

class DeviceTypeStatistic extends Equatable {
  @JsonKey(name: 'device_type_id')
  final int deviceTypeId;

  @JsonKey(name: 'device_type_name')
  final String deviceTypeName;

  @JsonKey(name: 'count')
  final int count;

  @JsonKey(name: 'online_count')
  final int onlineCount;

  const DeviceTypeStatistic({
    required this.deviceTypeId,
    required this.deviceTypeName,
    required this.count,
    required this.onlineCount,
  });

  factory DeviceTypeStatistic.fromJson(Map<String, dynamic> json) {
    print('DeviceTypeStatistic.fromJson - Raw JSON: $json');
    return DeviceTypeStatistic(
      deviceTypeId: json['device_type_id'] as int,
      deviceTypeName: json['device_type_name'] as String,
      count: json['count'] as int,
      onlineCount: json['online_count'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'device_type_id': deviceTypeId,
      'device_type_name': deviceTypeName,
      'count': count,
      'online_count': onlineCount,
    };
  }

  @override
  List<Object> get props => [deviceTypeId, deviceTypeName, count, onlineCount];

  // Getter for compatibility
  String get deviceType => deviceTypeName;
  int get totalCount => count;
}

class DeviceAreaStatistic extends Equatable {
  @JsonKey(name: 'area_id')
  final int areaId;

  @JsonKey(name: 'area_name')
  final String areaName;

  @JsonKey(name: 'total_count')
  final int totalCount;

  @JsonKey(name: 'online_count')
  final int onlineCount;

  const DeviceAreaStatistic({
    required this.areaId,
    required this.areaName,
    required this.totalCount,
    required this.onlineCount,
  });

  factory DeviceAreaStatistic.fromJson(Map<String, dynamic> json) {
    return DeviceAreaStatistic(
      areaId: json['area_id'] as int,
      areaName: json['area_name'] as String,
      totalCount: json['total_count'] as int,
      onlineCount: json['online_count'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'area_id': areaId,
      'area_name': areaName,
      'total_count': totalCount,
      'online_count': onlineCount,
    };
  }

  @override
  List<Object> get props => [areaId, areaName, totalCount, onlineCount];
}