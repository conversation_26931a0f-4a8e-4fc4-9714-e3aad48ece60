/// Models for monitoring and alert system
class MonitoringAlert {
  final int? alertId;
  final int homeId;
  final int? deviceId;
  final String title;
  final String message;
  final String alertType; // threshold, offline, anomaly, custom
  final String severity; // low, medium, high, critical
  final DateTime timestamp;
  final bool isRead;
  final bool isResolved;
  final String? deviceName;
  final String? areaName;
  final Map<String, dynamic>? metadata;

  const MonitoringAlert({
    this.alertId,
    required this.homeId,
    this.deviceId,
    required this.title,
    required this.message,
    required this.alertType,
    required this.severity,
    required this.timestamp,
    this.isRead = false,
    this.isResolved = false,
    this.deviceName,
    this.areaName,
    this.metadata,
  });

  factory MonitoringAlert.fromJson(Map<String, dynamic> json) {
    return MonitoringAlert(
      alertId: json['alert_id'],
      homeId: json['home_id'] ?? 0,
      deviceId: json['device_id'],
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      alertType: json['alert_type'] ?? 'custom',
      severity: json['severity'] ?? 'medium',
      timestamp: DateTime.tryParse(json['timestamp'] ?? '') ?? DateTime.now(),
      isRead: json['is_read'] ?? false,
      isResolved: json['is_resolved'] ?? false,
      deviceName: json['device_name'],
      areaName: json['area_name'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (alertId != null) 'alert_id': alertId,
      'home_id': homeId,
      if (deviceId != null) 'device_id': deviceId,
      'title': title,
      'message': message,
      'alert_type': alertType,
      'severity': severity,
      'timestamp': timestamp.toIso8601String(),
      'is_read': isRead,
      'is_resolved': isResolved,
      if (deviceName != null) 'device_name': deviceName,
      if (areaName != null) 'area_name': areaName,
      if (metadata != null) 'metadata': metadata,
    };
  }

  MonitoringAlert copyWith({
    int? alertId,
    int? homeId,
    int? deviceId,
    String? title,
    String? message,
    String? alertType,
    String? severity,
    DateTime? timestamp,
    bool? isRead,
    bool? isResolved,
    String? deviceName,
    String? areaName,
    Map<String, dynamic>? metadata,
  }) {
    return MonitoringAlert(
      alertId: alertId ?? this.alertId,
      homeId: homeId ?? this.homeId,
      deviceId: deviceId ?? this.deviceId,
      title: title ?? this.title,
      message: message ?? this.message,
      alertType: alertType ?? this.alertType,
      severity: severity ?? this.severity,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      isResolved: isResolved ?? this.isResolved,
      deviceName: deviceName ?? this.deviceName,
      areaName: areaName ?? this.areaName,
      metadata: metadata ?? this.metadata,
    );
  }
}

/// Model for alert rules configuration
class AlertRule {
  final int? ruleId;
  final int homeId;
  final int? deviceId;
  final String name;
  final String description;
  final String ruleType; // threshold, offline, anomaly, schedule
  final String condition; // JSON string with rule conditions
  final String severity;
  final bool isActive;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? deviceName;
  final String? areaName;

  const AlertRule({
    this.ruleId,
    required this.homeId,
    this.deviceId,
    required this.name,
    required this.description,
    required this.ruleType,
    required this.condition,
    required this.severity,
    this.isActive = true,
    this.createdAt,
    this.updatedAt,
    this.deviceName,
    this.areaName,
  });

  factory AlertRule.fromJson(Map<String, dynamic> json) {
    return AlertRule(
      ruleId: json['rule_id'],
      homeId: json['home_id'] ?? 0,
      deviceId: json['device_id'],
      name: json['name'] ?? '',
      description: json['description'] ?? '',
      ruleType: json['rule_type'] ?? 'threshold',
      condition: json['condition'] ?? '{}',
      severity: json['severity'] ?? 'medium',
      isActive: json['is_active'] ?? true,
      createdAt: DateTime.tryParse(json['created_at'] ?? ''),
      updatedAt: DateTime.tryParse(json['updated_at'] ?? ''),
      deviceName: json['device_name'],
      areaName: json['area_name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (ruleId != null) 'rule_id': ruleId,
      'home_id': homeId,
      if (deviceId != null) 'device_id': deviceId,
      'name': name,
      'description': description,
      'rule_type': ruleType,
      'condition': condition,
      'severity': severity,
      'is_active': isActive,
      if (createdAt != null) 'created_at': createdAt!.toIso8601String(),
      if (updatedAt != null) 'updated_at': updatedAt!.toIso8601String(),
      if (deviceName != null) 'device_name': deviceName,
      if (areaName != null) 'area_name': areaName,
    };
  }

  AlertRule copyWith({
    int? ruleId,
    int? homeId,
    int? deviceId,
    String? name,
    String? description,
    String? ruleType,
    String? condition,
    String? severity,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? deviceName,
    String? areaName,
  }) {
    return AlertRule(
      ruleId: ruleId ?? this.ruleId,
      homeId: homeId ?? this.homeId,
      deviceId: deviceId ?? this.deviceId,
      name: name ?? this.name,
      description: description ?? this.description,
      ruleType: ruleType ?? this.ruleType,
      condition: condition ?? this.condition,
      severity: severity ?? this.severity,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deviceName: deviceName ?? this.deviceName,
      areaName: areaName ?? this.areaName,
    );
  }
}

/// Model for device activity logs
class DeviceActivityLog {
  final int? logId;
  final int deviceId;
  final String deviceName;
  final String action; // turn_on, turn_off, status_change, error, etc.
  final String? previousValue;
  final String? newValue;
  final DateTime timestamp;
  final String? userId;
  final String? userName;
  final String? source; // manual, automation, schedule, etc.
  final Map<String, dynamic>? metadata;

  const DeviceActivityLog({
    this.logId,
    required this.deviceId,
    required this.deviceName,
    required this.action,
    this.previousValue,
    this.newValue,
    required this.timestamp,
    this.userId,
    this.userName,
    this.source,
    this.metadata,
  });

  factory DeviceActivityLog.fromJson(Map<String, dynamic> json) {
    return DeviceActivityLog(
      logId: json['log_id'],
      deviceId: json['device_id'] ?? 0,
      deviceName: json['device_name'] ?? '',
      action: json['action'] ?? '',
      previousValue: json['previous_value'],
      newValue: json['new_value'],
      timestamp: DateTime.tryParse(json['timestamp'] ?? '') ?? DateTime.now(),
      userId: json['user_id'],
      userName: json['user_name'],
      source: json['source'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (logId != null) 'log_id': logId,
      'device_id': deviceId,
      'device_name': deviceName,
      'action': action,
      if (previousValue != null) 'previous_value': previousValue,
      if (newValue != null) 'new_value': newValue,
      'timestamp': timestamp.toIso8601String(),
      if (userId != null) 'user_id': userId,
      if (userName != null) 'user_name': userName,
      if (source != null) 'source': source,
      if (metadata != null) 'metadata': metadata,
    };
  }
}

/// Model for monitoring statistics
class MonitoringStats {
  final int totalDevices;
  final int onlineDevices;
  final int offlineDevices;
  final int activeAlerts;
  final int unresolvedAlerts;
  final int todayActivities;
  final Map<String, int> alertsBySeverity;
  final Map<String, int> devicesByType;
  final List<DeviceStatus> recentDeviceStatus;

  const MonitoringStats({
    required this.totalDevices,
    required this.onlineDevices,
    required this.offlineDevices,
    required this.activeAlerts,
    required this.unresolvedAlerts,
    required this.todayActivities,
    required this.alertsBySeverity,
    required this.devicesByType,
    required this.recentDeviceStatus,
  });

  factory MonitoringStats.fromJson(Map<String, dynamic> json) {
    return MonitoringStats(
      totalDevices: json['total_devices'] ?? 0,
      onlineDevices: json['online_devices'] ?? 0,
      offlineDevices: json['offline_devices'] ?? 0,
      activeAlerts: json['active_alerts'] ?? 0,
      unresolvedAlerts: json['unresolved_alerts'] ?? 0,
      todayActivities: json['today_activities'] ?? 0,
      alertsBySeverity: Map<String, int>.from(json['alerts_by_severity'] ?? {}),
      devicesByType: Map<String, int>.from(json['devices_by_type'] ?? {}),
      recentDeviceStatus: (json['recent_device_status'] as List<dynamic>?)
          ?.map((item) => DeviceStatus.fromJson(item))
          .toList() ?? [],
    );
  }
}

/// Model for device status in monitoring
class DeviceStatus {
  final int deviceId;
  final String deviceName;
  final String status; // online, offline, error
  final DateTime lastSeen;
  final String? areaName;
  final Map<String, dynamic>? properties;

  const DeviceStatus({
    required this.deviceId,
    required this.deviceName,
    required this.status,
    required this.lastSeen,
    this.areaName,
    this.properties,
  });

  factory DeviceStatus.fromJson(Map<String, dynamic> json) {
    return DeviceStatus(
      deviceId: json['device_id'] ?? 0,
      deviceName: json['device_name'] ?? '',
      status: json['status'] ?? 'offline',
      lastSeen: DateTime.tryParse(json['last_seen'] ?? '') ?? DateTime.now(),
      areaName: json['area_name'],
      properties: json['properties'],
    );
  }
}

/// Model for monitoring events
class MonitoringEvent {
  final int? eventId;
  final int homeId;
  final String eventType; // device_status, alert_triggered, rule_activated, etc.
  final String title;
  final String description;
  final DateTime timestamp;
  final Map<String, dynamic>? data;

  const MonitoringEvent({
    this.eventId,
    required this.homeId,
    required this.eventType,
    required this.title,
    required this.description,
    required this.timestamp,
    this.data,
  });

  factory MonitoringEvent.fromJson(Map<String, dynamic> json) {
    return MonitoringEvent(
      eventId: json['event_id'],
      homeId: json['home_id'] ?? 0,
      eventType: json['event_type'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      timestamp: DateTime.tryParse(json['timestamp'] ?? '') ?? DateTime.now(),
      data: json['data'],
    );
  }
}
