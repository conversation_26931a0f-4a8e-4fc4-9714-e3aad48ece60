import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';
import 'area_model.dart';
import 'device_model.dart';

part 'permission_model.g.dart';

@JsonSerializable()
class Permission extends Equatable {
  @Json<PERSON>ey(name: 'permission_id')
  final int permissionId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'user_id')
  final int userId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'area_id')
  final int? areaId;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'device_id')
  final int? deviceId;

  @Json<PERSON>ey(name: 'can_view')
  final bool canView;

  @Json<PERSON>ey(name: 'can_control')
  final bool canControl;

  @Json<PERSON>ey(name: 'can_configure')
  final bool canConfigure;

  // Nested objects from backend
  final Area? area;
  final Device? device;

  const Permission({
    required this.permissionId,
    required this.userId,
    this.areaId,
    this.deviceId,
    required this.canView,
    required this.canControl,
    required this.canConfigure,
    this.area,
    this.device,
  });

  factory Permission.fromJson(Map<String, dynamic> json) {
    try {
      print('🔍 Parsing Permission JSON: $json');

      // Parse basic fields
      final permissionId = json['permission_id'] as int? ?? 0;
      final userId = json['user_id'] as int? ?? 0;
      final areaId = json['area_id'] as int?;
      final deviceId = json['device_id'] as int?;
      final canView = json['can_view'] as bool? ?? true;
      final canControl = json['can_control'] as bool? ?? false;
      final canConfigure = json['can_configure'] as bool? ?? false;

      // Parse nested objects safely
      Area? area;
      Device? device;

      try {
        if (json['area'] != null && json['area'] is Map<String, dynamic>) {
          area = Area.fromJson(json['area'] as Map<String, dynamic>);
        }
      } catch (e) {
        print('🔍 Error parsing nested area: $e');
      }

      try {
        if (json['device'] != null && json['device'] is Map<String, dynamic>) {
          device = Device.fromJson(json['device'] as Map<String, dynamic>);
        }
      } catch (e) {
        print('🔍 Error parsing nested device: $e');
      }

      return Permission(
        permissionId: permissionId,
        userId: userId,
        areaId: areaId,
        deviceId: deviceId,
        canView: canView,
        canControl: canControl,
        canConfigure: canConfigure,
        area: area,
        device: device,
      );
    } catch (e) {
      print('Permission parsing error: $e');
      return Permission(
        permissionId: json['permission_id'] as int? ?? 0,
        userId: json['user_id'] as int? ?? 0,
        areaId: json['area_id'] as int?,
        deviceId: json['device_id'] as int?,
        canView: json['can_view'] as bool? ?? true,
        canControl: json['can_control'] as bool? ?? false,
        canConfigure: json['can_configure'] as bool? ?? false,
        area: null,
        device: null,
      );
    }
  }

  Map<String, dynamic> toJson() => _$PermissionToJson(this);

  // Getters for compatibility
  int get id => permissionId;

  String get resourceType {
    if (deviceId != null) return 'device';
    if (areaId != null) return 'area';
    return 'unknown';
  }

  int get resourceId {
    if (deviceId != null) return deviceId!;
    if (areaId != null) return areaId!;
    throw StateError('No valid resource ID found');
  }

  String get permissionType {
    if (canControl) return 'control';
    if (canView) return 'view';
    return 'none';
  }

  @override
  List<Object?> get props => [permissionId, userId, areaId, deviceId, canView, canControl, canConfigure, area, device];
}

@JsonSerializable()
class PermissionRequest extends Equatable {
  @JsonKey(name: 'user_id')
  final int userId;
  
  @JsonKey(name: 'area_id')
  final int? areaId;
  
  @JsonKey(name: 'device_id')
  final int? deviceId;
  
  @JsonKey(name: 'can_view')
  final bool canView;
  
  @JsonKey(name: 'can_control')
  final bool canControl;

  @JsonKey(name: 'can_configure')
  final bool canConfigure;

  const PermissionRequest({
    required this.userId,
    this.areaId,
    this.deviceId,
    required this.canView,
    required this.canControl,
    required this.canConfigure,
  });

  factory PermissionRequest.fromJson(Map<String, dynamic> json) => _$PermissionRequestFromJson(json);
  Map<String, dynamic> toJson() => _$PermissionRequestToJson(this);

  @override
  List<Object?> get props => [userId, areaId, deviceId, canView, canControl, canConfigure];
}

@JsonSerializable()
class Role extends Equatable {
  @JsonKey(name: 'role_id')
  final int roleId;
  
  final String name;

  const Role({
    required this.roleId,
    required this.name,
  });

  factory Role.fromJson(Map<String, dynamic> json) => _$RoleFromJson(json);
  Map<String, dynamic> toJson() => _$RoleToJson(this);

  @override
  List<Object> get props => [roleId, name];
}

@JsonSerializable()
class UserRole extends Equatable {
  @JsonKey(name: 'user_id')
  final int userId;
  
  @JsonKey(name: 'role_id')
  final int roleId;

  const UserRole({
    required this.userId,
    required this.roleId,
  });

  factory UserRole.fromJson(Map<String, dynamic> json) => _$UserRoleFromJson(json);
  Map<String, dynamic> toJson() => _$UserRoleToJson(this);

  @override
  List<Object> get props => [userId, roleId];
}
