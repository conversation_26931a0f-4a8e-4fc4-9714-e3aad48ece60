// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'permission_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Permission _$PermissionFromJson(Map<String, dynamic> json) => Permission(
      permissionId: (json['permission_id'] as num).toInt(),
      userId: (json['user_id'] as num).toInt(),
      areaId: (json['area_id'] as num?)?.toInt(),
      deviceId: (json['device_id'] as num?)?.toInt(),
      canView: json['can_view'] as bool,
      canControl: json['can_control'] as bool,
      canConfigure: json['can_configure'] as bool,
      area: json['area'] == null
          ? null
          : Area.fromJson(json['area'] as Map<String, dynamic>),
      device: json['device'] == null
          ? null
          : Device.fromJson(json['device'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PermissionToJson(Permission instance) =>
    <String, dynamic>{
      'permission_id': instance.permissionId,
      'user_id': instance.userId,
      'area_id': instance.areaId,
      'device_id': instance.deviceId,
      'can_view': instance.canView,
      'can_control': instance.canControl,
      'can_configure': instance.canConfigure,
      'area': instance.area,
      'device': instance.device,
    };

PermissionRequest _$PermissionRequestFromJson(Map<String, dynamic> json) =>
    PermissionRequest(
      userId: (json['user_id'] as num).toInt(),
      areaId: (json['area_id'] as num?)?.toInt(),
      deviceId: (json['device_id'] as num?)?.toInt(),
      canView: json['can_view'] as bool,
      canControl: json['can_control'] as bool,
      canConfigure: json['can_configure'] as bool,
    );

Map<String, dynamic> _$PermissionRequestToJson(PermissionRequest instance) =>
    <String, dynamic>{
      'user_id': instance.userId,
      'area_id': instance.areaId,
      'device_id': instance.deviceId,
      'can_view': instance.canView,
      'can_control': instance.canControl,
      'can_configure': instance.canConfigure,
    };

Role _$RoleFromJson(Map<String, dynamic> json) => Role(
      roleId: (json['role_id'] as num).toInt(),
      name: json['name'] as String,
    );

Map<String, dynamic> _$RoleToJson(Role instance) => <String, dynamic>{
      'role_id': instance.roleId,
      'name': instance.name,
    };

UserRole _$UserRoleFromJson(Map<String, dynamic> json) => UserRole(
      userId: (json['user_id'] as num).toInt(),
      roleId: (json['role_id'] as num).toInt(),
    );

Map<String, dynamic> _$UserRoleToJson(UserRole instance) => <String, dynamic>{
      'user_id': instance.userId,
      'role_id': instance.roleId,
    };
