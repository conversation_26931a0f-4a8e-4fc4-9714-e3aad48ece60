import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class User extends Equatable {
  @Json<PERSON>ey(name: 'user_id')
  final int userId;

  final String email;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'phone_number')
  final String? phoneNumber;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'full_name')
  final String? fullName;

  @<PERSON>sonKey(name: 'avatar_url')
  final String? avatarUrl;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'is_verified')
  final bool isVerified;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final String createdAt;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  final String updatedAt;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'last_login')
  final String? lastLogin;

  const User({
    required this.userId,
    required this.email,
    this.phoneNumber,
    this.fullName,
    this.avatarUrl,
    required this.isVerified,
    required this.createdAt,
    required this.updatedAt,
    this.lastLogin,
  });

  // Getter for backward compatibility
  int get id => userId;

  // Safe getters with fallbacks
  String get displayName => fullName ?? email.split('@').first;
  String get displayPhone => phoneNumber ?? 'Chưa cập nhật';
  String get displayAvatar => avatarUrl ?? '';

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  
  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    int? userId,
    String? email,
    String? phoneNumber,
    String? fullName,
    String? avatarUrl,
    bool? isVerified,
    String? createdAt,
    String? updatedAt,
    String? lastLogin,
  }) {
    return User(
      userId: userId ?? this.userId,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      fullName: fullName ?? this.fullName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      isVerified: isVerified ?? this.isVerified,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastLogin: lastLogin ?? this.lastLogin,
    );
  }

  @override
  List<Object?> get props => [
    userId, email, phoneNumber, fullName, avatarUrl,
    isVerified, createdAt, updatedAt, lastLogin
  ];
}