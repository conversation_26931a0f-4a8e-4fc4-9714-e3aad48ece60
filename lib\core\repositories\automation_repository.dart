import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/automation_models.dart';
import '../models/api_response.dart';
import '../config/api_config.dart';

class AutomationRepository {
  final String baseUrl = ApiConfig.baseUrl;
  
  // Helper method để tạo headers
  Map<String, String> _getHeaders(String token) {
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Create Schedule-based Rule
  Future<ApiResponse<AutomationRule>> createScheduleRule(AutomationRule rule, String token) async {
    try {
      print('DEBUG: Creating schedule rule: ${rule.name}');

      final requestBody = rule.toJson();
      print('DEBUG: Request body: ${json.encode(requestBody)}');

      final response = await http.post(
        Uri.parse('$baseUrl/api/automation/rules/schedule'),
        headers: _getHeaders(token),
        body: json.encode(requestBody),
      );

      print('DEBUG: Create schedule rule response: ${response.statusCode}');
      print('DEBUG: Response body: ${response.body}');
      
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = json.decode(response.body);
        if (responseData['status'] == true) {
          final ruleData = responseData['data'];
          return ApiResponse<AutomationRule>.success(
            message: responseData['message'] ?? 'Tạo automation rule thành công',
            data: AutomationRule.fromJson(ruleData),
          );
        } else {
          return ApiResponse<AutomationRule>.error(
            message: responseData['message'] ?? 'Tạo automation rule thất bại',
          );
        }
      } else {
        final responseData = json.decode(response.body);
        return ApiResponse<AutomationRule>.error(
          message: responseData['message'] ?? 'Lỗi khi tạo automation rule',
        );
      }
    } catch (e) {
      print('DEBUG: Error creating schedule rule: $e');
      return ApiResponse<AutomationRule>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  // Create Condition-based Rule
  Future<ApiResponse<AutomationRule>> createConditionRule(AutomationRule rule, String token) async {
    try {
      print('🔍 [DEBUG] Creating condition rule: ${rule.name}');

      final requestBody = rule.toJson();
      print('🔍 [DEBUG] Request body: ${json.encode(requestBody)}');
      print('🔍 [DEBUG] Conditions: ${requestBody['conditions']}');

      final response = await http.post(
        Uri.parse('$baseUrl/api/automation/rules/condition'),
        headers: _getHeaders(token),
        body: json.encode(requestBody),
      );

      print('🔍 [DEBUG] Create condition rule response: ${response.statusCode}');
      print('🔍 [DEBUG] Response body: ${response.body}');
      
      if (response.statusCode == 200 || response.statusCode == 201) {
        final responseData = json.decode(response.body);
        if (responseData['status'] == true) {
          final ruleData = responseData['data'];
          return ApiResponse<AutomationRule>.success(
            message: responseData['message'] ?? 'Tạo automation rule thành công',
            data: AutomationRule.fromJson(ruleData),
          );
        } else {
          return ApiResponse<AutomationRule>.error(
            message: responseData['message'] ?? 'Tạo automation rule thất bại',
          );
        }
      } else {
        final responseData = json.decode(response.body);
        return ApiResponse<AutomationRule>.error(
          message: responseData['message'] ?? 'Lỗi khi tạo automation rule',
        );
      }
    } catch (e) {
      print('DEBUG: Error creating condition rule: $e');
      return ApiResponse<AutomationRule>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  // Get Automation Rules List
  Future<ApiResponse<List<AutomationRule>>> getAutomationRules(int homeId, String token) async {
    try {
      print('DEBUG: Getting automation rules for home: $homeId');
      
      final response = await http.get(
        Uri.parse('$baseUrl/api/homes/$homeId/automation/rules'),
        headers: _getHeaders(token),
      );

      print('DEBUG: Get automation rules response: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['status'] == true) {
          // Safely handle null data
          final rulesData = responseData['data'] as List<dynamic>? ?? [];
          final rules = rulesData.map((rule) => AutomationRule.fromJson(rule)).toList();

          return ApiResponse<List<AutomationRule>>.success(
            message: responseData['message'] ?? 'Lấy danh sách automation rules thành công',
            data: rules,
          );
        } else {
          return ApiResponse<List<AutomationRule>>.error(
            message: responseData['message'] ?? 'Lấy danh sách automation rules thất bại',
          );
        }
      } else {
        final responseData = json.decode(response.body);
        return ApiResponse<List<AutomationRule>>.error(
          message: responseData['message'] ?? 'Lỗi khi lấy danh sách automation rules',
        );
      }
    } catch (e) {
      print('DEBUG: Error getting automation rules: $e');
      return ApiResponse<List<AutomationRule>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  // Update Automation Rule
  Future<ApiResponse<AutomationRule>> updateAutomationRule(int homeId, int ruleId, AutomationRule rule, String token) async {
    try {
      print('DEBUG: Updating automation rule: $ruleId');
      
      final response = await http.put(
        Uri.parse('$baseUrl/api/homes/$homeId/automation/rules/$ruleId'),
        headers: _getHeaders(token),
        body: json.encode(rule.toJson()),
      );

      print('DEBUG: Update automation rule response: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['status'] == true) {
          final ruleData = responseData['data'];
          return ApiResponse<AutomationRule>.success(
            message: responseData['message'] ?? 'Cập nhật automation rule thành công',
            data: AutomationRule.fromJson(ruleData),
          );
        } else {
          return ApiResponse<AutomationRule>.error(
            message: responseData['message'] ?? 'Cập nhật automation rule thất bại',
          );
        }
      } else {
        final responseData = json.decode(response.body);
        return ApiResponse<AutomationRule>.error(
          message: responseData['message'] ?? 'Lỗi khi cập nhật automation rule',
        );
      }
    } catch (e) {
      print('DEBUG: Error updating automation rule: $e');
      return ApiResponse<AutomationRule>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  // Toggle Rule Status (Active/Inactive)
  Future<ApiResponse<bool>> toggleRuleStatus(int homeId, int ruleId, bool isActive, String token) async {
    try {
      print('DEBUG: Toggling rule status: $ruleId to $isActive');
      
      final response = await http.put(
        Uri.parse('$baseUrl/api/homes/$homeId/automation/rules/$ruleId/status'),
        headers: _getHeaders(token),
        body: json.encode({'is_active': isActive}),
      );

      print('DEBUG: Toggle rule status response: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['status'] == true) {
          return ApiResponse<bool>.success(
            message: responseData['message'] ?? 'Cập nhật trạng thái rule thành công',
            data: isActive,
          );
        } else {
          return ApiResponse<bool>.error(
            message: responseData['message'] ?? 'Cập nhật trạng thái rule thất bại',
          );
        }
      } else {
        final responseData = json.decode(response.body);
        return ApiResponse<bool>.error(
          message: responseData['message'] ?? 'Lỗi khi cập nhật trạng thái rule',
        );
      }
    } catch (e) {
      print('DEBUG: Error toggling rule status: $e');
      return ApiResponse<bool>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  // Execute Rule Manually
  Future<ApiResponse<AutomationExecution>> executeRule(int homeId, int ruleId, String token) async {
    try {
      print('DEBUG: Executing rule manually: $ruleId');
      
      final response = await http.post(
        Uri.parse('$baseUrl/api/homes/$homeId/automation/rules/$ruleId/execute'),
        headers: _getHeaders(token),
        body: json.encode({}),
      );

      print('DEBUG: Execute rule response: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['status'] == true) {
          final executionData = responseData['data'];
          return ApiResponse<AutomationExecution>.success(
            message: responseData['message'] ?? 'Thực thi automation rule thành công',
            data: AutomationExecution.fromJson(executionData),
          );
        } else {
          return ApiResponse<AutomationExecution>.error(
            message: responseData['message'] ?? 'Thực thi automation rule thất bại',
          );
        }
      } else {
        final responseData = json.decode(response.body);
        return ApiResponse<AutomationExecution>.error(
          message: responseData['message'] ?? 'Lỗi khi thực thi automation rule',
        );
      }
    } catch (e) {
      print('DEBUG: Error executing rule: $e');
      return ApiResponse<AutomationExecution>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  // Get Execution History
  Future<ApiResponse<List<AutomationExecution>>> getExecutionHistory(int homeId, String token, {int limit = 20}) async {
    try {
      print('DEBUG: Getting execution history for home: $homeId');
      
      final response = await http.get(
        Uri.parse('$baseUrl/api/homes/$homeId/automation/executions?limit=$limit'),
        headers: _getHeaders(token),
      );

      print('DEBUG: Get execution history response: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['status'] == true) {
          // Safely handle null data
          final executionsData = responseData['data'] as List<dynamic>? ?? [];
          final executions = executionsData.map((execution) => AutomationExecution.fromJson(execution)).toList();
          
          return ApiResponse<List<AutomationExecution>>.success(
            message: responseData['message'] ?? 'Lấy lịch sử thực thi thành công',
            data: executions,
          );
        } else {
          return ApiResponse<List<AutomationExecution>>.error(
            message: responseData['message'] ?? 'Lấy lịch sử thực thi thất bại',
          );
        }
      } else {
        final responseData = json.decode(response.body);
        return ApiResponse<List<AutomationExecution>>.error(
          message: responseData['message'] ?? 'Lỗi khi lấy lịch sử thực thi',
        );
      }
    } catch (e) {
      print('DEBUG: Error getting execution history: $e');
      return ApiResponse<List<AutomationExecution>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  // Delete Automation Rule
  Future<ApiResponse<bool>> deleteAutomationRule(int homeId, int ruleId, String token) async {
    try {
      print('DEBUG: Deleting automation rule: $ruleId');
      
      final response = await http.delete(
        Uri.parse('$baseUrl/api/homes/$homeId/automation/rules/$ruleId'),
        headers: _getHeaders(token),
      );

      print('DEBUG: Delete automation rule response: ${response.statusCode}');
      
      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData['status'] == true) {
          return ApiResponse<bool>.success(
            message: responseData['message'] ?? 'Xóa automation rule thành công',
            data: true,
          );
        } else {
          return ApiResponse<bool>.error(
            message: responseData['message'] ?? 'Xóa automation rule thất bại',
          );
        }
      } else {
        final responseData = json.decode(response.body);
        return ApiResponse<bool>.error(
          message: responseData['message'] ?? 'Lỗi khi xóa automation rule',
        );
      }
    } catch (e) {
      print('DEBUG: Error deleting automation rule: $e');
      return ApiResponse<bool>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }
}
