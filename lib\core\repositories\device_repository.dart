import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/api_response.dart';
import '../models/device_model.dart';
import '../models/device_type_model.dart';
import '../config/api_config.dart';
import '../models/esp32_device_model.dart';
import '../services/lan_scanner_service.dart';
import '../utils/network_utils.dart';

class DeviceRepository {
  final String baseUrl = ApiConfig.baseUrl;

  /// Get available devices (chưa thuộc nhà nào)
  Future<ApiResponse<List<Device>>> getAvailableDevices(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/devices/available'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = json.decode(response.body);

      // Check for backend response format
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        final List<dynamic> devicesJson = data['data'] ?? [];
        final devices = devicesJson.map((json) => Device.fromJson(json)).toList();

        return ApiResponse<List<Device>>.success(
          data: devices,
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<List<Device>>.error(
          message: data['message'] ?? 'Lỗi khi tải danh sách thiết bị có sẵn',
        );
      }
    } catch (e) {
      return ApiResponse<List<Device>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Get all devices in system
  Future<ApiResponse<List<Device>>> getAllDevices(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/devices'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = json.decode(response.body);

      // Check for backend response format
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        final List<dynamic> devicesJson = data['data'] ?? [];
        final devices = devicesJson.map((json) => Device.fromJson(json)).toList();

        return ApiResponse<List<Device>>.success(
          data: devices,
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<List<Device>>.error(
          message: data['message'] ?? 'Lỗi khi tải danh sách thiết bị',
        );
      }
    } catch (e) {
      return ApiResponse<List<Device>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Get device detail
  Future<ApiResponse<Device>> getDevice(String token, int deviceId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/devices/$deviceId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = json.decode(response.body);

      // Check for backend response format
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        final device = Device.fromJson(data['data']);

        return ApiResponse<Device>.success(
          data: device,
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<Device>.error(
          message: data['message'] ?? 'Lỗi khi tải chi tiết thiết bị',
        );
      }
    } catch (e) {
      return ApiResponse<Device>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Register new device
  Future<ApiResponse<Device>> registerDevice(String token, Device device) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/devices'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(device.toJson()),
      );

      final data = json.decode(response.body);

      // Check for backend response format
      final status = data['status'] as bool? ?? false;

      if ((response.statusCode == 200 || response.statusCode == 201) && status) {
        final registeredDevice = Device.fromJson(data['data']);

        return ApiResponse<Device>.success(
          data: registeredDevice,
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<Device>.error(
          message: data['message'] ?? 'Lỗi khi đăng ký thiết bị',
        );
      }
    } catch (e) {
      return ApiResponse<Device>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Update device
  Future<ApiResponse<Device>> updateDevice(String token, int deviceId, Map<String, dynamic> updateData) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/api/devices/$deviceId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(updateData),
      );

      final data = json.decode(response.body);

      // Check for backend response format
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        final device = Device.fromJson(data['data']);

        return ApiResponse<Device>.success(
          data: device,
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<Device>.error(
          message: data['message'] ?? 'Lỗi khi cập nhật thiết bị',
        );
      }
    } catch (e) {
      return ApiResponse<Device>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Delete device
  Future<ApiResponse<void>> deleteDevice(String token, int deviceId) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/api/devices/$deviceId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = json.decode(response.body);

      // Check for backend response format
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        return ApiResponse<void>.success(
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<void>.error(
          message: data['message'] ?? 'Lỗi khi xóa thiết bị',
        );
      }
    } catch (e) {
      return ApiResponse<void>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Control single device
  Future<ApiResponse<Map<String, dynamic>>> controlDevice(
    String token,
    int homeId,
    int deviceId,
    String command,
    String value,
  ) async {
    final url = '$baseUrl/api/homes/$homeId/devices/$deviceId/control';
    final requestBody = {
      'command': command,
      'value': value,
    };

    print(' [DEBUG] controlDevice - URL: $url');
    print(' [DEBUG] controlDevice - homeId: $homeId, deviceId: $deviceId');
    print(' [DEBUG] controlDevice - Command: $command, Value: $value');
    print(' [DEBUG] controlDevice - Request Body: ${json.encode(requestBody)}');

    try {
      final response = await http.put(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(requestBody),
      );

      print(' [DEBUG] controlDevice - Status Code: ${response.statusCode}');
      print(' [DEBUG] controlDevice - Response Body: ${response.body}');

      final data = json.decode(response.body);

      // Check for backend response format
      final status = data['status'] as bool? ?? false;

      print(' [DEBUG] controlDevice - Parsed Status: $status');
      print(' [DEBUG] controlDevice - Parsed Data: ${data['data']}');

      if (response.statusCode == 200 && status) {
        print(' [DEBUG] controlDevice - Success');
        return ApiResponse<Map<String, dynamic>>.success(
          data: data['data'],
          message: data['message'] ?? 'Success',
        );
      } else {
        print(' [DEBUG] controlDevice - Failed: ${data['message']}');
        return ApiResponse<Map<String, dynamic>>.error(
          message: data['message'] ?? 'Lỗi khi điều khiển thiết bị',
        );
      }
    } catch (e) {
      print(' [DEBUG] controlDevice - Exception: $e');
      return ApiResponse<Map<String, dynamic>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Batch control multiple devices
  Future<ApiResponse<Map<String, dynamic>>> batchControlDevices(
    String token,
    int homeId,
    List<Map<String, dynamic>> deviceCommands,
    bool executeSequentially,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/homes/$homeId/devices/batch-control'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'device_commands': deviceCommands,
          'execute_sequentially': executeSequentially,
        }),
      );

      final data = json.decode(response.body);

      // Check for backend response format
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        return ApiResponse<Map<String, dynamic>>.success(
          data: data['data'],
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<Map<String, dynamic>>.error(
          message: data['message'] ?? 'Lỗi khi điều khiển nhiều thiết bị',
        );
      }
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Get real-time device status
  Future<ApiResponse<Map<String, dynamic>>> getDeviceStatus(
    String token,
    int homeId,
    int deviceId,
  ) async {
    final url = '$baseUrl/api/homes/$homeId/devices/$deviceId/status';
    print(' [DEBUG] getDeviceStatus - URL: $url');
    print(' [DEBUG] getDeviceStatus - homeId: $homeId, deviceId: $deviceId');

    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print(' [DEBUG] getDeviceStatus - Status Code: ${response.statusCode}');
      print(' [DEBUG] getDeviceStatus - Response Body: ${response.body}');

      final data = json.decode(response.body);
      final status = data['status'] as bool? ?? false;

      print(' [DEBUG] getDeviceStatus - Parsed Status: $status');
      print(' [DEBUG] getDeviceStatus - Parsed Data: ${data['data']}');

      if (response.statusCode == 200 && status) {
        print(' [DEBUG] getDeviceStatus - Success');
        return ApiResponse<Map<String, dynamic>>.success(
          data: data['data'],
          message: data['message'] ?? 'Success',
        );
      } else {
        print(' [DEBUG] getDeviceStatus - Failed: ${data['message']}');
        return ApiResponse<Map<String, dynamic>>.error(
          message: data['message'] ?? 'Lỗi khi lấy trạng thái thiết bị',
        );
      }
    } catch (e) {
      print(' [DEBUG] getDeviceStatus - Exception: $e');
      return ApiResponse<Map<String, dynamic>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Get all devices status in home
  Future<ApiResponse<List<Map<String, dynamic>>>> getAllDevicesStatus(
    String token,
    int homeId,
  ) async {
    final url = '$baseUrl/api/homes/$homeId/devices/status';
    print(' [DEBUG] getAllDevicesStatus - URL: $url');
    print(' [DEBUG] getAllDevicesStatus - homeId: $homeId');

    try {
      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print(' [DEBUG] getAllDevicesStatus - Status Code: ${response.statusCode}');
      print(' [DEBUG] getAllDevicesStatus - Response Body: ${response.body}');

      final data = json.decode(response.body);
      final status = data['status'] as bool? ?? false;

      print(' [DEBUG] getAllDevicesStatus - Parsed Status: $status');
      print(' [DEBUG] getAllDevicesStatus - Data Type: ${data['data'].runtimeType}');

      if (response.statusCode == 200 && status) {
        final List<dynamic> statusData = data['data'] ?? [];
        final statusList = statusData.cast<Map<String, dynamic>>();

        print(' [DEBUG] getAllDevicesStatus - Success, devices count: ${statusList.length}');
        return ApiResponse<List<Map<String, dynamic>>>.success(
          data: statusList,
          message: data['message'] ?? 'Success',
        );
      } else {
        print(' [DEBUG] getAllDevicesStatus - Failed: ${data['message']}');
        return ApiResponse<List<Map<String, dynamic>>>.error(
          message: data['message'] ?? 'Lỗi khi lấy trạng thái thiết bị',
        );
      }
    } catch (e) {
      print(' [DEBUG] getAllDevicesStatus - Exception: $e');
      return ApiResponse<List<Map<String, dynamic>>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Get device history
  Future<ApiResponse<List<Map<String, dynamic>>>> getDeviceHistory(
    String token,
    int homeId,
    int deviceId,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/homes/$homeId/devices/$deviceId/history'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = json.decode(response.body);
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        final List<dynamic> historyData = data['data'] ?? [];
        final historyList = historyData.cast<Map<String, dynamic>>();

        return ApiResponse<List<Map<String, dynamic>>>.success(
          data: historyList,
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<List<Map<String, dynamic>>>.error(
          message: data['message'] ?? 'Lỗi khi lấy lịch sử thiết bị',
        );
      }
    } catch (e) {
      return ApiResponse<List<Map<String, dynamic>>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Get device properties
  Future<ApiResponse<Map<String, dynamic>>> getDeviceProperties(
    String token,
    int homeId,
    int deviceId,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/homes/$homeId/devices/$deviceId/properties'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = json.decode(response.body);
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        return ApiResponse<Map<String, dynamic>>.success(
          data: data['data'],
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<Map<String, dynamic>>.error(
          message: data['message'] ?? 'Lỗi khi lấy thuộc tính thiết bị',
        );
      }
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Get device commands
  Future<ApiResponse<List<Map<String, dynamic>>>> getDeviceCommands(
    String token,
    int homeId,
    int deviceId,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/homes/$homeId/devices/$deviceId/commands'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = json.decode(response.body);
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        final List<dynamic> commandsData = data['data'] ?? [];
        final commandsList = commandsData.cast<Map<String, dynamic>>();

        return ApiResponse<List<Map<String, dynamic>>>.success(
          data: commandsList,
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<List<Map<String, dynamic>>>.error(
          message: data['message'] ?? 'Lỗi khi lấy lệnh thiết bị',
        );
      }
    } catch (e) {
      return ApiResponse<List<Map<String, dynamic>>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Get device types in home
  Future<ApiResponse<List<Map<String, dynamic>>>> getHomeDeviceTypes(
    String token,
    int homeId,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/homes/$homeId/devices/types'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = json.decode(response.body);
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        final List<dynamic> typesData = data['data'] ?? [];
        final typesList = typesData.cast<Map<String, dynamic>>();

        return ApiResponse<List<Map<String, dynamic>>>.success(
          data: typesList,
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<List<Map<String, dynamic>>>.error(
          message: data['message'] ?? 'Lỗi khi lấy loại thiết bị',
        );
      }
    } catch (e) {
      return ApiResponse<List<Map<String, dynamic>>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Get devices by type in home
  Future<ApiResponse<List<Device>>> getDevicesByTypeInHome(String token, int homeId, int deviceTypeId) async {
    try {
      print('🔍 [DEBUG] getDevicesByTypeInHome - HomeID: $homeId, DeviceTypeID: $deviceTypeId');

      final response = await http.get(
        Uri.parse('$baseUrl${ApiEndpoints.devicesByType(homeId, deviceTypeId)}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('🔍 [DEBUG] getDevicesByTypeInHome - Response: ${response.statusCode}');
      print('🔍 [DEBUG] getDevicesByTypeInHome - Body: ${response.body}');

      final data = json.decode(response.body);
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        final List<dynamic> devicesData = data['data'] ?? [];
        final devices = devicesData.map((deviceJson) => Device.fromJson(deviceJson)).toList();

        return ApiResponse<List<Device>>.success(
          data: devices,
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<List<Device>>.error(
          message: data['message'] ?? 'Lỗi khi lấy thiết bị theo loại',
        );
      }
    } catch (e) {
      print('❌ [DEBUG] getDevicesByTypeInHome - Exception: $e');
      return ApiResponse<List<Device>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Get device types (global)
  Future<ApiResponse<List<String>>> getDeviceTypes(String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/devices/types'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = json.decode(response.body);

      // Check for backend response format
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        final List<dynamic> typesJson = data['data'] ?? [];
        final types = typesJson.map((type) => type.toString()).toList();

        return ApiResponse<List<String>>.success(
          data: types,
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<List<String>>.error(
          message: data['message'] ?? 'Lỗi khi tải danh sách loại thiết bị',
        );
      }
    } catch (e) {
      return ApiResponse<List<String>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Update device property
  Future<ApiResponse<Map<String, dynamic>>> updateDeviceProperty(
    String token,
    int homeId,
    int deviceId,
    String property,
    dynamic value,
  ) async {
    try {
      final response = await http.put(
        Uri.parse('$baseUrl/api/homes/$homeId/devices/$deviceId/property/$property'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode({
          'value': value,
        }),
      );

      final data = json.decode(response.body);
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        return ApiResponse<Map<String, dynamic>>.success(
          data: data['data'],
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<Map<String, dynamic>>.error(
          message: data['message'] ?? 'Lỗi khi cập nhật thuộc tính thiết bị',
        );
      }
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Start device monitoring
  Future<ApiResponse<Map<String, dynamic>>> startDeviceMonitoring(
    String token,
    int homeId,
    int deviceId,
  ) async {
    try {
      final response = await http.post(
        Uri.parse('$baseUrl/api/homes/$homeId/devices/$deviceId/monitor'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = json.decode(response.body);
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        return ApiResponse<Map<String, dynamic>>.success(
          data: data['data'],
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<Map<String, dynamic>>.error(
          message: data['message'] ?? 'Lỗi khi bắt đầu giám sát thiết bị',
        );
      }
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Delete device from system
  Future<ApiResponse<void>> deleteDeviceFromSystem(
    String token,
    int deviceId,
  ) async {
    try {
      final response = await http.delete(
        Uri.parse('$baseUrl/api/devices/$deviceId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      final data = json.decode(response.body);
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        return ApiResponse<void>.success(
          data: null,
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<void>.error(
          message: data['message'] ?? 'Lỗi khi xóa thiết bị',
        );
      }
    } catch (e) {
      return ApiResponse<void>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Update device info (name, area_id, status)
  Future<ApiResponse<Device>> updateDeviceInfo(
    String token,
    int deviceId, {
    String? name,
    int? areaId,
    String? status,
  }) async {
    try {
      final requestBody = <String, dynamic>{};

      if (name != null) requestBody['name'] = name;
      if (areaId != null) requestBody['area_id'] = areaId;
      if (status != null) requestBody['status'] = status;

      print(' [DEBUG] updateDevice - deviceId: $deviceId');
      print(' [DEBUG] updateDevice - Request Body: ${json.encode(requestBody)}');

      final response = await http.put(
        Uri.parse('$baseUrl/api/devices/$deviceId'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(requestBody),
      );

      print(' [DEBUG] updateDevice - Response Status: ${response.statusCode}');
      print(' [DEBUG] updateDevice - Response Body: ${response.body}');

      final data = json.decode(response.body);
      final success = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && success) {
        // Backend returns nil data, so we return a success response without device data
        // The UI will handle reloading the device info
        return ApiResponse<Device>.success(
          data: null,
          message: data['message'] ?? 'Cập nhật thiết bị thành công',
        );
      } else {
        return ApiResponse<Device>.error(
          message: data['message'] ?? 'Lỗi khi cập nhật thiết bị',
        );
      }
    } catch (e) {
      print(' [DEBUG] updateDevice - Exception: $e');
      return ApiResponse<Device>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Discover new devices
  Future<ApiResponse<List<Map<String, dynamic>>>> discoverDevices(
    String token, {
    required List<int> connectionTypes,
    int? scanDuration,
  }) async {
    try {
      final requestBody = {
        'connection_types': connectionTypes,
        if (scanDuration != null) 'scan_duration': scanDuration,
      };

      print(' [DEBUG] discoverDevices - Request Body: ${json.encode(requestBody)}');

      final response = await http.post(
        Uri.parse('$baseUrl/api/devices/discovery'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(requestBody),
      );

      print(' [DEBUG] discoverDevices - Response Status: ${response.statusCode}');
      print(' [DEBUG] discoverDevices - Response Body: ${response.body}');

      final data = json.decode(response.body);
      final success = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && success) {
        final devicesData = data['data'] as List<dynamic>? ?? [];
        final devices = devicesData.map((device) => device as Map<String, dynamic>).toList();

        return ApiResponse<List<Map<String, dynamic>>>.success(
          data: devices,
          message: data['message'] ?? 'Tìm kiếm thiết bị thành công',
        );
      } else {
        return ApiResponse<List<Map<String, dynamic>>>.error(
          message: data['message'] ?? 'Lỗi khi tìm kiếm thiết bị',
        );
      }
    } catch (e) {
      print(' [DEBUG] discoverDevices - Exception: $e');
      return ApiResponse<List<Map<String, dynamic>>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Discover/Scan for new devices
  Future<ApiResponse<List<Map<String, dynamic>>>> scanForDevices(
    String token, {
    required List<int> connectionTypes,
    int? scanDuration,
  }) async {
    try {
      final requestBody = {
        'connection_types': connectionTypes,
        if (scanDuration != null) 'scan_duration': scanDuration,
      };

      print(' [DEBUG] scanForDevices - Request Body: ${json.encode(requestBody)}');

      final response = await http.post(
        Uri.parse('$baseUrl/api/discovery/scan'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(requestBody),
      );

      print(' [DEBUG] scanForDevices - Response Status: ${response.statusCode}');
      print(' [DEBUG] scanForDevices - Response Body: ${response.body}');

      final data = json.decode(response.body);
      final success = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && success) {
        final devicesData = data['data'] as List<dynamic>? ?? [];
        final devices = devicesData.map((device) => device as Map<String, dynamic>).toList();

        return ApiResponse<List<Map<String, dynamic>>>.success(
          data: devices,
          message: data['message'] ?? 'Quét thiết bị thành công',
        );
      } else {
        return ApiResponse<List<Map<String, dynamic>>>.error(
          message: data['message'] ?? 'Lỗi khi quét thiết bị',
        );
      }
    } catch (e) {
      print(' [DEBUG] scanForDevices - Exception: $e');
      return ApiResponse<List<Map<String, dynamic>>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Register a discovered device to the system
  Future<ApiResponse<Device>> registerDiscoveredDevice(
    String token,
    Map<String, dynamic> deviceData,
  ) async {
    try {
      print(' [DEBUG] registerDevice - Request Body: ${json.encode(deviceData)}');

      final response = await http.post(
        Uri.parse('$baseUrl/api/devices'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(deviceData),
      );

      print(' [DEBUG] registerDevice - Response Status: ${response.statusCode}');
      print(' [DEBUG] registerDevice - Response Body: ${response.body}');

      final data = json.decode(response.body);
      final success = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && success) {
        final deviceData = data['data'];
        print(' [DEBUG] Device data before parsing: $deviceData');

        try {
          final device = Device.fromJson(deviceData);
          print(' [DEBUG] Device parsed successfully: $device');

          return ApiResponse<Device>.success(
            data: device,
            message: data['message'] ?? 'Đăng ký thiết bị thành công',
          );
        } catch (parseError) {
          print(' [DEBUG] Error parsing device: $parseError');
          return ApiResponse<Device>.error(
            message: 'Lỗi parse device: $parseError',
          );
        }
      } else {
        return ApiResponse<Device>.error(
          message: data['message'] ?? 'Lỗi khi đăng ký thiết bị',
        );
      }
    } catch (e) {
      print(' [DEBUG] registerDevice - Exception: $e');
      return ApiResponse<Device>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Get device templates
  Future<ApiResponse<List<Map<String, dynamic>>>> getDeviceTemplates(
    String token,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/discovery/templates'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print(' [DEBUG] getDeviceTemplates - Response Status: ${response.statusCode}');
      print(' [DEBUG] getDeviceTemplates - Response Body: ${response.body}');

      final data = json.decode(response.body);
      final success = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && success) {
        final templatesData = data['data'] as List<dynamic>? ?? [];
        final templates = templatesData.map((template) => template as Map<String, dynamic>).toList();

        return ApiResponse<List<Map<String, dynamic>>>.success(
          data: templates,
          message: data['message'] ?? 'Lấy templates thành công',
        );
      } else {
        return ApiResponse<List<Map<String, dynamic>>>.error(
          message: data['message'] ?? 'Lỗi khi lấy templates',
        );
      }
    } catch (e) {
      print(' [DEBUG] getDeviceTemplates - Exception: $e');
      return ApiResponse<List<Map<String, dynamic>>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Get connection types
  Future<ApiResponse<List<Map<String, dynamic>>>> getConnectionTypes(
    String token,
  ) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/discovery/connection-types'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print(' [DEBUG] getConnectionTypes - Response Status: ${response.statusCode}');
      print(' [DEBUG] getConnectionTypes - Response Body: ${response.body}');

      final data = json.decode(response.body);
      final success = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && success) {
        final typesData = data['data'] as List<dynamic>? ?? [];
        final types = typesData.map((type) => type as Map<String, dynamic>).toList();

        return ApiResponse<List<Map<String, dynamic>>>.success(
          data: types,
          message: data['message'] ?? 'Lấy connection types thành công',
        );
      } else {
        return ApiResponse<List<Map<String, dynamic>>>.error(
          message: data['message'] ?? 'Lỗi khi lấy connection types',
        );
      }
    } catch (e) {
      print(' [DEBUG] getConnectionTypes - Exception: $e');
      return ApiResponse<List<Map<String, dynamic>>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Get unique identifiers of existing devices in a home
  Future<ApiResponse<List<String>>> getExistingDeviceUniqueIdentifiers(int homeId, String token) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/homes/$homeId/devices'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('getExistingDeviceUniqueIdentifiers - Status: ${response.statusCode}');

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);

        if (responseData['status'] == true && responseData['data'] != null) {
          final List<dynamic> devicesJson = responseData['data'] as List<dynamic>;
          final List<String> uniqueIdentifiers = [];

          for (final deviceJson in devicesJson) {
            final uniqueId = deviceJson['unique_identifier'] as String?;
            if (uniqueId != null && uniqueId.isNotEmpty) {
              uniqueIdentifiers.add(uniqueId);
            }
          }

          return ApiResponse<List<String>>.success(
            data: uniqueIdentifiers,
            message: 'Lấy danh sách unique_identifier thành công',
          );
        } else {
          return ApiResponse<List<String>>.error(
            message: responseData['message'] ?? 'Không thể lấy danh sách thiết bị',
          );
        }
      } else {
        return ApiResponse<List<String>>.error(
          message: 'Lỗi server: ${response.statusCode}',
        );
      }
    } catch (e) {
      print('getExistingDeviceUniqueIdentifiers - Exception: $e');
      return ApiResponse<List<String>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  // ==================== LAN SCANNING METHODS ====================

  /// Scan local network for ESP32 devices
  Future<ApiResponse<LanScanResult>> scanLANForESP32({
    String? subnet,
    int timeout = 3000,
    List<int>? ports,
    Function(String)? onProgress,
  }) async {
    try {
      final scanner = LanScannerService();
      final startTime = DateTime.now();

      onProgress?.call('Bắt đầu quét mạng LAN...');

      final esp32Devices = await scanner.scanForESP32DevicesUDPOnly(
        timeout: timeout,
        onProgress: onProgress,
      );

      final endTime = DateTime.now();
      final scanDuration = endTime.difference(startTime).inMilliseconds;
      final targetSubnet = subnet ?? await NetworkUtils.getLocalIP();
      final subnetBase = targetSubnet != null ? NetworkUtils.getSubnet(targetSubnet) : 'Unknown';

      final result = LanScanResult(
        subnet: subnetBase ?? 'Unknown',
        scanDurationMs: scanDuration,
        devicesFound: esp32Devices.length,
        esp32Devices: esp32Devices,
        otherDevices: [], // For now, we only focus on ESP32 devices
        scanTimestamp: startTime,
        scanRange: '${subnetBase ?? 'Unknown'}.1-254',
      );

      return ApiResponse<LanScanResult>.success(
        data: result,
        message: 'Quét LAN thành công. Tìm thấy ${esp32Devices.length} thiết bị ESP32',
      );

    } catch (e) {
      return ApiResponse<LanScanResult>.error(
        message: 'Lỗi khi quét LAN: $e',
      );
    }
  }

  /// Scan local network for ESP32 devices using ONLY UDP broadcast listening
  Future<ApiResponse<LanScanResult>> scanLANForESP32UDPOnly({
    int timeout = 5000,
    Function(String)? onProgress,
    List<String>? existingUniqueIdentifiers,
  }) async {
    try {
      final scanner = LanScannerService();
      final startTime = DateTime.now();

      onProgress?.call('Bắt đầu quét UDP broadcasts...');

      final esp32Devices = await scanner.scanForESP32DevicesUDPOnly(
        timeout: timeout,
        onProgress: onProgress,
        existingUniqueIdentifiers: existingUniqueIdentifiers,
      );

      final endTime = DateTime.now();
      final scanDuration = endTime.difference(startTime).inMilliseconds;
      final targetSubnet = await NetworkUtils.getLocalIP();
      final subnetBase = targetSubnet != null ? NetworkUtils.getSubnet(targetSubnet) : 'Unknown';

      final result = LanScanResult(
        subnet: subnetBase ?? 'Unknown',
        scanDurationMs: scanDuration,
        devicesFound: esp32Devices.length,
        esp32Devices: esp32Devices,
        otherDevices: [], // UDP scan only finds ESP32 devices
        scanTimestamp: startTime,
        scanRange: 'UDP Broadcast Port 8266',
      );

      return ApiResponse<LanScanResult>.success(
        data: result,
        message: 'Quét UDP thành công. Tìm thấy ${esp32Devices.length} thiết bị ESP32',
      );

    } catch (e) {
      return ApiResponse<LanScanResult>.error(
        message: 'Lỗi khi quét UDP: $e',
      );
    }
  }

  /// Test connection to ESP32 device
  Future<ApiResponse<bool>> testESP32Connection(String ip, int port) async {
    try {
      final scanner = LanScannerService();
      final isConnected = await scanner.testConnection(ip, port);

      return ApiResponse<bool>.success(
        data: isConnected,
        message: isConnected ? 'Kết nối thành công' : 'Không thể kết nối',
      );
    } catch (e) {
      return ApiResponse<bool>.error(
        message: 'Lỗi khi test kết nối: $e',
      );
    }
  }

  /// Send HTTP request to ESP32 device
  Future<ApiResponse<Map<String, dynamic>>> sendESP32Request(
    String ip,
    int port,
    String endpoint, {
    String method = 'GET',
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    int timeout = 5000,
  }) async {
    try {
      final scanner = LanScannerService();
      final response = await scanner.sendRequest(
        ip,
        port,
        endpoint,
        method: method,
        body: body,
        headers: headers,
        timeout: timeout,
      );

      if (response != null) {
        return ApiResponse<Map<String, dynamic>>.success(
          data: response,
          message: 'Gửi request thành công',
        );
      } else {
        return ApiResponse<Map<String, dynamic>>.error(
          message: 'Không nhận được response từ thiết bị',
        );
      }
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>.error(
        message: 'Lỗi khi gửi request: $e',
      );
    }
  }

  /// Get network information
  Future<ApiResponse<Map<String, dynamic>>> getNetworkInfo() async {
    try {
      final networkInfo = await NetworkUtils.getNetworkInfo();

      return ApiResponse<Map<String, dynamic>>.success(
        data: networkInfo,
        message: 'Lấy thông tin mạng thành công',
      );
    } catch (e) {
      return ApiResponse<Map<String, dynamic>>.error(
        message: 'Lỗi khi lấy thông tin mạng: $e',
      );
    }
  }

  /// Register ESP32 device to backend (add to home)
  Future<ApiResponse<Device>> registerESP32Device(
    String token,
    ESP32Device esp32Device, {
    String? deviceName,
    String? deviceType,
    required int homeId,
    int? areaId,
  }) async {
    try {

      // Convert ESP32Device to backend AddDeviceToHomeRequest format
      final deviceData = {
        'name': deviceName ?? esp32Device.deviceName,
        'device_type_id': esp32Device.additionalInfo?['device_type_id'] ?? 1, // Smart Light default
        'connection_type_id': esp32Device.additionalInfo?['connection_type_id'] ?? 1, // WiFi default
        'unique_identifier': esp32Device.additionalInfo?['unique_identifier'] ?? esp32Device.macAddress ?? esp32Device.fullAddress,
        'area_id': areaId, // Optional
        'network_info': {
          'ip_address': esp32Device.ipAddress,
          'port': esp32Device.port,
          'mac_address': esp32Device.macAddress,
          'subnet_mask': esp32Device.additionalInfo?['network_info']?['subnet_mask'],
          'gateway': esp32Device.additionalInfo?['network_info']?['gateway'],
          'dns_server': esp32Device.additionalInfo?['network_info']?['dns_server'],
        },
      };

      return await addDeviceToHome(token, homeId, deviceData);
    } catch (e) {
      return ApiResponse<Device>.error(
        message: 'Lỗi khi đăng ký thiết bị ESP32: $e',
      );
    }
  }

  /// Add device to home using correct backend endpoint
  Future<ApiResponse<Device>> addDeviceToHome(
    String token,
    int homeId,
    Map<String, dynamic> deviceData,
  ) async {
    try {
      print(' [DEBUG] addDeviceToHome - HomeID: $homeId');
      print(' [DEBUG] addDeviceToHome - Request Body: ${json.encode(deviceData)}');

      final response = await http.post(
        Uri.parse('$baseUrl/api/admin/homes/$homeId/devices'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: json.encode(deviceData),
      );

      print(' [DEBUG] addDeviceToHome - Response Status: ${response.statusCode}');
      print(' [DEBUG] addDeviceToHome - Response Body: ${response.body}');

      final data = json.decode(response.body);
      final success = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && success) {
        final deviceData = data['data'];
        print(' [DEBUG] Device data before parsing: $deviceData');

        try {
          final device = Device.fromJson(deviceData);
          print(' [DEBUG] Device parsed successfully: $device');

          return ApiResponse<Device>.success(
            data: device,
            message: data['message'] ?? 'Thêm thiết bị vào nhà thành công',
          );
        } catch (parseError) {
          print(' [DEBUG] Error parsing device: $parseError');
          return ApiResponse<Device>.error(
            message: 'Lỗi parse device: $parseError',
          );
        }
      } else {
        return ApiResponse<Device>.error(
          message: data['message'] ?? 'Lỗi khi thêm thiết bị vào nhà',
        );
      }
    } catch (e) {
      print(' [DEBUG] addDeviceToHome - Exception: $e');
      return ApiResponse<Device>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  // ==================== DEVICE TYPES MANAGEMENT ====================

  /// Get all device types
  Future<ApiResponse<List<DeviceType>>> getAllDeviceTypes(String token) async {
    try {
      print('🔍 [DEBUG] getAllDeviceTypes - Making request...');

      final response = await http.get(
        Uri.parse('$baseUrl${ApiEndpoints.deviceTypesManagement()}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('🔍 [DEBUG] getAllDeviceTypes - Status: ${response.statusCode}');
      print('🔍 [DEBUG] getAllDeviceTypes - Response: ${response.body}');

      final data = json.decode(response.body);

      if (response.statusCode == 200) {
        final List<dynamic> deviceTypesJson = data['data'] ?? [];
        final deviceTypes = deviceTypesJson
            .map((json) => DeviceType.fromJson(json))
            .toList();

        return ApiResponse<List<DeviceType>>.success(
          data: deviceTypes,
          message: data['message'] ?? 'Lấy danh sách loại thiết bị thành công',
        );
      } else {
        return ApiResponse<List<DeviceType>>.error(
          message: data['message'] ?? 'Lỗi khi lấy danh sách loại thiết bị',
        );
      }
    } catch (e) {
      print('❌ [DEBUG] getAllDeviceTypes - Exception: $e');
      return ApiResponse<List<DeviceType>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Get device type detail by ID
  Future<ApiResponse<DeviceType>> getDeviceTypeDetail(String token, int deviceTypeId) async {
    try {
      print('🔍 [DEBUG] getDeviceTypeDetail - ID: $deviceTypeId');

      final response = await http.get(
        Uri.parse('$baseUrl${ApiEndpoints.deviceTypeManagement(deviceTypeId)}'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('🔍 [DEBUG] getDeviceTypeDetail - Status: ${response.statusCode}');
      print('🔍 [DEBUG] getDeviceTypeDetail - Response: ${response.body}');

      final data = json.decode(response.body);

      if (response.statusCode == 200) {
        final deviceType = DeviceType.fromJson(data['data']);
        return ApiResponse<DeviceType>.success(
          data: deviceType,
          message: data['message'] ?? 'Lấy chi tiết loại thiết bị thành công',
        );
      } else {
        return ApiResponse<DeviceType>.error(
          message: data['message'] ?? 'Lỗi khi lấy chi tiết loại thiết bị',
        );
      }
    } catch (e) {
      print('❌ [DEBUG] getDeviceTypeDetail - Exception: $e');
      return ApiResponse<DeviceType>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  // CRUD operations removed - only view operations available

  // Templates method removed - using local predefined templates only
}
