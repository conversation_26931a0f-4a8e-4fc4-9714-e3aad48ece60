import 'dart:convert';
import 'package:http/http.dart' as http;
import '../config/api_config.dart';
import '../models/api_response.dart';
import '../models/home_invitation_model.dart';

/// Repository for home invitation operations
class HomeInvitationRepository {
  final String baseUrl = ApiConfig.baseUrl;

  /// Get headers with authorization
  Map<String, String> _getHeaders(String token) {
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  /// Send invitation to user
  Future<ApiResponse<HomeInvitation>> sendInvitation({
    required String token,
    required int homeId,
    required String email,
    int? roleId,
  }) async {
    try {
      final url = '$baseUrl/api/admin/homes/$homeId/invitations';
      final requestBody = {
        'email': email,
        if (roleId != null) 'role_id': roleId,
      };

      print(' [DEBUG] sendInvitation - URL: $url');
      print(' [DEBUG] sendInvitation - Request Body: ${json.encode(requestBody)}');

      final response = await http.post(
        Uri.parse(url),
        headers: _getHeaders(token),
        body: json.encode(requestBody),
      );

      print(' [DEBUG] sendInvitation - Status Code: ${response.statusCode}');
      print(' [DEBUG] sendInvitation - Response Body: ${response.body}');

      final data = json.decode(response.body);
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        // Check if data field exists and is not null
        if (data['data'] != null) {
          final invitationData = data['data'] as Map<String, dynamic>;
          final invitation = HomeInvitation.fromJson(invitationData);

          return ApiResponse<HomeInvitation>.success(
            data: invitation,
            message: data['message'] ?? 'Gửi lời mời thành công',
          );
        } else {
          // Success but no data returned (some APIs just return status/message)
          return ApiResponse<HomeInvitation>.success(
            data: null,
            message: data['message'] ?? 'Gửi lời mời thành công',
          );
        }
      } else {
        return ApiResponse<HomeInvitation>.error(
          message: data['message'] ?? 'Lỗi khi gửi lời mời',
        );
      }
    } catch (e) {
      print(' [DEBUG] sendInvitation - Exception: $e');
      return ApiResponse<HomeInvitation>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Get list of invitations for a home
  Future<ApiResponse<List<HomeInvitation>>> getInvitations({
    required String token,
    required int homeId,
  }) async {
    try {
      final url = '$baseUrl/api/admin/homes/$homeId/invitations';

      print(' [DEBUG] getInvitations - URL: $url');

      final response = await http.get(
        Uri.parse(url),
        headers: _getHeaders(token),
      );

      print(' [DEBUG] getInvitations - Status Code: ${response.statusCode}');
      print(' [DEBUG] getInvitations - Response Body: ${response.body}');

      final data = json.decode(response.body);
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        // Check if data field exists and is not null
        if (data['data'] != null) {
          final invitationsData = data['data'] as List<dynamic>;
          final invitations = invitationsData
              .map((item) => HomeInvitation.fromJson(item as Map<String, dynamic>))
              .toList();

          return ApiResponse<List<HomeInvitation>>.success(
            data: invitations,
            message: data['message'] ?? 'Lấy danh sách lời mời thành công',
          );
        } else {
          // Success but no data (empty list)
          return ApiResponse<List<HomeInvitation>>.success(
            data: [],
            message: data['message'] ?? 'Không có lời mời nào',
          );
        }
      } else {
        return ApiResponse<List<HomeInvitation>>.error(
          message: data['message'] ?? 'Lỗi khi lấy danh sách lời mời',
        );
      }
    } catch (e) {
      print(' [DEBUG] getInvitations - Exception: $e');
      return ApiResponse<List<HomeInvitation>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Accept invitation (public endpoint)
  Future<ApiResponse<Map<String, dynamic>>> acceptInvitation({
    required String invitationToken,
  }) async {
    try {
      final url = '$baseUrl/api/invitations/accept/$invitationToken';

      print(' [DEBUG] acceptInvitation - URL: $url');

      final response = await http.get(Uri.parse(url));

      print(' [DEBUG] acceptInvitation - Status Code: ${response.statusCode}');
      print(' [DEBUG] acceptInvitation - Response Body: ${response.body}');

      final data = json.decode(response.body);
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        return ApiResponse<Map<String, dynamic>>.success(
          data: data['data'] as Map<String, dynamic>? ?? {},
          message: data['message'] ?? 'Chấp nhận lời mời thành công',
        );
      } else {
        return ApiResponse<Map<String, dynamic>>.error(
          message: data['message'] ?? 'Lỗi khi chấp nhận lời mời',
        );
      }
    } catch (e) {
      print(' [DEBUG] acceptInvitation - Exception: $e');
      return ApiResponse<Map<String, dynamic>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Get invitation page info (public endpoint)
  Future<ApiResponse<Map<String, dynamic>>> getInvitationPage({
    required String invitationToken,
  }) async {
    try {
      final url = '$baseUrl/api/invitations/page/$invitationToken';

      print(' [DEBUG] getInvitationPage - URL: $url');

      final response = await http.get(Uri.parse(url));

      print(' [DEBUG] getInvitationPage - Status Code: ${response.statusCode}');
      print(' [DEBUG] getInvitationPage - Response Body: ${response.body}');

      final data = json.decode(response.body);
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        return ApiResponse<Map<String, dynamic>>.success(
          data: data['data'] as Map<String, dynamic>? ?? {},
          message: data['message'] ?? 'Lấy thông tin lời mời thành công',
        );
      } else {
        return ApiResponse<Map<String, dynamic>>.error(
          message: data['message'] ?? 'Lỗi khi lấy thông tin lời mời',
        );
      }
    } catch (e) {
      print(' [DEBUG] getInvitationPage - Exception: $e');
      return ApiResponse<Map<String, dynamic>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Cancel/Delete invitation
  Future<ApiResponse<void>> cancelInvitation({
    required String token,
    required int homeId,
    required int invitationId,
  }) async {
    try {
      final url = '$baseUrl/api/admin/homes/$homeId/invitations/$invitationId';

      print(' [DEBUG] cancelInvitation - URL: $url');

      final response = await http.delete(
        Uri.parse(url),
        headers: _getHeaders(token),
      );

      print(' [DEBUG] cancelInvitation - Status Code: ${response.statusCode}');
      print(' [DEBUG] cancelInvitation - Response Body: ${response.body}');

      final data = json.decode(response.body);
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        return ApiResponse<void>.success(
          message: data['message'] ?? 'Hủy lời mời thành công',
        );
      } else {
        return ApiResponse<void>.error(
          message: data['message'] ?? 'Lỗi khi hủy lời mời',
        );
      }
    } catch (e) {
      print(' [DEBUG] cancelInvitation - Exception: $e');
      return ApiResponse<void>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  /// Resend invitation
  Future<ApiResponse<HomeInvitation>> resendInvitation({
    required String token,
    required int homeId,
    required int invitationId,
  }) async {
    try {
      final url = '$baseUrl/api/admin/homes/$homeId/invitations/$invitationId/resend';

      print(' [DEBUG] resendInvitation - URL: $url');

      final response = await http.post(
        Uri.parse(url),
        headers: _getHeaders(token),
      );

      print(' [DEBUG] resendInvitation - Status Code: ${response.statusCode}');
      print(' [DEBUG] resendInvitation - Response Body: ${response.body}');

      final data = json.decode(response.body);
      final status = data['status'] as bool? ?? false;

      if (response.statusCode == 200 && status) {
        // Check if data field exists and is not null
        if (data['data'] != null) {
          final invitationData = data['data'] as Map<String, dynamic>;
          final invitation = HomeInvitation.fromJson(invitationData);

          return ApiResponse<HomeInvitation>.success(
            data: invitation,
            message: data['message'] ?? 'Gửi lại lời mời thành công',
          );
        } else {
          // Success but no data returned
          return ApiResponse<HomeInvitation>.success(
            data: null,
            message: data['message'] ?? 'Gửi lại lời mời thành công',
          );
        }
      } else {
        return ApiResponse<HomeInvitation>.error(
          message: data['message'] ?? 'Lỗi khi gửi lại lời mời',
        );
      }
    } catch (e) {
      print(' [DEBUG] resendInvitation - Exception: $e');
      return ApiResponse<HomeInvitation>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }
}
