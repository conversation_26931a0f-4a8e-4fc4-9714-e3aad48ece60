import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/permission_model.dart';
import '../models/api_response.dart';
import '../models/home_model.dart';
import '../models/device_model.dart';
import '../models/area_model.dart';
import '../config/api_config.dart';

class PermissionRepository {
   final String baseUrl = ApiConfig.baseUrl;

  // Helper method để tạo headers
  Map<String, String> _getHeaders(String token) {
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // <PERSON><PERSON><PERSON> danh sách quyền của user trong một home với filter
  Future<ApiResponse<List<Permission>>> getUserPermissions(
    String token,
    int homeId, {
    String? filter, // 'device', 'area', 'all' hoặc null (default all)
  }) async {
    try {
      String url;
      switch (filter) {
        case 'device':
          url = '${ApiConfig.baseUrl}${ApiEndpoints.homeDevicePermissions(homeId)}';
          break;
        case 'area':
          url = '${ApiConfig.baseUrl}${ApiEndpoints.homeAreaPermissions(homeId)}';
          break;
        case 'all':
        default:
          url = '${ApiConfig.baseUrl}${ApiEndpoints.homePermissions(homeId)}';
          break;
      }

      print('🔍 Permission API Request: $url (filter: $filter)');

      final response = await http.get(
        Uri.parse(url),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
      );

      print('🔍 Permission API Response Status: ${response.statusCode}');
      print('🔍 Permission API Response Body: ${response.body}');

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        // Debug: Check data structure
        print('🔍 Permission API Data Structure: ${data.runtimeType}');
        print('🔍 Permission API Data Keys: ${data.keys}');

        final dynamic responseData = data['data'];
        print('🔍 Response Data Type: ${responseData.runtimeType}');

        if (responseData == null) {
          return ApiResponse<List<Permission>>.success(
            data: [],
            message: data['message'] ?? 'No permissions found',
          );
        }

        List<Permission> permissions = [];

        if (responseData is Map<String, dynamic>) {
          // New structure: data.permissions contains the array
          print('🔍 Response Data Map Keys: ${responseData.keys}');
          final permissionsArray = responseData['permissions'];
          print('🔍 Permissions Array Type: ${permissionsArray.runtimeType}');
          print('🔍 Permissions Array Content: $permissionsArray');

          if (permissionsArray is List) {
            print('🔍 Processing ${permissionsArray.length} permissions...');
            permissions = permissionsArray.map((json) {
              print('🔍 Parsing permission: $json');
              try {
                return Permission.fromJson(json as Map<String, dynamic>);
              } catch (e) {
                print('🔍 Error parsing permission: $e');
                return null;
              }
            }).where((p) => p != null).cast<Permission>().toList();

            print('🔍 Successfully parsed ${permissions.length} permissions');
          } else {
            print('🔍 Permissions array is not a List: ${permissionsArray.runtimeType}');
          }
        } else if (responseData is List) {
          // Old structure: data is directly the array
          print('🔍 Processing direct array with ${responseData.length} items...');
          permissions = responseData.map((json) => Permission.fromJson(json as Map<String, dynamic>)).toList();
        } else {
          print('🔍 Unexpected responseData type: ${responseData.runtimeType}');
        }

        print('🔍 Parsed ${permissions.length} permissions');

        return ApiResponse<List<Permission>>.success(
          data: permissions,
          message: data['message'],
        );
      } else {
        return ApiResponse<List<Permission>>.error(
          message: data['message'] ?? 'Lấy danh sách quyền thất bại',
        );
      }
    } catch (e) {
      print('🔍 Permission API Error: $e');
      return ApiResponse<List<Permission>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  // Thêm quyền thiết bị cho user
  Future<ApiResponse<void>> addDevicePermission(String token, int homeId, PermissionRequest request) async {
    final response = await http.post(
      Uri.parse('$baseUrl/api/admin/homes/$homeId/permissions/device'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode(request.toJson()),
    );

    final data = jsonDecode(response.body);
    
    if (response.statusCode == 200) {
      return ApiResponse<void>.success(
        data: null,
        message: data['message'],
      );
    } else {
      return ApiResponse<void>.error(
        message: data['message'] ?? 'Thêm quyền thiết bị thất bại',
      );
    }
  }

  // Thêm quyền khu vực cho user
  Future<ApiResponse<void>> addAreaPermission(String token, int homeId, PermissionRequest request) async {
    final response = await http.post(
      Uri.parse('$baseUrl/api/admin/homes/$homeId/permissions/area'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode(request.toJson()),
    );

    final data = jsonDecode(response.body);
    
    if (response.statusCode == 200) {
      return ApiResponse<void>.success(
        data: null,
        message: data['message'],
      );
    } else {
      return ApiResponse<void>.error(
        message: data['message'] ?? 'Thêm quyền khu vực thất bại',
      );
    }
  }

  // Cập nhật quyền
  Future<ApiResponse<void>> updatePermission(String token, int homeId, int permissionId, PermissionRequest request) async {
    final response = await http.put(
      Uri.parse('$baseUrl/api/admin/homes/$homeId/permissions/$permissionId'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode(request.toJson()),
    );

    final data = jsonDecode(response.body);
    
    if (response.statusCode == 200) {
      return ApiResponse<void>.success(
        data: null,
        message: data['message'],
      );
    } else {
      return ApiResponse<void>.error(
        message: data['message'] ?? 'Cập nhật quyền thất bại',
      );
    }
  }

  // Xóa quyền
  Future<ApiResponse<void>> removePermission(String token, int homeId, int permissionId) async {
    final response = await http.delete(
      Uri.parse('$baseUrl/api/admin/homes/$homeId/permissions/$permissionId'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
    );

    final data = jsonDecode(response.body);
    
    if (response.statusCode == 200) {
      return ApiResponse<void>.success(
        data: null,
        message: data['message'],
      );
    } else {
      return ApiResponse<void>.error(
        message: data['message'] ?? 'Xóa quyền thất bại',
      );
    }
  }

  // Phân quyền cho user
  Future<ApiResponse<void>> assignRole(String token, int userId, String roleName) async {
    final response = await http.post(
      Uri.parse('$baseUrl/api/admin/users/$userId/role'),
      headers: _getHeaders(token),
      body: jsonEncode({
        'role_name': roleName,
      }),
    );

    final data = jsonDecode(response.body);

    if (response.statusCode == 200) {
      return ApiResponse<void>.success(
        data: null,
        message: data['message'],
      );
    } else {
      return ApiResponse<void>.error(
        message: data['message'] ?? 'Phân quyền thất bại',
      );
    }
  }

  // Lấy danh sách users trong home
  Future<ApiResponse<List<HomeUser>>> getHomeUsers(String token, int homeId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/admin/homes/$homeId/users'),
        headers: _getHeaders(token),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        final List<dynamic> usersData = data['data'] ?? [];
        final users = usersData
            .map((json) => HomeUser.fromJson(json as Map<String, dynamic>))
            .toList();

        return ApiResponse<List<HomeUser>>.success(
          data: users,
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<List<HomeUser>>.error(
          message: data['message'] ?? 'Lấy danh sách users thất bại',
        );
      }
    } catch (e) {
      return ApiResponse<List<HomeUser>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  // Lấy danh sách devices trong home
  Future<ApiResponse<List<Device>>> getHomeDevices(String token, int homeId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/admin/homes/$homeId/devices'),
        headers: _getHeaders(token),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        final List<dynamic> devicesData = data['data'] ?? [];
        final devices = devicesData
            .map((json) => Device.fromJson(json as Map<String, dynamic>))
            .toList();

        return ApiResponse<List<Device>>.success(
          data: devices,
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<List<Device>>.error(
          message: data['message'] ?? 'Lấy danh sách devices thất bại',
        );
      }
    } catch (e) {
      return ApiResponse<List<Device>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }

  // Lấy danh sách areas trong home
  Future<ApiResponse<List<Area>>> getHomeAreas(String token, int homeId) async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/api/admin/homes/$homeId/areas'),
        headers: _getHeaders(token),
      );

      final data = jsonDecode(response.body);

      if (response.statusCode == 200) {
        final List<dynamic> areasData = data['data'] ?? [];
        final areas = areasData
            .map((json) => Area.fromJson(json as Map<String, dynamic>))
            .toList();

        return ApiResponse<List<Area>>.success(
          data: areas,
          message: data['message'] ?? 'Success',
        );
      } else {
        return ApiResponse<List<Area>>.error(
          message: data['message'] ?? 'Lấy danh sách areas thất bại',
        );
      }
    } catch (e) {
      return ApiResponse<List<Area>>.error(
        message: 'Lỗi kết nối: $e',
      );
    }
  }
}
