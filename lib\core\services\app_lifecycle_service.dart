import 'dart:async';
import 'package:flutter/material.dart';


/// Service để quản lý lifecycle của các background services
/// Giúp tối ưu hóa performance và giảm API calls không cần thiết
class AppLifecycleService with WidgetsBindingObserver {
  static final AppLifecycleService _instance = AppLifecycleService._internal();
  factory AppLifecycleService() => _instance;
  AppLifecycleService._internal();

  // Services

  
  // State tracking
  bool _isAppInBackground = false;
  bool _isOnAuthPage = false;
  bool _isOnChangePasswordPage = false;
  Timer? _backgroundTimer;
  
  // Getters
  bool get isAppInBackground => _isAppInBackground;
  bool get isOnAuthPage => _isOnAuthPage;
  bool get isOnChangePasswordPage => _isOnChangePasswordPage;
  bool get shouldPauseBackgroundServices => 
      _isAppInBackground || _isOnAuthPage || _isOnChangePasswordPage;

  /// Initialize the service
  void initialize() {
    WidgetsBinding.instance.addObserver(this);
  }

  /// Dispose the service
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _backgroundTimer?.cancel();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    switch (state) {
      case AppLifecycleState.resumed:
        _handleAppResumed();
        break;
      case AppLifecycleState.paused:
      case AppLifecycleState.inactive:
        _handleAppPaused();
        break;
      case AppLifecycleState.detached:
        _handleAppDetached();
        break;
      case AppLifecycleState.hidden:
        _handleAppHidden();
        break;
    }
  }

  /// Notify when entering auth pages (login, register, etc.)
  void enterAuthPage() {
    _isOnAuthPage = true;
    _pauseBackgroundServices();
  }

  /// Notify when leaving auth pages
  void exitAuthPage() {
    _isOnAuthPage = false;
    if (!shouldPauseBackgroundServices) {
      _resumeBackgroundServices();
    }
  }

  /// Notify when entering change password page
  void enterChangePasswordPage() {
    _isOnChangePasswordPage = true;
    _pauseNonEssentialServices();
  }

  /// Notify when leaving change password page
  void exitChangePasswordPage() {
    _isOnChangePasswordPage = false;
    if (!shouldPauseBackgroundServices) {
      _resumeBackgroundServices();
    }
  }

  /// Handle app resumed
  void _handleAppResumed() {
    print('🔄 App resumed - checking background services');
    _isAppInBackground = false;
    _backgroundTimer?.cancel();
    
    if (!shouldPauseBackgroundServices) {
      _resumeBackgroundServices();
    }
  }

  /// Handle app paused
  void _handleAppPaused() {
    print('⏸️ App paused - pausing background services');
    _isAppInBackground = true;
    _pauseBackgroundServices();
    
    // Background timer removed
  }

  /// Handle app detached
  void _handleAppDetached() {
    print('🔌 App detached - services disconnected');
  }

  /// Handle app hidden
  void _handleAppHidden() {
    print('👁️ App hidden - pausing background services');
    _pauseBackgroundServices();
  }

  /// Pause all background services
  void _pauseBackgroundServices() {
    print('⏸️ Pausing background services');
    // Background services paused
  }

  /// Pause only non-essential services (for change password page)
  void _pauseNonEssentialServices() {
    print('⏸️ Pausing non-essential services for change password');
    // Keep essential services running but reduce frequency
  }

  /// Resume background services
  void _resumeBackgroundServices() {
    print('▶️ Resuming background services');
    // Services will automatically resume when components check shouldPauseBackgroundServices
  }

  // WebSocket methods removed

  /// Check if should allow background API calls
  bool shouldAllowBackgroundCalls() {
    return !shouldPauseBackgroundServices;
  }

  // WebSocket methods removed

  /// Check if should allow polling/timers
  bool shouldAllowPolling() {
    return !shouldPauseBackgroundServices;
  }

  /// Get recommended polling interval based on current state
  Duration getPollingInterval() {
    if (_isOnChangePasswordPage) {
      return const Duration(minutes: 5); // Reduce frequency
    }
    if (_isAppInBackground) {
      return const Duration(minutes: 10); // Much less frequent
    }
    return const Duration(seconds: 30); // Normal frequency
  }
}

/// Mixin for widgets to easily use lifecycle service
mixin AppLifecycleMixin<T extends StatefulWidget> on State<T> {
  AppLifecycleService get lifecycleService => AppLifecycleService();

  @override
  void initState() {
    super.initState();
    lifecycleService.initialize();
  }

  @override
  void dispose() {
    lifecycleService.dispose();
    super.dispose();
  }

  /// Check if should perform background operations
  bool get shouldPerformBackgroundOps => lifecycleService.shouldAllowBackgroundCalls();
  
  // WebSocket getter removed
  
  /// Check if should start polling
  bool get shouldStartPolling => lifecycleService.shouldAllowPolling();
  
  /// Get polling interval
  Duration get pollingInterval => lifecycleService.getPollingInterval();
}
