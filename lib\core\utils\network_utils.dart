import 'dart:io';
import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;

/// Network utility functions for device discovery and network operations
class NetworkUtils {
  
  /// Check if an IP address is valid
  static bool isValidIP(String ip) {
    try {
      final addr = InternetAddress(ip);
      return addr.type == InternetAddressType.IPv4;
    } catch (e) {
      return false;
    }
  }
  
  /// Check if an IP is in private range
  static bool isPrivateIP(String ip) {
    if (!isValidIP(ip)) return false;
    
    return ip.startsWith('192.168.') ||
           ip.startsWith('10.') ||
           ip.startsWith('172.16.') ||
           ip.startsWith('172.17.') ||
           ip.startsWith('172.18.') ||
           ip.startsWith('172.19.') ||
           ip.startsWith('172.20.') ||
           ip.startsWith('172.21.') ||
           ip.startsWith('172.22.') ||
           ip.startsWith('172.23.') ||
           ip.startsWith('172.24.') ||
           ip.startsWith('172.25.') ||
           ip.startsWith('172.26.') ||
           ip.startsWith('172.27.') ||
           ip.startsWith('172.28.') ||
           ip.startsWith('172.29.') ||
           ip.startsWith('172.30.') ||
           ip.startsWith('172.31.');
  }
  
  /// Get all local network interfaces
  static Future<List<NetworkInterface>> getLocalInterfaces() async {
    try {
      return await NetworkInterface.list(includeLoopback: false);
    } catch (e) {
      print('Error getting network interfaces: $e');
      return [];
    }
  }
  
  /// Get local IP address
  static Future<String?> getLocalIP() async {
    try {
      final interfaces = await getLocalInterfaces();
      
      for (final interface in interfaces) {
        for (final addr in interface.addresses) {
          if (addr.type == InternetAddressType.IPv4 && 
              !addr.isLoopback && 
              isPrivateIP(addr.address)) {
            return addr.address;
          }
        }
      }
    } catch (e) {
      print('Error getting local IP: $e');
    }
    
    return null;
  }
  
  /// Get subnet from IP address (e.g., "*************" -> "192.168.1")
  static String? getSubnet(String ip) {
    if (!isValidIP(ip)) return null;
    
    final parts = ip.split('.');
    if (parts.length >= 3) {
      return '${parts[0]}.${parts[1]}.${parts[2]}';
    }
    
    return null;
  }
  
  /// Generate IP range for scanning
  static List<String> generateIPRange(String subnet, {int start = 1, int end = 254}) {
    final List<String> ips = [];
    
    for (int i = start; i <= end; i++) {
      ips.add('$subnet.$i');
    }
    
    return ips;
  }
  
  /// Ping an IP address (check if reachable)
  static Future<bool> ping(String ip, {int timeout = 3000}) async {
    try {
      final result = await Process.run(
        Platform.isWindows ? 'ping' : 'ping',
        Platform.isWindows 
          ? ['-n', '1', '-w', timeout.toString(), ip]
          : ['-c', '1', '-W', (timeout / 1000).toString(), ip],
      ).timeout(Duration(milliseconds: timeout + 1000));
      
      return result.exitCode == 0;
    } catch (e) {
      return false;
    }
  }
  
  /// Check if a port is open on an IP
  static Future<bool> isPortOpen(String ip, int port, {int timeout = 3000}) async {
    try {
      final socket = await Socket.connect(
        ip, 
        port, 
        timeout: Duration(milliseconds: timeout),
      );
      await socket.close();
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// Scan multiple ports on an IP
  static Future<List<int>> scanPorts(String ip, List<int> ports, {int timeout = 1000}) async {
    final List<int> openPorts = [];
    final List<Future<void>> tasks = [];
    
    for (final port in ports) {
      tasks.add(() async {
        if (await isPortOpen(ip, port, timeout: timeout)) {
          openPorts.add(port);
        }
      }());
    }
    
    await Future.wait(tasks);
    openPorts.sort();
    
    return openPorts;
  }
  
  /// Get device info via HTTP
  static Future<Map<String, dynamic>?> getDeviceInfo(
    String ip, 
    int port, {
    List<String>? endpoints,
    int timeout = 5000,
  }) async {
    final defaultEndpoints = endpoints ?? [
      '/info',
      '/device',
      '/status', 
      '/api/info',
      '/api/device',
      '/api/status',
      '/',
    ];
    
    for (final endpoint in defaultEndpoints) {
      try {
        final response = await http.get(
          Uri.parse('http://$ip:$port$endpoint'),
          headers: {
            'User-Agent': 'SmartHome-Scanner/1.0',
            'Accept': 'application/json, text/plain, */*',
          },
        ).timeout(Duration(milliseconds: timeout));
        
        if (response.statusCode == 200) {
          try {
            // Try to parse as JSON
            final json = jsonDecode(response.body);
            return {
              'endpoint': endpoint,
              'data': json,
              'raw': response.body,
            };
          } catch (e) {
            // Return raw response if not JSON
            return {
              'endpoint': endpoint,
              'raw': response.body,
              'is_json': false,
            };
          }
        }
      } catch (e) {
        continue;
      }
    }
    
    return null;
  }
  
  /// Check if response indicates ESP32 device
  static bool isESP32Device(Map<String, dynamic> deviceInfo) {
    final raw = deviceInfo['raw']?.toString().toLowerCase() ?? '';
    final data = deviceInfo['data'] as Map<String, dynamic>?;
    
    // Check raw response content
    if (raw.contains('esp32') ||
        raw.contains('espressif') ||
        raw.contains('arduino') ||
        raw.contains('nodemcu') ||
        raw.contains('wemos')) {
      return true;
    }
    
    // Check JSON data
    if (data != null) {
      final deviceType = data['type']?.toString().toLowerCase() ?? '';
      final deviceName = data['name']?.toString().toLowerCase() ?? '';
      final chipId = data['chip_id']?.toString() ?? '';
      
      if (deviceType.contains('esp32') ||
          deviceName.contains('esp32') ||
          chipId.isNotEmpty) {
        return true;
      }
    }
    
    return false;
  }
  
  /// Get network info summary
  static Future<Map<String, dynamic>> getNetworkInfo() async {
    final localIP = await getLocalIP();
    final subnet = localIP != null ? getSubnet(localIP) : null;
    final interfaces = await getLocalInterfaces();
    
    return {
      'local_ip': localIP,
      'subnet': subnet,
      'interfaces': interfaces.map((i) => {
        'name': i.name,
        'addresses': i.addresses.map((a) => a.address).toList(),
      }).toList(),
      'timestamp': DateTime.now().toIso8601String(),
    };
  }
  
  /// Format bytes to human readable
  static String formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
  
  /// Format uptime to human readable
  static String formatUptime(int seconds) {
    final days = seconds ~/ 86400;
    final hours = (seconds % 86400) ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final secs = seconds % 60;
    
    if (days > 0) {
      return '${days}d ${hours}h ${minutes}m';
    } else if (hours > 0) {
      return '${hours}h ${minutes}m ${secs}s';
    } else if (minutes > 0) {
      return '${minutes}m ${secs}s';
    } else {
      return '${secs}s';
    }
  }
  
  /// Validate MAC address format
  static bool isValidMACAddress(String mac) {
    final regex = RegExp(r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$');
    return regex.hasMatch(mac);
  }
  
  /// Format MAC address
  static String formatMACAddress(String mac) {
    if (!isValidMACAddress(mac)) return mac;
    
    return mac.toUpperCase().replaceAll('-', ':');
  }
}
