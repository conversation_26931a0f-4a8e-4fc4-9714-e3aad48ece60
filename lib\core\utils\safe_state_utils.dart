import 'package:flutter/widgets.dart';

/// Utility functions để handle setState safely
class SafeStateUtils {
  /// Safe setState với WidgetsBinding.instance.addPostFrameCallback
  static void safeSetState(State state, VoidCallback fn) {
    if (state.mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (state.mounted) {
          try {
            state.setState(fn);
          } catch (e) {
            debugPrint('❌ Error in safeSetState: $e');
          }
        }
      });
    }
  }

  /// Immediate setState với check mounted
  static void immediateSetState(State state, VoidCallback fn) {
    if (state.mounted) {
      try {
        state.setState(fn);
      } catch (e) {
        debugPrint('❌ Error in immediateSetState: $e');
        // Fallback to safe setState
        safeSetState(state, fn);
      }
    }
  }
}

/// Extension để dễ sử dụng
extension SafeStateExtension on State {
  /// Safe setState với WidgetsBinding.instance.addPostFrameCallback
  void safeSetState(VoidCallback fn) {
    SafeStateUtils.safeSetState(this, fn);
  }

  /// Immediate setState với check mounted
  void immediateSetState(VoidCallback fn) {
    SafeStateUtils.immediateSetState(this, fn);
  }
}
