import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';
import '../../features/auth/view_models/auth_view_model.dart';
import '../../features/home/<USER>/home_view_model.dart';
import '../../features/user/view_models/user_view_model.dart';
import '../../features/permission/view_models/permission_view_model.dart';
import '../../features/device/view_models/device_view_model.dart';
import '../../features/automation/view_models/automation_view_model.dart';
import '../repositories/auth_repository.dart';
import '../repositories/home_repository.dart';
import '../repositories/permission_repository.dart';
import '../repositories/device_repository.dart';
import '../repositories/automation_repository.dart';

/// Service Locator cho ViewModels
/// Quản lý việc tạo và cung cấp ViewModels cho toàn bộ app
class ViewModelLocator {
  static ViewModelLocator? _instance;
  static ViewModelLocator get instance => _instance ??= ViewModelLocator._();
  
  ViewModelLocator._();

  // Repositories
  final AuthRepository _authRepository = AuthRepository();
  final HomeRepository _homeRepository = HomeRepository();
  final PermissionRepository _permissionRepository = PermissionRepository();
  final DeviceRepository _deviceRepository = DeviceRepository();
  final AutomationRepository _automationRepository = AutomationRepository();

  /// Tạo danh sách providers cho MultiProvider
  List<SingleChildWidget> get providers => [
    // Repositories
    Provider<AuthRepository>.value(value: _authRepository),
    Provider<HomeRepository>.value(value: _homeRepository),
    Provider<PermissionRepository>.value(value: _permissionRepository),
    Provider<DeviceRepository>.value(value: _deviceRepository),
    Provider<AutomationRepository>.value(value: _automationRepository),

    // ViewModels
    ChangeNotifierProvider<AuthViewModel>(
      create: (context) => AuthViewModel(
        authRepository: context.read<AuthRepository>(),
      ),
    ),

    ChangeNotifierProvider<HomeViewModel>(
      create: (context) => HomeViewModel(
        homeRepository: context.read<HomeRepository>(),
      ),
    ),

    ChangeNotifierProvider<UserViewModel>(
      create: (context) => UserViewModel(
        authRepository: context.read<AuthRepository>(),
      ),
    ),

    ChangeNotifierProvider<PermissionViewModel>(
      create: (context) => PermissionViewModel(
        permissionRepository: context.read<PermissionRepository>(),
      ),
    ),

    ChangeNotifierProvider<DeviceViewModel>(
      create: (context) => DeviceViewModel(
        deviceRepository: context.read<DeviceRepository>(),
      ),
    ),

    ChangeNotifierProvider<AutomationViewModel>(
      create: (context) => AutomationViewModel(),
    ),
  ];

  /// Dispose tất cả resources
  void dispose() {
    // Dispose repositories nếu cần
  }
}

/// Extension để dễ dàng access ViewModels từ BuildContext
extension ViewModelContext on BuildContext {
  T viewModel<T>() => Provider.of<T>(this, listen: false);
  T watchViewModel<T>() => Provider.of<T>(this, listen: true);
}
