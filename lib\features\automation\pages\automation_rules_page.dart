import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/models/automation_models.dart';
import '../view_models/automation_view_model.dart';
import 'automation_rule_card.dart';
import 'create_automation_rule_page.dart';

class AutomationRulesPage extends StatefulWidget {
  final int homeId;

  const AutomationRulesPage({
    super.key,
    required this.homeId,
  });

  @override
  State<AutomationRulesPage> createState() => _AutomationRulesPageState();
}

class _AutomationRulesPageState extends State<AutomationRulesPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  AutomationViewModel? _viewModel;

  final List<Map<String, dynamic>> _filterTabs = [
    {'key': 'all', 'label': 'Tất cả', 'icon': Icons.list},
    {'key': 'active', 'label': 'Hoạt động', 'icon': Icons.play_circle, 'color': AppTheme.successGreen},
    {'key': 'inactive', 'label': 'Tạm dừng', 'icon': Icons.pause_circle, 'color': AppTheme.errorRed},
    {'key': 'schedule', 'label': 'Lịch trình', 'icon': Icons.schedule, 'color': AppTheme.infoBlue},
    {'key': 'condition', 'label': 'Điều kiện', 'icon': Icons.sensors, 'color': AppTheme.warningOrange},
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _filterTabs.length, vsync: this);

    // Initialize view model with home ID
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _viewModel = Provider.of<AutomationViewModel>(context, listen: false);
      _viewModel!.addListener(_handleErrorChanges);

      if (widget.homeId > 0) {
        _viewModel!.setCurrentHomeId(widget.homeId);
        _viewModel!.loadAutomationRules();
      } else {
        // Auto-load homeId if not provided or invalid
        _viewModel!.autoLoadHomeId().then((_) {
          _viewModel!.loadAutomationRules();
        });
      }
    });
  }

  void _handleErrorChanges() {
    if (_viewModel?.errorMessage != null && mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_viewModel!.errorMessage!),
              backgroundColor: Colors.red,
              action: SnackBarAction(
                label: 'Đóng',
                textColor: Colors.white,
                onPressed: () {
                  _viewModel!.clearError();
                },
              ),
            ),
          );
        }
      });
    }
  }

  @override
  void dispose() {
    _viewModel?.removeListener(_handleErrorChanges);
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AutomationViewModel>(
      builder: (context, viewModel, child) => Column(
        children: [
          // Filter tabs
          Container(
            color: AppTheme.primaryBlue,
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              indicatorColor: AppTheme.surfaceWhite,
              labelColor: AppTheme.surfaceWhite,
              unselectedLabelColor: AppTheme.surfaceWhite.withValues(alpha: 0.7),
              onTap: (index) {
                viewModel.setFilter(_filterTabs[index]['key']);
              },
              tabs: _filterTabs.map((tab) {
                final count = viewModel.getRulesCountByType(tab['key']);
                return Tab(
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        tab['icon'],
                        size: 16,
                        color: tab['color'] ?? AppTheme.surfaceWhite,
                      ),
                      const SizedBox(width: 4),
                      Text(tab['label']),
                      if (count > 0) ...[
                        const SizedBox(width: 4),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppTheme.surfaceWhite,
                            borderRadius: BorderRadius.circular(10),
                          ),
                          child: Text(
                            count.toString(),
                            style: const TextStyle(
                              color: AppTheme.primaryBlue,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                );
              }).toList(),
            ),
          ),

          // Body content
          Expanded(
            child: RefreshIndicator(
              onRefresh: () => viewModel.refresh(),
              child: _buildBody(viewModel),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody(AutomationViewModel viewModel) {
    if (viewModel.isLoading && viewModel.filteredRules.isEmpty) {
      return const Center(
        child: CircularProgressIndicator(
          valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryBlue),
        ),
      );
    }

    if (viewModel.filteredRules.isEmpty) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: viewModel.filteredRules.length,
      itemBuilder: (context, index) {
        final rule = viewModel.filteredRules[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: AutomationRuleCard(
            rule: rule,
            onTap: () => _showRuleDetails(context, rule),
            onToggle: (isActive) => viewModel.toggleRuleStatus(rule.ruleId!, isActive),
            onEdit: () => _navigateToEditRule(context, rule),
            onDelete: () => _showDeleteConfirmation(context, viewModel, rule),
            onExecute: () => viewModel.executeRule(rule.ruleId!),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.settings_input_component,
            size: 80,
            color: AppTheme.textSecondary.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'Chưa có automation rules',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: AppTheme.textSecondary.withValues(alpha: 0.8),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Tạo automation rule đầu tiên để tự động hóa thiết bị thông minh',
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 14,
              color: AppTheme.textSecondary.withValues(alpha: 0.6),
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () => _navigateToCreateRule(context),
            icon: const Icon(Icons.add),
            label: const Text('Tạo Rule Mới'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryBlue,
              foregroundColor: AppTheme.surfaceWhite,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            ),
          ),
        ],
      ),
    );
  }

  void _navigateToCreateRule(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateAutomationRulePage(homeId: widget.homeId),
      ),
    ).then((_) {
      // Refresh rules list after creating new rule
      if (mounted && _viewModel != null) {
        _viewModel!.loadAutomationRules();
      }
    });
  }

  void _navigateToEditRule(BuildContext context, AutomationRule rule) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CreateAutomationRulePage(
          homeId: widget.homeId,
          editingRule: rule,
        ),
      ),
    ).then((_) {
      // Refresh rules list after editing
      if (mounted && _viewModel != null) {
        _viewModel!.loadAutomationRules();
      }
    });
  }

  void _showRuleDetails(BuildContext context, AutomationRule rule) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildRuleDetailsSheet(rule),
    );
  }

  Widget _buildRuleDetailsSheet(AutomationRule rule) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: AppTheme.surfaceWhite,
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle bar
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: AppTheme.dividerGrey,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    rule.name,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: rule.isActive ? AppTheme.successGreen : AppTheme.errorRed,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    rule.isActive ? 'Hoạt động' : 'Tạm dừng',
                    style: const TextStyle(
                      color: AppTheme.surfaceWhite,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          
          const SizedBox(height: 16),
          
          // Content
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (rule.description.isNotEmpty) ...[
                    const Text(
                      'Mô tả:',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      rule.description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textSecondary,
                      ),
                    ),
                    const SizedBox(height: 20),
                  ],
                  
                  // Rule type and details
                  _buildRuleTypeDetails(rule),
                  
                  const SizedBox(height: 20),
                  
                  // Actions
                  const Text(
                    'Hành động:',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...rule.actions.map((action) => _buildActionItem(action)),
                  
                  const SizedBox(height: 20),
                  
                  // Execution info
                  if (rule.lastExecuted != null || rule.executionCount != null)
                    _buildExecutionInfo(rule),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRuleTypeDetails(AutomationRule rule) {
    if (rule.ruleType == AutomationRuleType.schedule && rule.schedule != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Lịch trình:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Thời gian: ${rule.schedule!.time}',
            style: const TextStyle(fontSize: 14, color: AppTheme.textSecondary),
          ),
          const SizedBox(height: 4),
          Text(
            'Ngày: ${rule.schedule!.days.map((day) => AutomationDays.getShortDisplayName(day)).join(', ')}',
            style: const TextStyle(fontSize: 14, color: AppTheme.textSecondary),
          ),
        ],
      );
    } else if (rule.ruleType == AutomationRuleType.condition && rule.conditions != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Điều kiện:',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: AppTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          ...rule.conditions!.map((condition) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Text(
              'Device ${condition.deviceId} ${AutomationOperator.getDisplayName(condition.operator)} ${condition.value}',
              style: const TextStyle(fontSize: 14, color: AppTheme.textSecondary),
            ),
          )),
          if (rule.conditionLogic != null) ...[
            const SizedBox(height: 4),
            Text(
              'Logic: ${rule.conditionLogic}',
              style: const TextStyle(fontSize: 14, color: AppTheme.textSecondary),
            ),
          ],
        ],
      );
    }
    return const SizedBox.shrink();
  }

  Widget _buildActionItem(AutomationAction action) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          
          Expanded(
            child: Text(
              '${action.deviceName ?? 'Device ${action.deviceId}'}: ${action.command}${action.value.isNotEmpty ? ' (${action.value})' : ''}',
              style: const TextStyle(fontSize: 14, color: AppTheme.textSecondary),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExecutionInfo(AutomationRule rule) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Thông tin thực thi:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        if (rule.lastExecuted != null)
          Text(
            'Lần cuối: ${_formatDateTime(rule.lastExecuted!)}',
            style: const TextStyle(fontSize: 14, color: AppTheme.textSecondary),
          ),
        if (rule.executionCount != null)
          Text(
            'Số lần thực thi: ${rule.executionCount}',
            style: const TextStyle(fontSize: 14, color: AppTheme.textSecondary),
          ),
      ],
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _showDeleteConfirmation(BuildContext context, AutomationViewModel viewModel, AutomationRule rule) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận xóa'),
        content: Text('Bạn có chắc chắn muốn xóa automation rule "${rule.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              viewModel.deleteAutomationRule(rule.ruleId!);
            },
            style: TextButton.styleFrom(foregroundColor: AppTheme.errorRed),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );
  }
}