import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/models/automation_models.dart';
import '../../../core/utils/safe_state_utils.dart';
import '../view_models/automation_view_model.dart';
import 'rule_type_selection_step.dart';
import 'schedule_configuration_step.dart';
import '../pages/if_then_rule_create_page.dart';
import '../pages/sensor_automation_create_page.dart';

import 'actions_configuration_step.dart';

class CreateAutomationRulePage extends StatefulWidget {
  final int homeId;
  final AutomationRule? editingRule;

  const CreateAutomationRulePage({
    super.key,
    required this.homeId,
    this.editingRule,
  });

  @override
  State<CreateAutomationRulePage> createState() => _CreateAutomationRulePageState();
}

class _CreateAutomationRulePageState extends State<CreateAutomationRulePage> {
  final PageController _pageController = PageController();
  int _currentStep = 0;

  // Lưu reference để tránh lỗi context
  late ScaffoldMessengerState _scaffoldMessenger;
  late NavigatorState _navigator;

  // Form data
  String _ruleName = '';
  String _ruleDescription = '';
  String _ruleType = '';
  AutomationSchedule? _schedule;
  List<AutomationCondition> _conditions = [];
  String _conditionLogic = AutomationConditionLogic.and;
  List<AutomationAction> _actions = [];

  final List<String> _stepTitles = [
    '1. Chọn loại tự động hóa',
    '2. Thiết lập điều kiện',
    '3. Chọn hành động',
  ];

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Lưu reference an toàn
    _scaffoldMessenger = ScaffoldMessenger.of(context);
    _navigator = Navigator.of(context);
  }

  @override
  void initState() {
    super.initState();

    // If editing, populate form with existing data
    if (widget.editingRule != null) {
      final rule = widget.editingRule!;
      _ruleName = rule.name;
      _ruleDescription = rule.description;
      _ruleType = rule.ruleType;
      _schedule = rule.schedule;
      _conditions = rule.conditions ?? [];
      _conditionLogic = rule.conditionLogic ?? AutomationConditionLogic.and;
      _actions = List.from(rule.actions);
    }
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundGrey,
      appBar: AppBar(
        title: Text(widget.editingRule != null ? 'Chỉnh sửa Rule' : 'Tạo Rule Mới'),
        backgroundColor: AppTheme.primaryBlue,
        foregroundColor: AppTheme.surfaceWhite,
        elevation: 0,
      ),
      body: Column(
         
        children: [
  // Progress indicator 
SizedBox(
  width: double.infinity,
  child: Center(
    child: _buildProgressIndicator(),
  ),
),
          // Step content
          Expanded(
            child: PageView(
              controller: _pageController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                // Step 1: Rule Type Selection
                RuleTypeSelectionStep(
                  selectedType: _ruleType,
                  onTypeSelected: (type) {
                    print('🔍 [DEBUG] Rule type selected: $type');
                    safeSetState(() {
                      _ruleType = type;
                      print('🔍 [DEBUG] Rule type set in state: $_ruleType');
                    });
                  },
                ),
                
                // Step 2: Configuration (Schedule or Condition)
                _buildConfigurationStep(),
                
                // Step 3: Actions Configuration
                ActionsConfigurationStep(
                  homeId: widget.homeId,
                  actions: _actions,
                  onActionsChanged: (actions) {
                    safeSetState(() {
                      _actions = actions;
                    });
                  },
                ),
              ],
            ),
          ),
          
          // Navigation buttons
          _buildNavigationButtons(),
        ],
      ),
    );
  }

 Widget _buildProgressIndicator() {
 return Container(
   padding: const EdgeInsets.all(20),
   color: AppTheme.surfaceWhite,
   alignment: Alignment.center,
   child: Column(
     mainAxisSize: MainAxisSize.min,
     children: [
       // Step indicators
       Row(
         mainAxisAlignment: MainAxisAlignment.center,
         children: List.generate(_stepTitles.length, (index) {
           final isActive = index == _currentStep;
           final isCompleted = index < _currentStep;
           
           return Expanded(
             child: Row(
               children: [
                 // Step circle
                 Container(
                   width: 32,
                   height: 32,
                   decoration: BoxDecoration(
                     color: isCompleted 
                         ? AppTheme.successGreen 
                         : isActive 
                             ? AppTheme.primaryBlue 
                             : AppTheme.dividerGrey,
                     shape: BoxShape.circle,
                   ),
                   child: Center(
                     child: isCompleted
                         ? const Icon(
                             Icons.check,
                             color: AppTheme.surfaceWhite,
                             size: 16,
                           )
                         : Text(
                             '${index + 1}',
                             style: TextStyle(
                               color: isActive 
                                   ? AppTheme.surfaceWhite 
                                   : AppTheme.textSecondary,
                               fontWeight: FontWeight.w600,
                               fontSize: 14,
                             ),
                           ),
                   ),
                 ),
                 
                 // Connector line
                 if (index < _stepTitles.length - 1)
                   Expanded(
                     child: Container(
                       height: 2,
                       margin: const EdgeInsets.symmetric(horizontal: 8),
                       color: isCompleted 
                           ? AppTheme.successGreen 
                           : AppTheme.dividerGrey,
                     ),
                   ),
               ],
             ),
           );
         }),
       ),
       
       const SizedBox(height: 12),
       
       // Step title
       Text(
         _stepTitles[_currentStep],
         style: const TextStyle(
           fontSize: 16,
           fontWeight: FontWeight.w600,
           color: AppTheme.textPrimary,
         ),
         textAlign: TextAlign.center,
       ),
     ],
   ),
 );
}
  Widget _buildConfigurationStep() {
    print('🔍 [DEBUG] Building configuration step for rule type: $_ruleType');

    if (_ruleType == AutomationRuleType.schedule) {
      print('🔍 [DEBUG] Building ScheduleConfigurationStep');
      return ScheduleConfigurationStep(
        ruleName: _ruleName,
        ruleDescription: _ruleDescription,
        schedule: _schedule,
        onNameChanged: (name) {
          safeSetState(() {
            _ruleName = name;
          });
        },
        onDescriptionChanged: (description) {
          safeSetState(() {
            _ruleDescription = description;
          });
        },
        onScheduleChanged: (schedule) {
          safeSetState(() {
            _schedule = schedule;
          });
        },
      );
    }  // Tách riêng cho từng loại rule
  // else if (_ruleType == AutomationRuleType.ifThen) {
  //   print('🔍 [DEBUG] Building IfThenRuleCreatePage');
  //   return IfThenRuleCreatePage(
  //     homeId: widget.homeId,
  //     ruleName: _ruleName,
  //     ruleDescription: _ruleDescription,
  //     conditions: _conditions,
  //     conditionLogic: _conditionLogic,
  //     onNameChanged: (name) {
  //       safeSetState(() {
  //         _ruleName = name;
  //       });
  //     },
  //     onDescriptionChanged: (description) {
  //       safeSetState(() {
  //         _ruleDescription = description;
  //       });
  //     },
  //     onConditionsChanged: (conditions) {
  //       safeSetState(() {
  //         _conditions = conditions;
  //       });
  //     },
  //     onLogicChanged: (logic) {
  //       safeSetState(() {
  //         _conditionLogic = logic;
  //       });
  //     },
  //   );
  // } 
  else if (_ruleType == AutomationRuleType.ifThen) {
  // Hiển thị snackbar sau khi build hoàn thành
  WidgetsBinding.instance.addPostFrameCallback((_) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Tính năng đang được phát triển, vui lòng chờ bản cập nhật sau!'),
          duration: Duration(seconds: 3),
        ),
      );
    }
  });
 
  return const SizedBox.shrink();
}

  else if (_ruleType == AutomationRuleType.condition) {
    print('🔍 [DEBUG] Building SensorAutomationCreatePage');
    return SensorAutomationCreatePage(
      homeId: widget.homeId,
      ruleName: _ruleName,
      ruleDescription: _ruleDescription,
      condition: _conditions.isNotEmpty ? _conditions.first : null,
      onNameChanged: (name) {
        safeSetState(() {
          _ruleName = name;
        });
      },
      onDescriptionChanged: (description) {
        safeSetState(() {
          _ruleDescription = description;
        });
      },
      onConditionChanged: (condition) {
        safeSetState(() {
          _conditions = condition != null ? [condition] : [];
        });
      },
    );
  }
    
    return const Center(
      child: Text('Vui lòng chọn loại rule trước'),
    );
  }

  Widget _buildNavigationButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      color: AppTheme.surfaceWhite,
      child: Row(
        children: [
          // Back button
          if (_currentStep > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _goToPreviousStep,
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: AppTheme.primaryBlue),
                  padding: const EdgeInsets.symmetric(vertical: 12),
                ),
                child: const Text(
                  'Quay lại',
                  style: TextStyle(color: AppTheme.primaryBlue),
                ),
              ),
            ),
          
          if (_currentStep > 0) const SizedBox(width: 16),
          
          // Next/Create button
          Expanded(
            child: ElevatedButton(
              onPressed: _canProceed() ? _handleNextStep : null,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryBlue,
                foregroundColor: AppTheme.surfaceWhite,
                padding: const EdgeInsets.symmetric(vertical: 12),
              ),
              child: Text(
                _currentStep == _stepTitles.length - 1 
                    ? (widget.editingRule != null ? 'Cập nhật' : 'Tạo Rule')
                    : 'Tiếp theo',
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool _canProceed() {
    switch (_currentStep) {
      case 0: // Rule type selection
        return _ruleType.isNotEmpty;
      case 1: // Configuration
        if (_ruleType == AutomationRuleType.schedule) {
          return _ruleName.isNotEmpty && _schedule != null;
        } else if (_ruleType == AutomationRuleType.condition || _ruleType == AutomationRuleType.ifThen) {
          return _ruleName.isNotEmpty && _conditions.isNotEmpty;
        }
        return false;
      case 2: // Actions
        return _actions.isNotEmpty;
      default:
        return false;
    }
  }

  void _goToPreviousStep() {
    if (_currentStep > 0) {
      safeSetState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  bool _validateRule() {
    // Validate rule name
    if (_ruleName.trim().isEmpty) {
      _showError("Vui lòng nhập tên quy tắc");
      return false;
    }

    // Validate rule type
    if (_ruleType.isEmpty) {
      _showError("Vui lòng chọn loại quy tắc");
      return false;
    }

    // Validate schedule rules
    if (_ruleType == AutomationRuleType.schedule && _schedule == null) {
      _showError("Vui lòng cấu hình lịch trình");
      return false;
    }

    // Validate condition rules
    if ((_ruleType == AutomationRuleType.condition || _ruleType == AutomationRuleType.ifThen)) {
      if (_conditions.isEmpty) {
        _showError("Vui lòng thêm ít nhất một điều kiện");
        return false;
      }

      // Check if same device is used in both condition and action
      for (var condition in _conditions) {
        for (var action in _actions) {
          if (condition.deviceId == action.deviceId) {
            _showError("Không thể dùng cùng thiết bị cho cả điều kiện và hành động!\n\n"
                "Gợi ý: Dùng cảm biến để điều khiển thiết bị khác");
            return false;
          }
        }
      }
    }

    // Validate actions
    if (_actions.isEmpty) {
      _showError("Vui lòng thêm ít nhất một hành động");
      return false;
    }

    return true;
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
          duration: const Duration(seconds: 4),
        ),
      );
    }
  }

  void _handleNextStep() {
    if (_currentStep < _stepTitles.length - 1) {
      safeSetState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    } else {
      _createOrUpdateRule();
    }
  }

  void _createOrUpdateRule() async {
    // Validate rule before creating
    if (!_validateRule()) {
      return;
    }

    final viewModel = Provider.of<AutomationViewModel>(context, listen: false);

    // Map ifThen to condition for backend compatibility
    final backendRuleType = _ruleType == AutomationRuleType.ifThen
        ? AutomationRuleType.condition
        : _ruleType;

    final rule = AutomationRule(
      ruleId: widget.editingRule?.ruleId,
      name: _ruleName,
      description: _ruleDescription,
      homeId: widget.homeId,
      ruleType: backendRuleType,
      isActive: widget.editingRule?.isActive ?? true,
      schedule: _schedule,
      conditions: _conditions.isNotEmpty ? _conditions : null,
      conditionLogic: _conditions.length > 1 ? _conditionLogic : null,
      actions: _actions,
    );

    print('[DEBUG] Creating rule with:');
    print('[DEBUG] - Name: $_ruleName');
    print('[DEBUG] - Type: $backendRuleType');
    print('[DEBUG] - Conditions count: ${_conditions.length}');
    print('[DEBUG] - Actions count: ${_actions.length}');
    if (_conditions.isNotEmpty) {
      print('[DEBUG] - Conditions: ${_conditions.map((c) => c.toJson()).toList()}');
    }

    bool success = false;
    
    if (widget.editingRule != null) {
      // Update existing rule
      success = await viewModel.updateAutomationRule(rule);
    } else {
      // Create new rule
      if (_ruleType == AutomationRuleType.schedule) {
        success = await viewModel.createScheduleRule(rule);
      } else if (_ruleType == AutomationRuleType.condition || _ruleType == AutomationRuleType.ifThen) {
        success = await viewModel.createConditionRule(rule);
      }
    }

    if (success && mounted) {
      _navigator.pop();
      _showSnackBar(
        widget.editingRule != null
            ? 'Cập nhật automation rule thành công'
            : 'Tạo automation rule thành công',
        AppTheme.successGreen,
      );
    } else if (mounted) {
      _showSnackBar(
        viewModel.errorMessage ?? 'Có lỗi xảy ra',
        AppTheme.errorRed,
      );
    }
  }

  void _showSnackBar(String message, Color backgroundColor) {
    if (mounted) {
      _scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
        ),
      );
    }
  }
}
