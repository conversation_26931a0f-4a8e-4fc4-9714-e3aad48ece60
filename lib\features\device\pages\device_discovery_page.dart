import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/models/esp32_device_model.dart';
import '../../../core/repositories/device_repository.dart';
import '../../home/<USER>/home_view_model.dart';

class DeviceDiscoveryPage extends StatefulWidget {
  const DeviceDiscoveryPage({super.key});

  @override
  State<DeviceDiscoveryPage> createState() => _DeviceDiscoveryPageState();
}

enum DiscoveryType { wifi, bluetooth }

class _DeviceDiscoveryPageState extends State<DeviceDiscoveryPage> {
  List<ESP32Device> _esp32Devices = [];
  bool _isLanScanning = false;
  String _scanProgress = '';
  DiscoveryType _selectedDiscoveryType = DiscoveryType.wifi;

  // Lưu reference để tránh lỗi context
  late ScaffoldMessengerState _scaffoldMessenger;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Lưu reference an toàn
    _scaffoldMessenger = ScaffoldMessenger.of(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('Quét thiết bị ',style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.black87,
        elevation: 1,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            // Discovery Type Selection
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.05),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Chọn phương thức quét',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildDiscoveryTypeCard(
                          DiscoveryType.wifi,
                          'WiFi',
                          Icons.wifi,
                          'Quét thiết bị qua mạng WiFi',
                          Colors.blue,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildDiscoveryTypeCard(
                          DiscoveryType.bluetooth,
                          'Bluetooth',
                          Icons.bluetooth,
                          'Quét thiết bị Bluetooth',
                          Colors.indigo,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Header Section
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey[200]!),
              ),
              child: Column(
                children: [
                  Icon(
                    _isLanScanning ? Icons.wifi_find : Icons.devices_other,
                    color: Colors.grey[600],
                    size: 32,
                  ),
                  const SizedBox(height: 12),
                  Text(
                    _getHeaderTitle(),
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _getHeaderDescription(),
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[600],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: _getStatusColor()[50],
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: _getStatusColor()[200]!),
                    ),
                    child: Text(
                      _getStatusText(),
                      style: TextStyle(
                        fontSize: 12,
                        color: _getStatusColor()[700],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),

                  if (!_isLanScanning) ...[
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _selectedDiscoveryType == DiscoveryType.wifi
                            ? _startLanScanning
                            : _showBluetoothComingSoon,
                        icon: Icon(_selectedDiscoveryType == DiscoveryType.wifi
                            ? Icons.search
                            : Icons.bluetooth_searching),
                        label: Text(_selectedDiscoveryType == DiscoveryType.wifi
                            ? 'Quét WiFi'
                            : 'Quét Bluetooth'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _selectedDiscoveryType == DiscoveryType.wifi
                              ? Colors.blue[600]
                              : Colors.indigo[600],
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                  ] else ...[
                    Column(
                      children: [
                        const LinearProgressIndicator(),
                        const SizedBox(height: 12),
                        if (_scanProgress.isNotEmpty)
                          Text(
                            _scanProgress,
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                            textAlign: TextAlign.center,
                          ),
                      ],
                    ),
                  ],
                ],
              ),
            ),

            const SizedBox(height: 16),

            // Devices List
            Expanded(
              child: _buildESP32DevicesList(),
            ),
          ],
        ),
      ),
    );
  }

  // Helper methods for dynamic content
  String _getHeaderTitle() {
    if (_isLanScanning) {
      return _selectedDiscoveryType == DiscoveryType.wifi
          ? 'Đang quét WiFi...'
          : 'Đang quét Bluetooth...';
    }
    return _selectedDiscoveryType == DiscoveryType.wifi
        ? 'Quét thiết bị WiFi'
        : 'Quét thiết bị Bluetooth';
  }

  String _getHeaderDescription() {
    if (_isLanScanning) {
      return _selectedDiscoveryType == DiscoveryType.wifi
          ? 'Đang lắng nghe tín hiệu UDP từ thiết bị WiFi'
          : 'Đang tìm kiếm thiết bị Bluetooth';
    }
    return _selectedDiscoveryType == DiscoveryType.wifi
        ? 'Quét các thiết bị phát tín hiệu UDP qua WiFi'
        : 'Tìm kiếm thiết bị Bluetooth gần đây';
  }

  String _getStatusText() {
    return _selectedDiscoveryType == DiscoveryType.wifi
        ? 'Chỉ quét thiết bị UDP'
        : 'Bluetooth Low Energy';
  }

  MaterialColor _getStatusColor() {
    return _selectedDiscoveryType == DiscoveryType.wifi
        ? Colors.blue
        : Colors.indigo;
  }

  Widget _buildDiscoveryTypeCard(
    DiscoveryType type,
    String title,
    IconData icon,
    String description,
    MaterialColor color,
  ) {
    final isSelected = _selectedDiscoveryType == type;

    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedDiscoveryType = type;
          _esp32Devices.clear(); // Clear previous results
        });
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? color[50] : Colors.grey[50],
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected ? color[300]! : Colors.grey[200]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          children: [
            Icon(
              icon,
              color: isSelected ? color[600] : Colors.grey[500],
              size: 24,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isSelected ? color[700] : Colors.grey[700],
              ),
            ),
            const SizedBox(height: 4),
            Text(
              description,
              style: TextStyle(
                fontSize: 11,
                color: isSelected ? color[600] : Colors.grey[500],
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  void _showBluetoothComingSoon() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(
                Icons.bluetooth,
                color: Colors.indigo[600],
                size: 28,
              ),
              const SizedBox(width: 12),
              const Text(
                'Bluetooth Discovery',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.indigo[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.indigo[200]!),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.construction,
                      color: Colors.indigo[600],
                      size: 24,
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Tính năng đang phát triển',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Colors.indigo[700],
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Chúng tôi đang phát triển tính năng quét thiết bị Bluetooth. Vui lòng sử dụng WiFi Discovery trong thời gian này.',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.indigo[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                'Đóng',
                style: TextStyle(
                  color: Colors.indigo[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
                setState(() {
                  _selectedDiscoveryType = DiscoveryType.wifi;
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.indigo[600],
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Chuyển sang WiFi'),
            ),
          ],
        );
      },
    );
  }

  Future<void> _startLanScanning() async {
    setState(() {
      _isLanScanning = true;
      _esp32Devices.clear();
      _scanProgress = 'Đang khởi tạo quét UDP...';
    });

    final deviceRepository = DeviceRepository();

    try {
      // Lấy homeViewModel trước khi async
      final homeViewModel = context.read<HomeViewModel>();
      final currentHome = homeViewModel.currentHome;

      // Lấy danh sách unique_identifier của thiết bị đã có trong nhà
      List<String>? existingUniqueIds;
      try {
        setState(() {
          _scanProgress = 'Đang lấy danh sách thiết bị hiện có...';
        });

        final prefs = await SharedPreferences.getInstance();
        final token = prefs.getString('auth_token');

        if (token != null && currentHome != null) {
          final existingResult = await deviceRepository.getExistingDeviceUniqueIdentifiers(currentHome.homeId, token);
          if (existingResult.success && existingResult.data != null) {
            existingUniqueIds = existingResult.data!;
            print('Found ${existingUniqueIds.length} existing devices in home ${currentHome.homeId}');
          }
        }
      } catch (e) {
        print('Error getting existing devices: $e');
      }

      final result = await deviceRepository.scanLANForESP32UDPOnly(
        timeout: 15000,
        existingUniqueIdentifiers: existingUniqueIds,
        onProgress: (progress) {
          if (mounted) {
            setState(() {
              _scanProgress = progress;
            });
          }
        },
      );

      if (result.success && result.data != null) {
        setState(() {
          _esp32Devices = result.data!.esp32Devices;
          _scanProgress = '';
        });

        if (mounted) {
          final message = existingUniqueIds != null && existingUniqueIds.isNotEmpty
              ? 'Tìm thấy ${_esp32Devices.length} thiết bị mới (đã lọc thiết bị trùng lặp)'
              : 'Tìm thấy ${_esp32Devices.length} thiết bị UDP trong mạng';
          _showSnackBar(message, Colors.green);
        }
      } else {
        if (mounted) {
          _showSnackBar(
            result.message,
            Colors.orange,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar(
          'Lỗi khi quét LAN: $e',
          Colors.red,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLanScanning = false;
          _scanProgress = '';
        });
      }
    }
  }







  Widget _buildESP32DevicesList() {
    if (_esp32Devices.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _selectedDiscoveryType == DiscoveryType.wifi
                  ? Icons.wifi_off
                  : Icons.bluetooth_disabled,
              size: 48,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _selectedDiscoveryType == DiscoveryType.wifi
                  ? 'Chưa tìm thấy thiết bị WiFi nào'
                  : 'Chưa tìm thấy thiết bị Bluetooth nào',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _selectedDiscoveryType == DiscoveryType.wifi
                  ? 'Nhấn "Quét WiFi" để tìm thiết bị phát tín hiệu UDP'
                  : 'Nhấn "Quét Bluetooth" để tìm thiết bị Bluetooth gần đây',
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    return ListView.separated(
      itemCount: _esp32Devices.length,
      separatorBuilder: (context, index) => const SizedBox(height: 12),
      itemBuilder: (context, index) {
        final device = _esp32Devices[index];
        return _buildESP32DeviceCard(device);
      },
    );
  }

  Widget _buildESP32DeviceCard(ESP32Device device) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.memory,
                color: Colors.grey[600],
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  device.deviceName,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                decoration: BoxDecoration(
                  color: device.isOnline ? Colors.green[50] : Colors.red[50],
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: device.isOnline ? Colors.green[200]! : Colors.red[200]!,
                  ),
                ),
                child: Text(
                  device.isOnline ? 'Online' : 'Offline',
                  style: TextStyle(
                    color: device.isOnline ? Colors.green[700] : Colors.red[700],
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Device info
          Text(
            device.fullAddress,
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),

          if (device.deviceType.isNotEmpty) ...[
            const SizedBox(height: 4),
            Text(
              device.deviceType,
              style: TextStyle(
                color: Colors.grey[500],
                fontSize: 12,
              ),
            ),
          ],

          const SizedBox(height: 12),

          // Action button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: () => _registerESP32Device(device),
              icon: const Icon(Icons.add, size: 18),
              label: const Text('Thêm thiết bị'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue[600],
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 10),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _registerESP32Device(ESP32Device esp32Device) async {
    final deviceRepository = DeviceRepository();
    final homeViewModel = Provider.of<HomeViewModel>(context, listen: false);

    try {
      // Get token from SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token') ?? '';

      if (token.isEmpty) {
        throw Exception('Token không tồn tại');
      }

      // Get homeId from HomeViewModel - mỗi user chỉ có 1 nhà
      final homes = homeViewModel.homes;
      if (homes.isEmpty) {
        throw Exception('Không tìm thấy thông tin nhà. Vui lòng tạo nhà trước.');
      }

      // Lấy nhà đầu tiên (vì mỗi user chỉ có 1 nhà)
      final userHome = homes.first;

      final result = await deviceRepository.registerESP32Device(
        token,
        esp32Device,
        homeId: userHome.homeId,
      );

      if (mounted) {
        if (result.success) {
          _showSnackBar(
            'Đăng ký "${esp32Device.deviceName}" thành công!',
            Colors.green,
          );

          // Remove from ESP32 list
          setState(() {
            _esp32Devices.remove(esp32Device);
          });
        } else {
          _showSnackBar(
            result.message,
            Colors.orange,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        _showSnackBar(
          'Lỗi khi đăng ký ESP32: $e',
          Colors.red,
        );
      }
    }
  }

  void _showSnackBar(String message, Color backgroundColor) {
    if (mounted) {
      _scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }
}
