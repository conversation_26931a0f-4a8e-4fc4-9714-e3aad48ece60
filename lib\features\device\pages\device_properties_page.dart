import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view_models/device_view_model.dart';
import '../../../core/models/device_model.dart';
import '../../../core/models/home_model.dart';

class DevicePropertiesPage extends StatefulWidget {
  final Home home;
  final Device device;

  const DevicePropertiesPage({
    super.key,
    required this.home,
    required this.device,
  });

  @override
  State<DevicePropertiesPage> createState() => _DevicePropertiesPageState();
}

class _DevicePropertiesPageState extends State<DevicePropertiesPage> {
  Map<String, dynamic> _properties = {};
  List<Map<String, dynamic>> _commands = [];
  bool _isLoading = true;


  @override
  void initState() {
    super.initState();
    _loadDeviceData();
  }

  Future<void> _loadDeviceData() async {
    setState(() => _isLoading = true);

    final viewModel = context.read<DeviceViewModel>();
    
    final futures = await Future.wait([
      viewModel.getDeviceProperties(
        homeId: widget.home.id,
        deviceId: widget.device.id,
      ),
      viewModel.getDeviceCommands(
        homeId: widget.home.id,
        deviceId: widget.device.id,
      ),
    ]);

    if (mounted) {
      setState(() {
        _properties = futures[0] as Map<String, dynamic>? ?? {};
        _commands = futures[1] as List<Map<String, dynamic>>? ?? [];
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Thuộc tính - ${widget.device.name}'),
        backgroundColor: Colors.indigo[600],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDeviceData,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : RefreshIndicator(
              onRefresh: _loadDeviceData,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildDeviceInfo(),
                    const SizedBox(height: 16),
                    _buildPropertiesSection(),
                    const SizedBox(height: 16),
                    _buildCommandsSection(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildDeviceInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Thông tin thiết bị',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildInfoRow('Tên thiết bị', widget.device.name),
            _buildInfoRow('ID thiết bị', widget.device.id.toString()),
            _buildInfoRow('Loại thiết bị', widget.device.type ?? 'Unknown'),
            _buildInfoRow('Trạng thái', widget.device.status ?? 'Unknown'),
            _buildInfoRow('IP Address', widget.device.ipAddress ?? 'N/A'),
            _buildInfoRow('Mô tả', widget.device.description ?? 'Không có mô tả'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPropertiesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Thuộc tính thiết bị',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (_properties.isEmpty)
              Center(
                child: Text(
                  'Không có thuộc tính nào',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              )
            else
              ..._properties.entries.map((entry) => _buildPropertyTile(entry)),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyTile(MapEntry<String, dynamic> property) {
    final key = property.key;
    final value = property.value;
    final isEditable = _isPropertyEditable(key);

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              _getPropertyDisplayName(key),
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 14,
              ),
            ),
          ),
          Expanded(
            flex: 3,
            child: Text(
              value.toString(),
              style: TextStyle(
                color: Colors.grey[700],
                fontSize: 14,
              ),
            ),
          ),
          if (isEditable)
            IconButton(
              icon: const Icon(Icons.edit, size: 20),
              onPressed: () => _editProperty(key, value),
            ),
        ],
      ),
    );
  }

  Widget _buildCommandsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Lệnh điều khiển',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (_commands.isEmpty)
              Center(
                child: Text(
                  'Không có lệnh nào',
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
              )
            else
              Wrap(
                spacing: 8,
                runSpacing: 8,
                children: _commands.map((command) => _buildCommandChip(command)).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildCommandChip(Map<String, dynamic> command) {
    final name = command['name'] ?? 'Unknown';
    final description = command['description'] ?? '';

    return ActionChip(
      label: Text(name),
      onPressed: () => _executeCommand(command),
      backgroundColor: Colors.blue[50],
      labelStyle: TextStyle(color: Colors.blue[700]),
      tooltip: description.isNotEmpty ? description : null,
    );
  }

  bool _isPropertyEditable(String key) {
    // Define which properties can be edited
    const editableProperties = [
      'brightness',
      'temperature',
      'speed',
      'volume',
      'name',
      'description',
    ];
    return editableProperties.contains(key.toLowerCase());
  }

  String _getPropertyDisplayName(String key) {
    switch (key.toLowerCase()) {
      case 'brightness':
        return 'Độ sáng';
      case 'temperature':
        return 'Nhiệt độ';
      case 'speed':
        return 'Tốc độ';
      case 'volume':
        return 'Âm lượng';
      case 'name':
        return 'Tên';
      case 'description':
        return 'Mô tả';
      case 'status':
        return 'Trạng thái';
      case 'power':
        return 'Nguồn';
      default:
        return key;
    }
  }

  void _editProperty(String key, dynamic currentValue) {
    showDialog(
      context: context,
      builder: (context) => _EditPropertyDialog(
        propertyName: _getPropertyDisplayName(key),
        currentValue: currentValue,
        onSave: (newValue) => _updateProperty(key, newValue),
      ),
    );
  }

  Future<void> _updateProperty(String key, dynamic value) async {
    setState(() => _isLoading = true);

    final viewModel = context.read<DeviceViewModel>();
    final success = await viewModel.updateDeviceProperty(
      homeId: widget.home.id,
      deviceId: widget.device.id,
      property: key,
      value: value,
    );

    setState(() => _isLoading = false);

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cập nhật thuộc tính thành công'),
            backgroundColor: Colors.green,
          ),
        );
        _loadDeviceData(); // Reload to get updated values
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(viewModel.errorMessage ?? 'Cập nhật thất bại'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _executeCommand(Map<String, dynamic> command) {
    final name = command['name'] ?? '';
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Thực hiện lệnh: $name'),
        content: Text('Bạn có muốn thực hiện lệnh này không?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _performCommand(name);
            },
            child: const Text('Thực hiện'),
          ),
        ],
      ),
    );
  }

  Future<void> _performCommand(String command) async {
    final viewModel = context.read<DeviceViewModel>();
    final success = await viewModel.controlDevice(
      homeId: widget.home.id,
      deviceId: widget.device.id,
      command: command,
      value: '',
    );

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Thực hiện lệnh "$command" thành công'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(viewModel.errorMessage ?? 'Thực hiện lệnh thất bại'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

class _EditPropertyDialog extends StatefulWidget {
  final String propertyName;
  final dynamic currentValue;
  final Function(dynamic) onSave;

  const _EditPropertyDialog({
    required this.propertyName,
    required this.currentValue,
    required this.onSave,
  });

  @override
  State<_EditPropertyDialog> createState() => _EditPropertyDialogState();
}

class _EditPropertyDialogState extends State<_EditPropertyDialog> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.currentValue.toString());
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Chỉnh sửa ${widget.propertyName}'),
      content: TextField(
        controller: _controller,
        decoration: InputDecoration(
          labelText: widget.propertyName,
          border: const OutlineInputBorder(),
        ),
        keyboardType: _getKeyboardType(),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Hủy'),
        ),
        ElevatedButton(
          onPressed: () {
            final value = _parseValue(_controller.text);
            Navigator.of(context).pop();
            widget.onSave(value);
          },
          child: const Text('Lưu'),
        ),
      ],
    );
  }

  TextInputType _getKeyboardType() {
    if (widget.currentValue is num) {
      return TextInputType.number;
    }
    return TextInputType.text;
  }

  dynamic _parseValue(String text) {
    if (widget.currentValue is int) {
      return int.tryParse(text) ?? widget.currentValue;
    } else if (widget.currentValue is double) {
      return double.tryParse(text) ?? widget.currentValue;
    } else if (widget.currentValue is bool) {
      return text.toLowerCase() == 'true';
    }
    return text;
  }
}
