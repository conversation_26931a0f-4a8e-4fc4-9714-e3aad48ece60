import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view_models/home_view_model.dart';
import '../../../core/models/home_model.dart' as home_models;
import '../../../core/services/app_lifecycle_service.dart';
import 'home_members_page.dart';
import 'edit_home_page.dart';
import 'home_detail_page.dart';
import 'home_devices_page.dart';
import 'home_areas_page.dart';
import '../../permission/pages/permission_management_page.dart';
import '../../device/pages/device_discovery_page.dart';
import '../../device/pages/device_types_management_page.dart';



class DashboardPage extends StatefulWidget {
  const DashboardPage({super.key});

  @override
  State<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends State<DashboardPage> {
  bool _isLoadingData = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        print('🏠 [DEBUG] DashboardPage initState - Loading dashboard data...');
        _loadDashboardData(forceStatsRefresh: true, forceHomesRefresh: true); // Always force load
      }
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Prevent unnecessary reloads when dependencies change
  }

  Future<void> _loadDashboardData({bool forceStatsRefresh = false, bool forceHomesRefresh = false}) async {
    if (_isLoadingData) {
      print('_loadDashboardData already in progress, skipping...');
      return;
    }

    final lifecycleService = AppLifecycleService();
    if (!lifecycleService.shouldAllowBackgroundCalls()) {
      print('Skipping dashboard data load - background operations paused');
      return;
    }

    _isLoadingData = true;
    try {
      final homeViewModel = context.read<HomeViewModel>();

      // Only load homes if needed
      bool homesLoaded = true;
      if (forceHomesRefresh || homeViewModel.homes.isEmpty) {
        print('Loading homes list...');
        homesLoaded = await homeViewModel.loadHomes();
      } else {
        print('Using cached homes list (${homeViewModel.homes.length} homes)');
      }

      // Only load detailed stats if needed and if background operations are allowed
      if (homesLoaded && homeViewModel.homes.isNotEmpty && lifecycleService.shouldAllowBackgroundCalls()) {
        final firstHome = homeViewModel.homes.first;

        // Check if we need to refresh stats
        final shouldRefreshStats = forceStatsRefresh ||
            homeViewModel.homeDetail == null;

        if (shouldRefreshStats) {
          print('Refreshing dashboard statistics...');
          await homeViewModel.loadHomeDetailWithStats(firstHome.homeId);
        } else {
          print('Using cached dashboard statistics');
        }
      }
    } finally {
      _isLoadingData = false;
    }
  }

  Future<void> _forceRefreshStats() async {
    await _loadDashboardData(forceStatsRefresh: true, forceHomesRefresh: false);
  }

  @override
  Widget build(BuildContext context) {

    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: CustomScrollView(
        slivers: [
          // Modern App Bar
          SliverAppBar(
            expandedHeight: 90,
            floating: false,
            pinned: true,
            backgroundColor: Colors.blue[600],
            automaticallyImplyLeading: false,
            flexibleSpace: FlexibleSpaceBar(
              centerTitle: true,
              title: const Text(
                'Smart Home',
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
             
            ),
            // Removed create home button - users get auto-created homes
          ),

          // Content
          SliverToBoxAdapter(
            child: _buildDashboardContent(context),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboardContent(BuildContext context) {
    return Selector<HomeViewModel, ({bool isLoading, List<home_models.Home> homes, home_models.HomeDetail? homeDetail, String? errorMessage})>(
      selector: (context, homeViewModel) => (
        isLoading: homeViewModel.isLoading,
        homes: homeViewModel.homes,
        homeDetail: homeViewModel.homeDetail,
        errorMessage: homeViewModel.errorMessage,
      ),
      builder: (context, data, child) {
        if (data.isLoading) {
          return const Padding(
            padding: EdgeInsets.all(50),
            child: Center(
              child: CircularProgressIndicator(),
            ),
          );
        } else if (data.homes.isNotEmpty) {
          // Use HomeDetail if available, otherwise fallback to Home
          if (data.homeDetail != null) {
            return _buildDashboardWithDetail(data.homeDetail!);
          } else {
            return _buildDashboard(data.homes);
          }

         
        }
        return _buildEmptyState();
      },
    );
  }

  Widget _buildDashboard(List<home_models.Home> homes) {
    final home = homes.first; // Since user has only one home

    return RefreshIndicator(
      onRefresh: () async {
        await _loadDashboardData(forceStatsRefresh: false, forceHomesRefresh: false); // Don't force refresh on pull-to-refresh
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Card with home info
            _buildWelcomeCard(home),
            const SizedBox(height: 20),

            // Quick Actions
            _buildQuickActions(home),
            const SizedBox(height: 20),

            // Home Management Section
            _buildHomeManagement(home),
          ],
        ),
      ),
    );
  }

  Widget _buildDashboardWithDetail(home_models.HomeDetail homeDetail) {
    // Convert HomeDetail to Home for compatibility with existing functions
    final home = home_models.Home(
      homeId: homeDetail.homeId,
      name: homeDetail.name,
      address: homeDetail.address,
      createdAt: homeDetail.createdAt,
      updatedAt: homeDetail.updatedAt,
      areas: homeDetail.areas,
    );

    return RefreshIndicator(
      onRefresh: () async {
        await _loadDashboardData(forceStatsRefresh: false, forceHomesRefresh: false); // Don't force refresh on pull-to-refresh
      },
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Welcome Card with home info
            _buildWelcomeCardWithDetail(homeDetail),
            const SizedBox(height: 20),

            // Quick Actions
            _buildQuickActions(home),
            const SizedBox(height: 20),

            // Home Management Section
            _buildHomeManagement(home),
          ],
        ),
      ),
    );
  }

  Widget _buildWelcomeCard(home_models.Home home) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue[600]!,
            Colors.blue[800]!,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.home_filled,
                color: Colors.yellow[300],
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  home.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            home.address,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 12),
          
        ],
      ),
    );
  }

  Widget _buildWelcomeCardWithDetail(home_models.HomeDetail homeDetail) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue[600]!,
            Colors.blue[800]!,
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.3),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.home_filled,
                color: Colors.yellow[300],
                size: 28,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  homeDetail.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            homeDetail.address,
            style: TextStyle(
              color: Colors.white.withValues(alpha: 0.9),
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 12),
         
        ],
      ),
    );
  }



  Widget _buildQuickActions(home_models.Home home) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Thao tác nhanh',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickActionCard(
                'Khám phá thiết bị',
                'Tìm và đăng ký thiết bị mới',
                Icons.radar,
                Colors.green,
                () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => const DeviceDiscoveryPage(),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickActionCard(
                'Quản lý quyền',
                'Phân quyền thiết bị và khu vực',
                Icons.security,
                Colors.orange,
                () => Navigator.of(context).push(
                  MaterialPageRoute(
                    builder: (context) => PermissionManagementPage(home: home),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 24),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionCard(String title, String subtitle, IconData icon, Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildManagementCard(String title, String subtitle, IconData icon, Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withValues(alpha: 0.1),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 28),
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            const SizedBox(height: 2),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 11,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHomeManagement(home_models.Home home) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quản lý nhà',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.0,
          children: [
            _buildManagementCard(
              'Chi tiết nhà',
              'Xem thông tin chi tiết',
              Icons.info_outline,
              Colors.green,
              () => Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => HomeDetailPage(home: home),
                ),
              ),
            ),
            _buildManagementCard(
              'Thành viên',
              'Quản lý thành viên',
              Icons.people,
              Colors.blue,
              () => Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => HomeMembersPage(home: home),
                ),
              ),
            ),
            _buildManagementCard(
              'Khu vực',
              'Quản lý khu vực',
              Icons.room,
              Colors.purple,
              () => Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => HomeAreasPage(home: home),
                ),
              ),
            ),
            _buildManagementCard(
              'Thiết bị',
              'Quản lý thiết bị',
              Icons.devices,
              Colors.orange,
              () => Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => HomeDevicesPage(home: home),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Home Settings
        Card(
          child: ListTile(
            leading: Icon(Icons.settings, color: Colors.grey[600]),
            title: const Text('Cài đặt nhà'),
            subtitle: const Text('Chỉnh sửa thông tin nhà'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () => Navigator.of(context).push(
              MaterialPageRoute(
                builder: (context) => EditHomePage(home: home),
              ),
            ).then((result) {
              if (result == true && mounted) {
                // Only refresh if actually needed, use force refresh to update stats
                _forceRefreshStats();
              }
            }),
          ),
        ),
      ],
    );
  }



  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.home_outlined,
              size: 100,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'Chưa có nhà nào',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Nhà thông minh sẽ được tạo tự động sau khi đăng nhập lần đầu',
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[500],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

 
  

}
