import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view_models/home_view_model.dart';
import '../../../core/models/home_model.dart';
import '../../../core/models/area_model.dart';
import 'area_devices_page.dart';

class HomeAreasPage extends StatefulWidget {
  final Home home;

  const HomeAreasPage({
    super.key,
    required this.home,
  });

  @override
  State<HomeAreasPage> createState() => _HomeAreasPageState();
}

final List<String> commonAreaNames = [
  'Phòng khách',
  'Phòng ngủ chính',
  'Phòng ngủ phụ',
  '<PERSON><PERSON><PERSON> bếp',
  '<PERSON>òng ăn',
  '<PERSON>òng tắm chính',
  '<PERSON>hà vệ sinh',
  
  'Phòng làm việc',
  'Phòng đọc sách',
  'Phòng học',
  
  'Phòng chơi',
  'Phòng giải trí',
  '<PERSON>òng trẻ em',
  '<PERSON>òng tập gym',
  
  '<PERSON>òng giặt',
  '<PERSON><PERSON><PERSON> kho',
  '<PERSON><PERSON>ng thay đồ',
  '<PERSON>arage',
  
  '<PERSON><PERSON><PERSON> chính',
  '<PERSON><PERSON><PERSON> sau',
  '<PERSON>ành lang',
  'Sảnh chính',
  
  'Ban công',
  'Sân vườn',
  'Sân thượng',
  'Khu vực BBQ',
  'Khu vực hồ bơi',
  
  'Phòng thờ',
  'Phòng đa năng',
  'Hầm rượu',
  'Phòng khách VIP',
];

class _HomeAreasPageState extends State<HomeAreasPage> {
  final _areaNameController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  
  // Lưu reference để tránh lỗi context
  late ScaffoldMessengerState _scaffoldMessenger;
  late NavigatorState _navigator;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final viewModel = context.read<HomeViewModel>();
      // Load both areas and home detail with statistics
      viewModel.loadHomeAreas(widget.home.id);
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Lưu reference an toàn
    _scaffoldMessenger = ScaffoldMessenger.of(context);
    _navigator = Navigator.of(context);
  }

  @override
  void dispose() {
    _areaNameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Khu vực - ${widget.home.name}'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Consumer<HomeViewModel>(
        builder: (context, viewModel, child) {
          if (viewModel.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (viewModel.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    viewModel.errorMessage!,
                    style: TextStyle(color: Colors.red[600]),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => viewModel.loadHomeAreas(widget.home.id),
                    child: const Text('Thử lại'),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              await Future.wait([
                viewModel.loadHomeAreas(widget.home.id),
                viewModel.loadHomeDetailWithStats(widget.home.id),
              ]);
            },
            child: Column(
              children: [
                // Add Area Section
                Container(
                  padding: const EdgeInsets.all(16),
                  color: Colors.grey[50],
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Tạo khu vực mới',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 12),
                        Row(
                          children: [
                            Expanded(
                              child: Autocomplete<String>(
                                optionsBuilder: (TextEditingValue textEditingValue) {
                                  if (textEditingValue.text == '') {
                                    return commonAreaNames;
                                  }
                                  return commonAreaNames.where((String option) {
                                    return option.toLowerCase().contains(
                                      textEditingValue.text.toLowerCase(),
                                    );
                                  });
                                },
                                onSelected: (String selection) {
                                  _areaNameController.text = selection;
                                },
                                fieldViewBuilder:
                                    (context, textEditingController, focusNode, onFieldSubmitted) {
                                  textEditingController.text = _areaNameController.text;
                                  _areaNameController.value = textEditingController.value;

                                  return TextFormField(
                                    controller: textEditingController,
                                    focusNode: focusNode,
                                    decoration: const InputDecoration(
                                      labelText: 'Tên khu vực',
                                      hintText: 'Ví dụ: Phòng khách, Phòng ngủ...',
                                      border: OutlineInputBorder(),
                                      prefixIcon: Icon(Icons.room),
                                    ),
                                    validator: (value) {
                                      if (value == null || value.isEmpty) {
                                        return 'Vui lòng nhập tên khu vực';
                                      }
                                      if (value.length < 2) {
                                        return 'Tên khu vực phải có ít nhất 2 ký tự';
                                      }
                                      return null;
                                    },
                                  );
                                },
                              ),
                            ),
                            const SizedBox(width: 12),
                            ElevatedButton.icon(
                              onPressed: viewModel.isLoading ? null : _createArea,
                              icon: const Icon(Icons.add),
                              label: const Text('Tạo'),
                              style: ElevatedButton.styleFrom(
                                backgroundColor: Colors.blue[600],
                                foregroundColor: Colors.white,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 20,
                                  vertical: 12,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                // Areas List
                Expanded(
                  child: (viewModel.homeAreas?.isEmpty ?? true)
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.room_outlined, size: 64, color: Colors.grey[400]),
                              const SizedBox(height: 16),
                              Text(
                                'Chưa có khu vực nào',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: viewModel.homeAreas?.length ?? 0,
                          itemBuilder: (context, index) {
                            final area = viewModel.homeAreas![index];

                            // Tìm thống kê thiết bị cho area này từ homeDetail statistics
                            final homeDetail = viewModel.homeDetail;
                            final devicesByArea = homeDetail?.statistics.devicesByArea ?? [];
                            final areaStats = devicesByArea.firstWhere(
                              (stat) => stat.areaId == area.areaId,
                              orElse: () => DeviceAreaStatistic(
                                areaId: area.areaId,
                                areaName: area.displayName,
                                totalCount: area.deviceCount,
                                onlineCount: 0,
                              ),
                            );

                            return Card(
                              margin: const EdgeInsets.only(bottom: 12),
                              child: ListTile(
                                leading: Container(
                                  padding: const EdgeInsets.all(12),
                                  decoration: BoxDecoration(
                                    color: Colors.purple[100],
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Icon(
                                    Icons.room,
                                    color: Colors.purple[700],
                                    size: 24,
                                  ),
                                ),
                                title: Text(
                                  area.displayName,
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                subtitle: Text(
                                  '${areaStats.totalCount} thiết bị',
                                  style: TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 14,
                                  ),
                                ),
                                trailing: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    IconButton(
                                      icon: Icon(Icons.edit, color: Colors.blue[600]),
                                      onPressed: () => _editArea(area),
                                    ),
                                    IconButton(
                                      icon: Icon(Icons.delete, color: Colors.red[400]),
                                      onPressed: () => _deleteArea(area),
                                    ),
                                  ],
                                ),
                                onTap: () => _navigateToAreaDevices(area),
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Future<void> _createArea() async {
    if (!_formKey.currentState!.validate()) return;

    final areaName = _areaNameController.text.trim();
    final viewModel = context.read<HomeViewModel>();

    final success = await viewModel.createArea(
      homeId: widget.home.id,
      areaName: areaName,
    );

    if (success) {
       await viewModel.loadHomeAreas(widget.home.id); 
      _showSnackBar(
        'Tạo khu vực thành công',
        Colors.green,
      );
    } else if (viewModel.errorMessage != null) {
      _showSnackBar(
        viewModel.errorMessage!,
        Colors.red,
      );
    }
  }

  void _editArea(Area area) {
    final controller = TextEditingController(text: area.name);
    
    showDialog(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Sửa tên khu vực'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Tên khu vực',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () async {
              final newName = controller.text.trim();
              if (newName.isNotEmpty && newName != area.name) {
                Navigator.of(dialogContext).pop();
                await _updateArea(area.areaId, newName);
              } else {
                Navigator.of(dialogContext).pop();
              }
            },
            child: const Text('Lưu'),
          ),
        ],
      ),
    );
  }

  Future<void> _updateArea(int areaId, String newName) async {
    final viewModel = context.read<HomeViewModel>();
    final success = await viewModel.updateArea(
      homeId: widget.home.id,
      areaId: areaId,
      areaName: newName,
    );
    
    if (success) {
      _showSnackBar(
        'Cập nhật khu vực thành công',
        Colors.green,
      );
    } else if (viewModel.errorMessage != null) {
      _showSnackBar(
        viewModel.errorMessage!,
        Colors.red,
      );
    }
  }

  Future<void> _deleteArea(Area area) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (dialogContext) => AlertDialog(
        title: const Text('Xác nhận xóa'),
        content: Text('Bạn có chắc muốn xóa khu vực "${area.name}"?\n\nTất cả thiết bị trong khu vực này sẽ được chuyển về danh sách chung.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(false),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () => Navigator.of(dialogContext).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final viewModel = context.read<HomeViewModel>();
      final success = await viewModel.deleteArea(
        homeId: widget.home.id,
        areaId: area.id,
      );

      if (success) {
        _showSnackBar(
          'Xóa khu vực thành công',
          Colors.green,
        );
      } else if (viewModel.errorMessage != null) {
        _showSnackBar(
          viewModel.errorMessage!,
          Colors.red,
        );
      }
    }
  }

  void _navigateToAreaDevices(Area area) {
    _navigator.push(
      MaterialPageRoute(
        builder: (context) => AreaDevicesPage(
          home: widget.home,
          area: area,
        ),
      ),
    );
      context.read<HomeViewModel>().loadHomeAreas(widget.home.id);

  }

  void _showSnackBar(String message, Color backgroundColor) {
    if (mounted) {
      _scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
        ),
      );
    }
  }
}