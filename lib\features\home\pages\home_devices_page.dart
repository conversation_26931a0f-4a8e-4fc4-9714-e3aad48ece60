import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view_models/home_view_model.dart';
import '../../../core/models/home_model.dart';
import '../../../core/models/device_model.dart';
import '../../device/pages/device_detail_page.dart';
import '../../device/pages/device_info_page.dart';
import '../../device/pages/device_types_management_page.dart';
import '../../device/view_models/device_view_model.dart';

class HomeDevicesPage extends StatefulWidget {
  final Home home;

  const HomeDevicesPage({
    super.key,
    required this.home,
  });

  @override
  State<HomeDevicesPage> createState() => _HomeDevicesPageState();
}

class _HomeDevicesPageState extends State<HomeDevicesPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final viewModel = context.read<HomeViewModel>();
      viewModel.loadHomeDevices(widget.home.id);
      viewModel.loadHomeAreas(widget.home.id);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Tất cả thiết bị - ${widget.home.name}'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const DeviceTypesManagementPage(),
                ),
              );
            },
            icon: const Icon(Icons.category),
            tooltip: 'Loại thiết bị',
          ),
          IconButton(
            onPressed: () {
              final viewModel = context.read<HomeViewModel>();
              print('Manual refresh - Home devices count: ${viewModel.homeDevices.length}');
              viewModel.loadHomeDevices(widget.home.id);
            },
            icon: const Icon(Icons.refresh),
            tooltip: 'Refresh devices',
          ),
        ],
      ),
      body: Consumer<HomeViewModel>(
        builder: (context, viewModel, child) {
          if (viewModel.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (viewModel.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    viewModel.errorMessage!,
                    style: TextStyle(color: Colors.red[600]),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => viewModel.loadHomeDevices(widget.home.id),
                    child: const Text('Thử lại'),
                  ),
                ],
              ),
            );
          }

          return RefreshIndicator(
            onRefresh: () async {
              await viewModel.loadHomeDevices(widget.home.id);
              await viewModel.loadHomeAreas(widget.home.id);
            },
            child: _getAllHomeDevices(viewModel).isEmpty
                ? Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.devices_outlined, size: 64, color: Colors.grey[400]),
                        const SizedBox(height: 16),
                        Text(
                          'Không có thiết bị nào trong nhà này',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 16,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Hãy thêm thiết bị mới hoặc khám phá thiết bị',
                          style: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 14,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  )
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _getAllHomeDevices(viewModel).length,
                    itemBuilder: (context, index) {
                      final device = _getAllHomeDevices(viewModel)[index];
                      return Card(
                        margin: const EdgeInsets.only(bottom: 12),
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Container(
                                    padding: const EdgeInsets.all(12),
                                    decoration: BoxDecoration(
                                      color: _getDeviceColor(device.type ?? 'unknown').withOpacity(0.1),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Icon(
                                      _getDeviceIcon(device.type ?? 'unknown'),
                                      color: _getDeviceColor(device.type ?? 'unknown'),
                                      size: 24,
                                    ),
                                  ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          device.name,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 16,
                                          ),
                                          maxLines: 2,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          device.type ?? 'Unknown',
                                          style: TextStyle(
                                            color: Colors.grey[600],
                                            fontSize: 14,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                        const SizedBox(height: 2),
                                        Text(
                                        
                                              'Khu vực: ${_getAreaName(device.areaId!, viewModel)}',
                                          style: TextStyle(
                                            color: Colors.grey
                          ,
                                            fontSize: 12,
                                            fontWeight: FontWeight.w500,
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ],
                                    ),
                                  ),
                                  Container(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 6,
                                      vertical: 3,
                                    ),
                                    decoration: BoxDecoration(
                                      color: device.isOnline
                                          ? Colors.green[50]
                                          : Colors.red[50],
                                      borderRadius: BorderRadius.circular(8),
                                      border: Border.all(
                                        color: device.isOnline
                                            ? Colors.green[200]!
                                            : Colors.red[200]!,
                                      ),
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Container(
                                          width: 6,
                                          height: 6,
                                          decoration: BoxDecoration(
                                            color: device.isOnline
                                                ? Colors.green[600]
                                                : Colors.red[600],
                                            shape: BoxShape.circle,
                                          ),
                                        ),
                                        const SizedBox(width: 4),
                                        Text(
                                          device.isOnline ? 'Online' : 'Offline',
                                          style: TextStyle(
                                            color: device.isOnline
                                                ? Colors.green[700]
                                                : Colors.red[700],
                                            fontSize: 10,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 12),
                              // Simple action buttons
                              Row(
                                children: [
                                  Expanded(
                                    child: ElevatedButton.icon(
                                      onPressed: () => _navigateToDeviceControl(device),
                                      label: Text(device.isControllable ? 'Điều khiển' : 'Xem dữ liệu'),
                                      icon: Icon(device.isControllable ? Icons.settings_remote : Icons.sensors),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: _getDeviceColor(device.type ?? 'unknown'),
                                        foregroundColor: Colors.white,
                                        padding: const EdgeInsets.symmetric(vertical: 12),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  IconButton(
                                    onPressed: () => _showDeviceOptionsBottomSheet(device),
                                    icon: const Icon(Icons.more_vert),
                                    style: IconButton.styleFrom(
                                      backgroundColor: Colors.grey[100],
                                      foregroundColor: Colors.grey[700],
                                    ),
                                    tooltip: 'Tùy chọn khác',
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
          );
        },
      ),
     
    );
  }

  /// Lấy tất cả thiết bị trong nhà (không quan tâm đến area_id)
  List<Device> _getAllHomeDevices(HomeViewModel viewModel) {
    return viewModel.homeDevices;
  }

  /// Lấy tên khu vực theo area_id
 String _getAreaName(int? areaId, HomeViewModel viewModel) {
  if (areaId == null) return 'Không có khu vực';

  final areas = viewModel.homeDetail?.areas;
  if (areas == null || areas.isEmpty) return 'Không có dữ liệu khu vực';

  try {
    final match = areas.firstWhere((a) => a.areaId == areaId);
    return match.name.toString();
  } catch (e) {
    print(' Không tìm thấy areaId $areaId trong danh sách areas');
    return 'Không tìm thấy';
  }
}



  IconData _getDeviceIcon(String type) {
    switch (type.toLowerCase()) {
      case 'smart light':
      case 'light':
      case 'đèn':
        return Icons.lightbulb_outline;
      case 'light sensor':
      case 'sensor':
      case 'cảm biến':
        return Icons.sensors;
      case 'camera':
        return Icons.camera_alt_outlined;
      case 'temperature sensor':
        return Icons.thermostat;
      case 'smart ac':
      case 'ac':
      case 'điều hòa':
        return Icons.ac_unit;
      case 'smart switch':
      case 'switch':
      case 'công tắc':
        return Icons.toggle_on_outlined;
      case 'fan':
      case 'quạt':
        return Icons.air;
      default:
        return Icons.device_unknown;
    }
  }

  Color _getDeviceColor(String type) {
    switch (type.toLowerCase()) {
      case 'smart light':
      case 'light':
      case 'đèn':
        return Colors.amber;
      case 'light sensor':
      case 'sensor':
      case 'cảm biến':
        return Colors.green;
      case 'camera':
        return Colors.purple;
      case 'temperature sensor':
        return Colors.orange;
      case 'smart ac':
      case 'ac':
      case 'điều hòa':
        return Colors.blue;
      case 'smart switch':
      case 'switch':
      case 'công tắc':
        return Colors.teal;
      case 'fan':
      case 'quạt':
        return Colors.cyan;
      default:
        return Colors.grey;
    }
  }

  void _navigateToDeviceControl(Device device) {
    if (!device.isControllable) {
      // Hiển thị thông báo cho thiết bị sensor
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.sensors, color: Colors.white),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Thiết bị "${device.name}" là cảm biến, không thể điều khiển',
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.orange,
          duration: const Duration(seconds: 3),
          behavior: SnackBarBehavior.floating,
        ),
      );
      return;
    }

    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => DeviceDetailPage(
          device: device,
          home: widget.home,
        ),
      ),
    );
  }

  void _showDeviceOptionsBottomSheet(Device device) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              device.name,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),

            // Options
            _buildBottomSheetOption(
              icon: Icons.add_location,
              title: 'Thêm vào khu vực',
              subtitle: 'Gán thiết bị vào khu vực cụ thể',
              color: Colors.blue,
              onTap: () {
                Navigator.pop(context);
                _showAddToAreaDialog(device);
              },
            ),
            _buildBottomSheetOption(
              icon: Icons.info_outline,
              title: 'Chi tiết thiết bị',
              subtitle: 'Xem thông tin chi tiết',
              color: Colors.green,
              onTap: () {
                Navigator.pop(context);
                _showDeviceDetails(device);
              },
            ),
            _buildBottomSheetOption(
              icon: Icons.remove_circle_outline,
              title: 'Xóa khỏi nhà',
              subtitle: 'Gỡ thiết bị khỏi nhà này',
              color: Colors.red,
              onTap: () {
                Navigator.pop(context);
                _showRemoveFromHomeDialog(device);
              },
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomSheetOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required Color color,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.all(16),
        margin: const EdgeInsets.only(bottom: 8),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.grey[200]!),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
            Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
          ],
        ),
      ),
    );
  }

  void _showAddToAreaDialog(Device device) {
    final viewModel = context.read<HomeViewModel>();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Thêm ${device.name} vào khu vực'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (viewModel.homeAreas?.isEmpty ?? true)
              const Text('Chưa có khu vực nào. Vui lòng tạo khu vực trước.')
            else
              ...viewModel.homeAreas!.map((area) => ListTile(
                title: Text(area.name ?? 'Unknown Area'),
                leading: const Icon(Icons.room),
                onTap: () async {
                  Navigator.of(context).pop();
                  final success = await viewModel.addDeviceToArea(
                    homeId: widget.home.id,
                    areaId: area.id,
                    deviceId: device.id,
                  );
                  
                  if (mounted) {
                    if (success) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text('Đã thêm ${device.name} vào ${area.name}'),
                          backgroundColor: Colors.green,
                        ),
                      );
                    } else if (viewModel.errorMessage != null) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(viewModel.errorMessage!),
                          backgroundColor: Colors.red,
                        ),
                      );
                    }
                  }
                },
              )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
        ],
      ),
    );
  }

  void _showDeviceDetails(Device device) async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => DeviceInfoPage(
          device: device,
          home: widget.home,
        ),
      ),
    );

    // If device was deleted, reload the device list
    if (result == true && mounted) {
      final viewModel = context.read<HomeViewModel>();
      await viewModel.loadHomeDevices(widget.home.id);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Đã cập nhật danh sách thiết bị'),
            backgroundColor: Colors.blue,
          ),
        );
      }
    }
  }

  void _showAddDeviceDialog() {
    final viewModel = context.read<HomeViewModel>();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Thêm thiết bị vào nhà'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Chọn thiết bị từ danh sách thiết bị có sẵn:'),
            SizedBox(height: 16),
            Text(
              'Lưu ý: Chỉ có thể thêm thiết bị đã được đăng ký trong hệ thống.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _showAvailableDevicesDialog();
            },
            child: const Text('Chọn thiết bị'),
          ),
        ],
      ),
    );
  }

  void _showAvailableDevicesDialog() {
    showDialog(
      context: context,
      builder: (context) => Consumer<DeviceViewModel>(
        builder: (context, deviceViewModel, child) {
          return Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: Container(
              width: double.maxFinite,
              height: MediaQuery.of(context).size.height * 0.7,
              padding: const EdgeInsets.all(20),
              child: Column(
                children: [
                  // Header
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.green[50],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.add_home,
                          color: Colors.green[600],
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Expanded(
                        child: Text(
                          'Thêm thiết bị vào nhà',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      IconButton(
                        onPressed: () => Navigator.of(context).pop(),
                        icon: const Icon(Icons.close),
                        style: IconButton.styleFrom(
                          backgroundColor: Colors.grey[100],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  const Divider(),
                  const SizedBox(height: 16),
                  // Content
                  Expanded(
                    child: Column(
                      children: [
                  if (deviceViewModel.isLoading)
                    const Center(child: CircularProgressIndicator())
                  else if (deviceViewModel.errorMessage != null)
                    Column(
                      children: [
                        Icon(Icons.error_outline, size: 48, color: Colors.red[300]),
                        const SizedBox(height: 8),
                        Text(
                          deviceViewModel.errorMessage!,
                          style: TextStyle(color: Colors.red[600]),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => deviceViewModel.loadAvailableDevices(),
                          child: const Text('Thử lại'),
                        ),
                      ],
                    )
                  else if (deviceViewModel.availableDevices.isEmpty)
                    Column(
                      children: [
                        Icon(Icons.devices_outlined, size: 48, color: Colors.grey[400]),
                        const SizedBox(height: 8),
                        Text(
                          'Không có thiết bị nào có sẵn',
                          style: TextStyle(color: Colors.grey[600]),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Tất cả thiết bị đã được thêm vào nhà',
                          style: TextStyle(color: Colors.grey[500], fontSize: 12),
                        ),
                      ],
                    )
                  else
                    Expanded(
                      child: ListView.builder(
                        itemCount: deviceViewModel.availableDevices.length,
                        itemBuilder: (context, index) {
                          final device = deviceViewModel.availableDevices[index];
                          return Container(
                            margin: const EdgeInsets.only(bottom: 12),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: Colors.grey[200]!),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.1),
                                  spreadRadius: 1,
                                  blurRadius: 4,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      // Device icon
                                      Container(
                                        width: 48,
                                        height: 48,
                                        decoration: BoxDecoration(
                                          color: _getDeviceColor(device.type ?? 'unknown').withOpacity(0.1),
                                          borderRadius: BorderRadius.circular(12),
                                        ),
                                        child: Icon(
                                          _getDeviceIcon(device.type ?? 'unknown'),
                                          color: _getDeviceColor(device.type ?? 'unknown'),
                                          size: 24,
                                        ),
                                      ),
                                      const SizedBox(width: 12),
                                      // Device info
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Text(
                                              device.name,
                                              style: const TextStyle(
                                                fontWeight: FontWeight.bold,
                                                fontSize: 16,
                                              ),
                                              maxLines: 1,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                            const SizedBox(height: 4),
                                            Text(
                                              device.type ?? 'Unknown',
                                              style: TextStyle(
                                                color: Colors.grey[600],
                                                fontSize: 14,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      // Status badge
                                      Container(
                                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                                        decoration: BoxDecoration(
                                          color: device.isOnline ? Colors.green[50] : Colors.red[50],
                                          borderRadius: BorderRadius.circular(12),
                                          border: Border.all(
                                            color: device.isOnline ? Colors.green[200]! : Colors.red[200]!,
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            Container(
                                              width: 6,
                                              height: 6,
                                              decoration: BoxDecoration(
                                                color: device.isOnline ? Colors.green[600] : Colors.red[600],
                                                shape: BoxShape.circle,
                                              ),
                                            ),
                                            const SizedBox(width: 4),
                                            Text(
                                              device.isOnline ? 'Online' : 'Offline',
                                              style: TextStyle(
                                                color: device.isOnline ? Colors.green[700] : Colors.red[700],
                                                fontSize: 11,
                                                fontWeight: FontWeight.w500,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  // Add button
                                  SizedBox(
                                    width: double.infinity,
                                    child: ElevatedButton.icon(
                                      onPressed: () => _addDeviceToHome(device),
                                      icon: const Icon(Icons.add_home, size: 18),
                                      label: const Text('Thêm vào nhà'),
                                      style: ElevatedButton.styleFrom(
                                        backgroundColor: Colors.green[600],
                                        foregroundColor: Colors.white,
                                        padding: const EdgeInsets.symmetric(vertical: 12),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(8),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                      ],
                    ),
                  ),
                  // Footer actions
                  const SizedBox(height: 16),
                  const Divider(),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      if (!deviceViewModel.isLoading && deviceViewModel.errorMessage == null)
                        Expanded(
                          child: OutlinedButton.icon(
                            onPressed: () => deviceViewModel.loadAvailableDevices(),
                            icon: const Icon(Icons.refresh, size: 18),
                            label: const Text('Làm mới'),
                            style: OutlinedButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                          ),
                        ),
                      if (!deviceViewModel.isLoading && deviceViewModel.errorMessage == null)
                        const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => Navigator.of(context).pop(),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.grey[600],
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 12),
                          ),
                          child: const Text('Đóng'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );

    // Load available devices when dialog opens
    final deviceViewModel = context.read<DeviceViewModel>();
    deviceViewModel.loadAvailableDevices();
  }

  Future<void> _addDeviceToHome(Device device) async {
    final homeViewModel = context.read<HomeViewModel>();

    // Close the dialog first
    Navigator.of(context).pop();

    final success = await homeViewModel.addDeviceToHome(
      homeId: widget.home.id,
      deviceId: device.id,
    );

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Đã thêm ${device.name} vào nhà'),
            backgroundColor: Colors.green,
          ),
        );
        // Reload available devices to update the list
        final deviceViewModel = context.read<DeviceViewModel>();
        deviceViewModel.loadAvailableDevices();
      } else if (homeViewModel.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(homeViewModel.errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showRemoveFromHomeDialog(Device device) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận xóa'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.warning_amber_rounded,
              size: 48,
              color: Colors.orange[600],
            ),
            const SizedBox(height: 16),
            Text(
              'Bạn có chắc chắn muốn xóa "${device.name}" khỏi nhà này?',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'Thiết bị sẽ không còn thuộc về nhà này nhưng vẫn tồn tại trong hệ thống.',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey,
                fontStyle: FontStyle.italic,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _removeDeviceFromHome(device);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );
  }

  Future<void> _removeDeviceFromHome(Device device) async {
    final viewModel = context.read<HomeViewModel>();

    final success = await viewModel.removeDeviceFromHome(
      homeId: widget.home.id,
      deviceId: device.id,
    );

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Đã xóa ${device.name} khỏi nhà'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (viewModel.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(viewModel.errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
