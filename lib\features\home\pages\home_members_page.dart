import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view_models/home_view_model.dart';
import '../view_models/home_invitation_view_model.dart';
import '../../../core/models/home_model.dart';
import 'send_invitation_page.dart';
import 'invitation_list_page.dart';

class HomeMembersPage extends StatefulWidget {
  final Home home;

  const HomeMembersPage({
    super.key,
    required this.home,
  });

  @override
  State<HomeMembersPage> createState() => _HomeMembersPageState();
}

class _HomeMembersPageState extends State<HomeMembersPage> {
  HomeViewModel? _viewModel;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _viewModel = context.read<HomeViewModel>();
      _viewModel!.addListener(_handleErrorChanges);
      _viewModel!.loadHomeUsers(widget.home.homeId);
    });
  }

  void _handleErrorChanges() {
    if (_viewModel?.errorMessage != null && mounted) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(_viewModel!.errorMessage!),
              backgroundColor: Colors.red,
              action: SnackBarAction(
                label: 'Đóng',
                textColor: Colors.white,
                onPressed: () {
                  _viewModel!.clearError();
                },
              ),
            ),
          );
        }
      });
    }
  }

  @override
  void dispose() {
    _viewModel?.removeListener(_handleErrorChanges);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Thành viên - ${widget.home.name}'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.email),
            onPressed: () {
              final invitationViewModel = HomeInvitationViewModel();
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => ChangeNotifierProvider<HomeInvitationViewModel>.value(
                    value: invitationViewModel,
                    child: InvitationListPage(
                      homeId: widget.home.homeId,
                      homeName: widget.home.name,
                    ),
                  ),
                ),
              );
            },
            tooltip: 'Quản lý lời mời',
          ),
          IconButton(
            icon: const Icon(Icons.person_add),
            onPressed: () async {
              final invitationViewModel = HomeInvitationViewModel();
              final result = await Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => ChangeNotifierProvider<HomeInvitationViewModel>.value(
                    value: invitationViewModel,
                    child: SendInvitationPage(
                      homeId: widget.home.homeId,
                      homeName: widget.home.name,
                    ),
                  ),
                ),
              );

              if (result == true && _viewModel != null) {
                _viewModel!.loadHomeUsers(widget.home.homeId);
              }
            },
            tooltip: 'Mời thành viên',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              if (_viewModel != null) {
                _viewModel!.loadHomeUsers(widget.home.homeId);
              }
            },
            tooltip: 'Làm mới',
          ),
        ],
      ),
      body: Consumer<HomeViewModel>(
        builder: (context, viewModel, child) {
          if (viewModel.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return RefreshIndicator(
            onRefresh: () async {
              if (_viewModel != null) {
                await _viewModel!.loadHomeUsers(widget.home.homeId);
              }
            },
            child: Column(
              children: [
                // Info Section - Invitation System
                Container(
                  margin: const EdgeInsets.all(16),
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue[50],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue[200]!),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue[600]),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Thêm thành viên qua lời mời',
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w600,
                                color: Colors.blue[800],
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Sử dụng nút "Mời thành viên" để gửi lời mời qua email',
                              style: TextStyle(
                                fontSize: 12,
                                color: Colors.blue[700],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Members List
                Expanded(
                  child: (viewModel.homeUsers?.isEmpty ?? true)
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.people_outline, size: 64, color: Colors.grey[400]),
                              const SizedBox(height: 16),
                              Text(
                                'Chưa có thành viên nào',
                                style: TextStyle(
                                  color: Colors.grey[600],
                                  fontSize: 16,
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          padding: const EdgeInsets.all(16),
                          itemCount: viewModel.homeUsers?.length ?? 0,
                          itemBuilder: (context, index) {
                            final user = viewModel.homeUsers![index];
                            return Card(
                              margin: const EdgeInsets.only(bottom: 8),
                              child: ListTile(
                                leading: CircleAvatar(
                                  backgroundColor: Colors.blue[100],
                                  child: Text(
                                    user.email.substring(0, 1).toUpperCase(),
                                    style: TextStyle(
                                      color: Colors.blue[800],
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                                title: Text(
                                  user.fullName ?? user.email,
                                  style: const TextStyle(fontWeight: FontWeight.w500),
                                ),
                                subtitle: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(user.email),
                                    const SizedBox(height: 4),
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 2,
                                      ),
                                      decoration: BoxDecoration(
                                        color: _getRoleColor(user.userRole).withValues(alpha: 0.1),
                                        borderRadius: BorderRadius.circular(12),
                                        border: Border.all(color: _getRoleColor(user.userRole).withValues(alpha: 0.3)),
                                      ),
                                      child: Text(
                                        user.userRole,
                                        style: TextStyle(
                                          color: _getRoleColor(user.userRole),
                                          fontSize: 12,
                                          fontWeight: FontWeight.w500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                trailing: PopupMenuButton<String>(
                                  onSelected: (value) {
                                    switch (value) {
                                      // case 'promote':
                                      //   _showPromoteDialog(user);
                                      //   break;
                                      case 'transfer':
                                        _showTransferOwnershipDialog(user);
                                        break;
                                      case 'remove':
                                        _removeMember(user);
                                        break;
                                    }
                                  },
                                  itemBuilder: (context) => [
                                    
                                    const PopupMenuItem(
                                      value: 'transfer',
                                      child: Row(
                                        children: [
                                          Icon(Icons.transfer_within_a_station, color: Colors.purple),
                                          SizedBox(width: 8),
                                          Text('Chuyển quyền sở hữu'),
                                        ],
                                      ),
                                    ),
                                    const PopupMenuItem(
                                      value: 'remove',
                                      child: Row(
                                        children: [
                                          Icon(Icons.delete, color: Colors.red),
                                          SizedBox(width: 8),
                                          Text('Xóa thành viên'),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }



  Future<void> _removeMember(HomeUser user) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận xóa'),
        content: Text('Bạn có chắc muốn xóa ${user.fullName ?? user.email} khỏi nhà này?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final viewModel = context.read<HomeViewModel>();
      final success = await viewModel.removeUserFromHome(
        homeId: widget.home.homeId,
        userId: user.userId,
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Xóa thành viên thành công'),
              backgroundColor: Colors.green,
            ),
          );
        } else if (viewModel.errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(viewModel.errorMessage!),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _showPromoteDialog(HomeUser user) async {
    // Determine current role and available options
    final currentRole = _getRoleName(user.role);
    List<String> availableRoles = [];

    // Only allow changing between ADMIN and MEMBER (not OWNER due to backend limitation)
    if (currentRole == 'MEMBER') {
      availableRoles = ['ADMIN'];
    } else if (currentRole == 'ADMIN') {
      availableRoles = ['MEMBER'];
    } else {
      // OWNER cannot be changed via promote (use transfer ownership instead)
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Không thể thay đổi quyền OWNER. Sử dụng "Chuyển quyền sở hữu" thay thế.'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final selectedRole = await showDialog<String>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Thay đổi quyền'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Quyền hiện tại: $currentRole'),
            const SizedBox(height: 8),
            Text('Chọn quyền mới cho ${user.fullName ?? user.email}:'),
            const SizedBox(height: 16),
            ...availableRoles.map((role) => ListTile(
              title: Text(role),
              subtitle: Text(_getRoleDescription(role)),
              leading: Icon(
                role == 'ADMIN' ? Icons.admin_panel_settings : Icons.person,
                color: role == 'ADMIN' ? Colors.orange : Colors.blue,
              ),
              onTap: () => Navigator.of(context).pop(role),
            )),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
        ],
      ),
    );

    if (selectedRole != null && mounted) {
      final viewModel = context.read<HomeViewModel>();
      final success = await viewModel.promoteUser(
        homeId: widget.home.homeId,
        userId: user.userId,
        newRole: selectedRole,
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Thay đổi quyền ${user.fullName} thành $selectedRole thành công'),
              backgroundColor: Colors.green,
            ),
          );
        } else if (viewModel.errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(viewModel.errorMessage!),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _showTransferOwnershipDialog(HomeUser user) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Chuyển quyền sở hữu'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.warning_amber_rounded,
              size: 48,
              color: Colors.orange[600],
            ),
            const SizedBox(height: 16),
            Text(
              'Bạn có chắc chắn muốn chuyển quyền sở hữu nhà này cho ${user.fullName ?? user.email}?',
              textAlign: TextAlign.center,
              style: const TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 8),
            const Text(
              'Sau khi chuyển, bạn sẽ trở thành ADMIN và không thể hoàn tác.',
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 14,
                color: Colors.red,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('Chuyển quyền'),
          ),
        ],
      ),
    );

    if (confirmed == true && mounted) {
      final viewModel = context.read<HomeViewModel>();
      final success = await viewModel.transferOwnership(
        homeId: widget.home.homeId,
        newOwnerId: user.userId,
      );

      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Chuyển quyền sở hữu cho ${user.fullName ?? user.email} thành công'),
              backgroundColor: Colors.green,
            ),
          );
        } else if (viewModel.errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(viewModel.errorMessage!),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  String _getRoleName(String? role) {
    if (role == null) return 'MEMBER';
    return role.toUpperCase();
  }

  String _getRoleDescription(String role) {
    switch (role.toUpperCase()) {
      case 'OWNER':
        return 'Có toàn quyền, có thể xóa nhà';
      case 'ADMIN':
        return 'Có thể quản lý nhà, thành viên, thiết bị';
      case 'MEMBER':
        return 'Có thể xem và sử dụng thiết bị';
      default:
        return 'Không xác định';
    }
  }

  Color _getRoleColor(String role) {
    switch (role.toUpperCase()) {
      case 'OWNER':
        return Colors.purple;
      case 'ADMIN':
        return Colors.orange;
      case 'MEMBER':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }
}
