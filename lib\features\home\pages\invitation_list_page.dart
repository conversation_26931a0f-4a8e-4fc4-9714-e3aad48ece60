import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view_models/home_invitation_view_model.dart';
import '../../../core/models/home_invitation_model.dart';
import 'send_invitation_page.dart';

class InvitationListPage extends StatefulWidget {
  final int homeId;
  final String homeName;

  const InvitationListPage({
    Key? key,
    required this.homeId,
    required this.homeName,
  }) : super(key: key);

  @override
  State<InvitationListPage> createState() => _InvitationListPageState();
}

class _InvitationListPageState extends State<InvitationListPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadInvitations();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadInvitations() async {
    final viewModel = context.read<HomeInvitationViewModel>();
    await viewModel.initialize(homeId: widget.homeId);
    await viewModel.loadInvitations(widget.homeId);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text('Lời mời thành viên'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadInvitations,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Đang chờ'),
            Tab(text: 'Đã chấp nhận'),
            Tab(text: 'Đã hết hạn'),
          ],
        ),
      ),
      body: Consumer<HomeInvitationViewModel>(
        builder: (context, viewModel, child) {
          if (viewModel.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return Column(
            children: [
              

              // Tab Content
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: [
                    _buildInvitationList(viewModel.pendingInvitations, 'pending'),
                    _buildInvitationList(viewModel.acceptedInvitations, 'accepted'),
                    _buildInvitationList(viewModel.expiredInvitations, 'expired'),
                  ],
                ),
              ),
            ],
          );
        },
      ),
     floatingActionButton: FloatingActionButton.extended(
  backgroundColor: Colors.blue[600],
  foregroundColor: Colors.white,
  icon: const Icon(Icons.person_add),
  label: const Text('Mời thành viên'),
  onPressed: () async {
    final invitationViewModel = HomeInvitationViewModel();

    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (_) => ChangeNotifierProvider<HomeInvitationViewModel>.value(
          value: invitationViewModel,
          child: SendInvitationPage(
            homeId: widget.homeId,
            homeName: widget.homeName,
          ),
        ),
      ),
    );

    if (result == true) {
      _loadInvitations();
    }
  },
),


      
    );
  }

 
  Widget _buildInvitationList(List<HomeInvitation> invitations, String type) {
    if (invitations.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              _getEmptyIcon(type),
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              _getEmptyMessage(type),
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 16,
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadInvitations,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: invitations.length,
        itemBuilder: (context, index) {
          final invitation = invitations[index];
          return _buildInvitationCard(invitation);
        },
      ),
    );
  }

  Widget _buildInvitationCard(HomeInvitation invitation) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getStatusColor(invitation.status).withOpacity( 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.email,
                    color: _getStatusColor(invitation.status),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        invitation.email,
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        InvitationConstants.getRoleDisplayName(invitation.roleId),
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: _getStatusColor(invitation.status),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    invitation.statusDisplay,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Invitation Details
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  _buildDetailRow(Icons.schedule, 'Gửi lúc', _formatDateTime(invitation.createdAt)),
                  if (invitation.status == InvitationConstants.statusPending && !invitation.isExpired)
                    _buildDetailRow(Icons.timer, 'Hết hạn', invitation.timeRemaining),
                  if (invitation.acceptedAt != null)
                    _buildDetailRow(Icons.check, 'Chấp nhận lúc', _formatDateTime(invitation.acceptedAt!)),
                  if (invitation.inviterName != null)
                    _buildDetailRow(Icons.person, 'Người mời', invitation.inviterName!),
                ],
              ),
            ),

            // Action Buttons
            if (invitation.status == InvitationConstants.statusPending && !invitation.isExpired) ...[
              const SizedBox(height: 12),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _resendInvitation(invitation),
                      icon: const Icon(Icons.refresh, size: 18),
                      label: const Text('Gửi lại'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.blue[600],
                        side: BorderSide(color: Colors.blue[600]!),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _cancelInvitation(invitation),
                      icon: const Icon(Icons.cancel, size: 18),
                      label: const Text('Hủy'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: Colors.red[600],
                        side: BorderSide(color: Colors.red[600]!),
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Icon(icon, size: 16, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text(
            '$label: ',
            style: TextStyle(
              fontSize: 12,
              color: Colors.grey[600],
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  IconData _getEmptyIcon(String type) {
    switch (type) {
      case 'pending':
        return Icons.schedule;
      case 'accepted':
        return Icons.check_circle;
      case 'expired':
        return Icons.cancel;
      default:
        return Icons.email;
    }
  }

  String _getEmptyMessage(String type) {
    switch (type) {
      case 'pending':
        return 'Không có lời mời đang chờ';
      case 'accepted':
        return 'Chưa có lời mời nào được chấp nhận';
      case 'expired':
        return 'Không có lời mời hết hạn';
      default:
        return 'Không có lời mời';
    }
  }

  Color _getStatusColor(String status) {
    switch (status) {
      case InvitationConstants.statusPending:
        return Colors.orange;
      case InvitationConstants.statusAccepted:
        return Colors.blue;
      case InvitationConstants.statusExpired:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  Future<void> _resendInvitation(HomeInvitation invitation) async {
    final viewModel = context.read<HomeInvitationViewModel>();
    
    final success = await viewModel.resendInvitation(invitation.invitationId);
    
    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Gửi lại lời mời thành công đến ${invitation.email}'),
            backgroundColor: Colors.blue,
          ),
        );
      } else if (viewModel.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(viewModel.errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _cancelInvitation(HomeInvitation invitation) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận hủy lời mời'),
        content: Text('Bạn có chắc muốn hủy lời mời đến ${invitation.email}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Không'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Hủy lời mời'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final viewModel = context.read<HomeInvitationViewModel>();
      
      final success = await viewModel.cancelInvitation(invitation.invitationId);
      
      if (mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Hủy lời mời thành công cho ${invitation.email}'),
              backgroundColor: Colors.blue,
            ),
          );
        } else if (viewModel.errorMessage != null) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(viewModel.errorMessage!),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
