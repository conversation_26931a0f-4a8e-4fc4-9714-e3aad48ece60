import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/repositories/home_invitation_repository.dart';
import '../../../core/models/home_invitation_model.dart';

/// ViewModel for managing home invitations
class HomeInvitationViewModel extends ChangeNotifier {
  final HomeInvitationRepository _repository = HomeInvitationRepository();



  /// Validate email format
  bool isValidEmail(String email) {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(email);
  }

  // State
  List<HomeInvitation> _invitations = [];
  HomeInvitation? _currentInvitation;
  String? _token;
  int? _currentHomeId;
  bool _isLoading = false;
  String? _errorMessage;
  String? _successMessage;

  static SharedPreferences? _prefs;
  bool disposed = false;

  // Constructor - auto initialize token
  HomeInvitationViewModel() {
    _autoInitializeToken();
  }

  Future<void> _autoInitializeToken() async {
    try {
      _prefs ??= await SharedPreferences.getInstance();
      _token = _prefs!.getString('auth_token');
      if (_token != null) {
        print('Token auto-initialized');
      } else {
        print('No token found');
      }
    } catch (e) {
      print('Token initialization failed: $e');
    }
  }

  // Getters
  List<HomeInvitation> get invitations => _invitations;
  HomeInvitation? get currentInvitation => _currentInvitation;
  String? get token => _token;
  int? get currentHomeId => _currentHomeId;
  bool get isLoading => _isLoading;
  String? get errorMessage => _errorMessage;
  String? get successMessage => _successMessage;

  // Filtered invitations
  List<HomeInvitation> get pendingInvitations =>
      _invitations.where((inv) => inv.status == InvitationConstants.statusPending).toList();

  List<HomeInvitation> get acceptedInvitations =>
      _invitations.where((inv) => inv.status == InvitationConstants.statusAccepted).toList();

  List<HomeInvitation> get expiredInvitations =>
      _invitations.where((inv) => inv.status == InvitationConstants.statusExpired || inv.isExpired).toList();

  @override
  void dispose() {
    disposed = true;
    super.dispose();
  }

  /// Initialize with token and home ID
  Future<void> initialize({String? token, int? homeId}) async {
    if (token != null) {
      _token = token;
    } else {
      _prefs ??= await SharedPreferences.getInstance();
      _token = _prefs!.getString('auth_token');
    }

    if (homeId != null) {
      _currentHomeId = homeId;
    }

    // ✅ Gọi notify sau build để tránh crash
    delayedNotifyListeners();
  }

  /// Send invitation to user
  Future<bool> sendInvitation({
    required int homeId,
    required String email,
    int? roleId,
  }) async {
    if (_token == null) {
      await _autoInitializeToken();
      if (_token == null) {
        setError('Token không tồn tại. Vui lòng đăng nhập lại.');
        return false;
      }
    }

    final result = await handleAsync(() async {
      final response = await _repository.sendInvitation(
        token: _token!,
        homeId: homeId,
        email: email,
        roleId: roleId ?? InvitationConstants.roleMember,
      );

      if (response.success) {
        if (response.data != null) {
          _invitations.insert(0, response.data!);
        }
        setSuccessMessage('Gửi lời mời thành công đến $email');
        safeNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi gửi lời mời');

    return result ?? false;
  }

  /// Load invitations for a home
  Future<bool> loadInvitations(int homeId) async {
    if (_token == null) {
      await _autoInitializeToken();
      if (_token == null) {
        setError('Token không tồn tại. Vui lòng đăng nhập lại.');
        return false;
      }
    }

    _currentHomeId = homeId;

    final result = await handleAsync(() async {
      final response = await _repository.getInvitations(
        token: _token!,
        homeId: homeId,
      );

      if (response.success) {
        _invitations = response.data ?? [];
        safeNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi tải danh sách lời mời');

    return result ?? false;
  }

  /// Cancel invitation
  Future<bool> cancelInvitation(int invitationId) async {
    if (_token == null || _currentHomeId == null) {
      setError('Token hoặc Home ID không tồn tại');
      return false;
    }

    final result = await handleAsync(() async {
      final response = await _repository.cancelInvitation(
        token: _token!,
        homeId: _currentHomeId!,
        invitationId: invitationId,
      );

      if (response.success) {
        _invitations.removeWhere((inv) => inv.invitationId == invitationId);
        setSuccessMessage('Hủy lời mời thành công');
        safeNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi hủy lời mời');

    return result ?? false;
  }

  /// Resend invitation
  Future<bool> resendInvitation(int invitationId) async {
    if (_token == null || _currentHomeId == null) {
      setError('Token hoặc Home ID không tồn tại');
      return false;
    }

    final result = await handleAsync(() async {
      final response = await _repository.resendInvitation(
        token: _token!,
        homeId: _currentHomeId!,
        invitationId: invitationId,
      );

      if (response.success) {
        if (response.data != null) {
          final index = _invitations.indexWhere((inv) => inv.invitationId == invitationId);
          if (index != -1) {
            _invitations[index] = response.data!;
          }
        }
        setSuccessMessage('Gửi lại lời mời thành công');
        safeNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi gửi lại lời mời');

    return result ?? false;
  }

  /// Other helper methods
  void setError(String message) {
    _errorMessage = message;
    _successMessage = null;
    notifyListeners();
  }

  void setSuccessMessage(String message) {
    _successMessage = message;
    _errorMessage = null;
    notifyListeners();
  }

  void clearError() {
    _errorMessage = null;
    notifyListeners();
  }

  void clearSuccess() {
    _successMessage = null;
    delayedNotifyListeners();
  }

  void delayedNotifyListeners() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!disposed) notifyListeners();
    });
  }

  void setLoading(bool loading) {
    if (_isLoading == loading) return; // ✅ tránh notify dư
    _isLoading = loading;
    notifyListeners();
  }

  void safeNotifyListeners() {
    if (!disposed) notifyListeners();
  }

  Future<T?> handleAsync<T>(Future<T> Function() operation, {String? errorPrefix}) async {
    try {
      setLoading(true);
      clearError();
      clearSuccess();

      final result = await operation();
      return result;
    } catch (e) {
      final message = errorPrefix != null ? '$errorPrefix: $e' : e.toString();
      setError(message);
      return null;
    } finally {
      setLoading(false);
    }
  }
}
