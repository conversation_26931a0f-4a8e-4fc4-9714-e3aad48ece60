import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/base/base_view_model.dart';
import '../../../core/repositories/home_repository.dart';
import '../../../core/models/home_model.dart';
import '../../../core/models/area_model.dart';
import '../../../core/models/device_model.dart';

/// ViewModel cho Home module
class HomeViewModel extends BaseViewModel {
  final HomeRepository _homeRepository;
  

  HomeViewModel({required HomeRepository homeRepository})
      : _homeRepository = homeRepository;

  // States
  List<Home> _homes = [];
  Home? _currentHome;
  HomeDetail? _homeDetail;
  List<HomeUser>? _homeUsers;
  List<Area>? _homeAreas;
  List<Device> _areaDevices = [];
  List<Device> _homeDevices = [];
  String? _successMessage;
  bool _isLoadingHomes = false;

  // Debounce mechanism to prevent continuous API calls
  DateTime? _lastLoadTime;
  static const Duration _debounceDelay = Duration(seconds: 3); // Increased to 3 seconds

  // Statistics refresh tracking
  bool _needsStatsRefresh = false;

  // Statistics cache
  DateTime? _lastStatsLoadTime;
  static const Duration _statsCacheDelay = Duration(minutes: 3);

  // Getters
  List<Home> get homes => _homes;
  Home? get currentHome => _currentHome;
  HomeDetail? get homeDetail => _homeDetail;
  List<HomeUser>? get homeUsers => _homeUsers;
  List<Area>? get homeAreas => _homeAreas;
  List<Device> get areaDevices => _areaDevices;
  List<Device> get homeDevices => _homeDevices;
  String? get successMessage => _successMessage;


  /// Load danh sách homes của user
  Future<bool> loadHomes() async {
    if (_isLoadingHomes) {
      print('⚠️ loadHomes already in progress, skipping...');
      return false;
    }


    // Debounce mechanism for loadHomes as well
    final now = DateTime.now();
    if (_lastLoadTime != null && now.difference(_lastLoadTime!) < _debounceDelay) {
      print('🔍 [DEBUG] loadHomes - Debounced, skipping... (last call: ${now.difference(_lastLoadTime!).inSeconds}s ago)');
      return true;
    }

    print('🏠 [DEBUG] loadHomes() called - Stack trace:');
    print(StackTrace.current.toString().split('\n').take(3).join('\n')); // Reduced to 3 lines

    _isLoadingHomes = true;
    _lastLoadTime = now; // Set immediately to prevent concurrent calls
    try {
      final result = await handleAsync(() async {
        final prefs = await SharedPreferences.getInstance();
        final token = prefs.getString('auth_token');

        print('🔑 [DEBUG] Token check: ${token != null ? 'EXISTS (${token.length} chars)' : 'NULL'}');

        if (token == null) {
          throw Exception('Token không tồn tại - Vui lòng đăng nhập lại');
        }

        print('🌐 [DEBUG] Calling getUserHomes API...');
        final response = await _homeRepository.getUserHomes(token);

        print('📡 [DEBUG] API Response:');
        print('  Success: ${response.success}');
        print('  Message: ${response.message}');
        print('  Data: ${response.data}');
        print('  Error: ${response.error}');
        if (response.success) {
          _homes = response.data!;
          if (_homes.isNotEmpty && _currentHome == null) {
            _currentHome = _homes.first;
          }
          print('✅ [DEBUG] Loaded ${_homes.length} homes successfully');
          safeNotifyListeners();
          return true;
}
          throw Exception(response.message);
      }, errorPrefix: 'Lỗi khi tải danh sách nhà');

      return result ?? false;
    } finally {
      _isLoadingHomes = false;
    }
  }

  /// Tạo home mới
  /// Note: Backend enforces 1 home per user constraint
  Future<bool> createHome({
    required String name,
    required String address,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final request = HomeRequest(name: name, address: address);
      final response = await _homeRepository.createHome(token, request);

      if (response.success) {
        _successMessage = 'Tạo nhà thành công';
        safeNotifyListeners();
        // Reload homes only if not recently loaded
        final now = DateTime.now();
        if (_lastLoadTime == null || now.difference(_lastLoadTime!) > _debounceDelay) {
          await loadHomes();
        }
        return true;
      } else {
        // Handle specific constraint error
        final message = response.message ?? 'Lỗi không xác định';
        if (message.contains('mỗi tài khoản chỉ được tạo một nhà duy nhất') ||
            message.contains('only one home per user')) {
          throw Exception('Mỗi tài khoản chỉ được tạo một nhà duy nhất');
        }
        throw Exception(message);
      }
    }, errorPrefix: 'Lỗi khi tạo nhà');

    return result ?? false;
  }

  /// Cập nhật home
  Future<bool> updateHome({
    required int homeId,
    required String name,
    required String address,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final request = HomeRequest(name: name, address: address);
      final response = await _homeRepository.updateHome(token, homeId, request);
      
      if (response.success) {
        _successMessage = 'Cập nhật nhà thành công';
        safeNotifyListeners();
        // Reload homes only if not recently loaded
        if (response.success) {
            _successMessage = 'Cập nhật nhà thành công';
            await loadHomes(); 
            safeNotifyListeners();
            return true;
          }
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi cập nhật nhà');

    return result ?? false;
  }

  /// Load chi tiết home
  Future<bool> loadHomeDetail(int homeId) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final homeResponse = await _homeRepository.getHome(token, homeId);

      if (homeResponse.success) {
        _currentHome = homeResponse.data!;

        // Load users and areas in parallel
        final usersResponse = await _homeRepository.getHomeUsers(token, homeId);
        final areasResponse = await _homeRepository.getHomeAreas(token, homeId);

        _homeUsers = usersResponse.success ? usersResponse.data : null;
        _homeAreas = areasResponse.success ? areasResponse.data : null;

        safeNotifyListeners();
        return true;
      } else {
        throw Exception(homeResponse.message);
      }
    }, errorPrefix: 'Lỗi khi tải chi tiết nhà');

    return result ?? false;
  }

  /// Load chi tiết home với statistics
  Future<bool> loadHomeDetailWithStats(int homeId) async {
    // Debounce mechanism to prevent continuous API calls
    final now = DateTime.now();
    if (_lastLoadTime != null && now.difference(_lastLoadTime!) < _debounceDelay) {
      print('🔍 [DEBUG] loadHomeDetailWithStats - Debounced, skipping...');
      return true; // Return success to avoid error states
    }
    _lastLoadTime = now;

    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _homeRepository.getHomeDetail(token, homeId);

      if (response.success) {
        _homeDetail = response.data!;
        print('🔍 [DEBUG] loadHomeDetailWithStats - Statistics loaded:');
        print('  - Total devices: ${_homeDetail?.statistics.totalDevices}');
        if (_homeDetail?.statistics.devicesByArea != null) {
          for (var area in _homeDetail!.statistics.devicesByArea!) {
            print('  - Area "${area.areaName}": ${area.totalCount} devices');
          }
        }

        // Update current home from detail response
        _currentHome = Home(
          homeId: _homeDetail!.homeId,
          name: _homeDetail!.name,
          address: _homeDetail!.address,
          createdAt: _homeDetail!.createdAt,
          updatedAt: _homeDetail!.updatedAt,
          areas: _homeDetail!.areas,
        );

        // Merge areas data intelligently
        if (_homeAreas == null || _homeAreas!.isEmpty) {
          // Nếu chưa có areas, dùng từ detail
          print('🔍 [DEBUG] Using areas from HomeDetail: ${_homeDetail!.areas.length} areas');
          _homeAreas = _homeDetail!.areas;
        } else if (_homeDetail!.areas.isNotEmpty) {
          // Nếu đã có areas, merge với detail để có thống kê đầy đủ
          print('🔍 [DEBUG] Merging areas data - existing: ${_homeAreas!.length}, detail: ${_homeDetail!.areas.length}');

          // Tạo map để merge dữ liệu
          final Map<int, Area> mergedAreas = {};

          // Thêm areas hiện tại
          for (var area in _homeAreas!) {
            mergedAreas[area.areaId] = area;
          }

          // Merge với areas từ detail (có thể có thêm thông tin statistics)
          for (var detailArea in _homeDetail!.areas) {
            if (mergedAreas.containsKey(detailArea.areaId)) {
              // Giữ area hiện tại nhưng có thể cập nhật thông tin khác nếu cần
              mergedAreas[detailArea.areaId] = mergedAreas[detailArea.areaId]!;
            } else {
              // Thêm area mới từ detail
              mergedAreas[detailArea.areaId] = detailArea;
            }
          }

          _homeAreas = mergedAreas.values.toList();
          print('🔍 [DEBUG] Merged result: ${_homeAreas!.length} areas');
        } else {
          print('🔍 [DEBUG] Keeping existing areas: ${_homeAreas?.length ?? 0} areas');
        }

        safeNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi tải chi tiết nhà với thống kê');

    return result ?? false;
  }

  /// Load areas của home
  Future<bool> loadHomeAreas(int homeId) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _homeRepository.getHomeAreas(token, homeId);
      
      if (response.success) {
        _homeAreas = response.data!;
        safeNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi tải danh sách khu vực');

    return result ?? false;
  }

  /// Tạo area mới
  Future<bool> createArea({
    required int homeId,
    required String areaName,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final request = AreaRequest(name: areaName);
      final response = await _homeRepository.createArea(token, homeId, request.toJson());
      
      if (response.success) {
        _successMessage = 'Tạo khu vực thành công';
        markStatsForRefresh(); // Mark stats for refresh since area count changed
        safeNotifyListeners();
        // Reload areas
        await loadHomeAreas(homeId);
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi tạo khu vực');

    return result ?? false;
  }

  /// Load devices trong area
  Future<bool> loadAreaDevices(int homeId, int areaId) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _homeRepository.getAreaDevices(token, homeId, areaId);
      
      if (response.success) {
        _areaDevices = response.data!;
        print('✅ Loaded ${_areaDevices.length} devices for area $areaId');

        // Force clear any previous error messages when successful
        clearError();

        safeNotifyListeners();
        return true;
      } else {
        throw Exception(response.message ?? 'Không thể tải danh sách thiết bị');
      }
    }, errorPrefix: 'Lỗi kết nối khi tải thiết bị');

    return result ?? false;
  }

  /// Load tất cả devices của home
  Future<bool> loadHomeDevices(int homeId) async {
    print('🚀 [DEBUG] loadHomeDevices STARTED - homeId: $homeId');
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _homeRepository.getHomeDevices(token, homeId);

      print('🔍 [DEBUG] loadHomeDevices - Response:');
      print('  Success: ${response.success}');
      print('  Data: ${response.data}');
      print('  Message: ${response.message}');

      if (response.success) {
        _homeDevices = response.data!;
        print('✅ [DEBUG] loadHomeDevices - Loaded ${_homeDevices.length} devices');
        for (var device in _homeDevices) {
          print('  - Device: ${device.name} (ID: ${device.id}, Type: ${device.type})');
        }

        // FALLBACK: If no devices from API, try to get from areas
        if (_homeDevices.isEmpty && _homeDetail != null) {
          print('⚠️ [DEBUG] No devices from API, trying to get from areas...');
          final devicesFromAreas = <Device>[];
          for (var area in _homeDetail!.areas) {
            if (area.devicesList.isNotEmpty) {
              devicesFromAreas.addAll(area.devicesList);
              print('  - Found ${area.devicesList.length} devices in area: ${area.name}');
            }
          }
          _homeDevices = devicesFromAreas;
          print('✅ [DEBUG] Fallback: Loaded ${_homeDevices.length} devices from areas');
        }

        safeNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi tải danh sách thiết bị');

    return result ?? false;
  }

  /// Thêm device vào area
  Future<bool> addDeviceToArea({
    required int homeId,
    required int areaId,
    required int deviceId,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _homeRepository.addDeviceToArea(
        token,
        homeId,
        areaId,
        deviceId,
      );
      
      if (response.success) {
        _successMessage = 'Thêm thiết bị vào khu vực thành công';
        safeNotifyListeners();

        // Reload data in background without blocking UI (with debounce protection)
        safeAsync(() async {
          await Future.wait([
            loadAreaDevices(homeId, areaId),
            loadHomeDevices(homeId),
            // Skip loadHomeDetailWithStats to prevent continuous calls
          ]);
        });

        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi thêm thiết bị vào khu vực');

    return result ?? false;
  }

  /// Thêm user vào home
  Future<bool> addUserToHome({
    required int homeId,
    required String userEmail,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final request = HomeUserRequest(userEmail: userEmail);
      final response = await _homeRepository.addUserToHome(token, homeId, request);
      
      if (response.success) {
        _successMessage = 'Thêm thành viên thành công';
        safeNotifyListeners();
        // Reload home detail to get updated users
        await loadHomeDetail(homeId);
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi thêm thành viên');

    return result ?? false;
  }

  /// Clear success message
  void clearSuccessMessage() {
    _successMessage = null;
    delayedNotifyListeners();
  }

  /// Remove user from home
  Future<bool> removeUserFromHome({
    required int homeId,
    required int userId,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _homeRepository.removeUserFromHome(token, homeId, userId);

      if (response.success) {
        _successMessage = 'Xóa thành viên thành công';
        safeNotifyListeners();
        // Reload home detail to get updated users
        await loadHomeDetail(homeId);
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi xóa thành viên');

    return result ?? false;
  }

  /// Load home users
  Future<bool> loadHomeUsers(int homeId) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _homeRepository.getHomeUsers(token, homeId);

      if (response.success) {
        _homeUsers = response.data!;
        safeNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi tải danh sách thành viên');

    return result ?? false;
  }

  /// Change user role (promote or demote)
  Future<bool> promoteUser({
    required int homeId,
    required int userId,
    required String newRole,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      // Convert role name to role ID (based on backend constants)
      int roleId;
      switch (newRole.toUpperCase()) {
        case 'OWNER':
          roleId = 1; // RoleOwner = 1
          break;
        case 'ADMIN':
          roleId = 2; // RoleAdmin = 2
          break;
        case 'MEMBER':
          roleId = 3; // RoleMember = 3
          break;
        default:
          throw Exception('Invalid role: $newRole');
      }

      print('🔍 [DEBUG] promoteUser - Converting role "$newRole" to roleId: $roleId');

      final request = PromoteUserRequest(userId: userId, roleId: roleId);
      final response = await _homeRepository.promoteUser(token, homeId, request);

      if (response.success) {
        _successMessage = 'Thay đổi quyền thành công';
        safeNotifyListeners();
        // Reload home detail to get updated users
        await loadHomeDetail(homeId);
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi thay đổi quyền');

    return result ?? false;
  }

  /// Transfer ownership
  Future<bool> transferOwnership({
    required int homeId,
    required int newOwnerId,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final request = TransferOwnershipRequest(newOwnerId: newOwnerId);
      final response = await _homeRepository.transferOwnership(token, homeId, request);

      if (response.success) {
        _successMessage = 'Chuyển quyền sở hữu thành công';
        safeNotifyListeners();
        // Reload home detail to get updated users
        await loadHomeDetail(homeId);
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi chuyển quyền sở hữu');

    return result ?? false;
  }

  /// Update area
  Future<bool> updateArea({
    required int homeId,
    required int areaId,
    required String areaName,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final request = AreaRequest(name: areaName);
      final response = await _homeRepository.updateArea(token, homeId, areaId, request.toJson());

      if (response.success) {
        _successMessage = 'Cập nhật khu vực thành công';
        safeNotifyListeners();
        // Reload areas
        await loadHomeAreas(homeId);
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi cập nhật khu vực');

    return result ?? false;
  }

  /// Delete area
  Future<bool> deleteArea({
    required int homeId,
    required int areaId,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _homeRepository.deleteArea(token, homeId, areaId);

      if (response.success) {
        _successMessage = 'Xóa khu vực thành công';
        safeNotifyListeners();
        // Reload areas
        await loadHomeAreas(homeId);
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi xóa khu vực');

    return result ?? false;
  }

  /// Remove device from area
  Future<bool> removeDeviceFromArea({
    required int homeId,
    required int areaId,
    required int deviceId,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _homeRepository.removeDeviceFromArea(
        token,
        homeId,
        areaId,
        deviceId,
      );

      if (response.success) {
        _successMessage = 'Xóa thiết bị khỏi khu vực thành công';
        safeNotifyListeners();

        // Reload data in background without blocking UI (with debounce protection)
        safeAsync(() async {
          await Future.wait([
            loadAreaDevices(homeId, areaId),
            loadHomeDevices(homeId),
            // Skip loadHomeDetailWithStats to prevent continuous calls
          ]);
        });

        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi xóa thiết bị khỏi khu vực');

    return result ?? false;
  }

  /// Delete home (OWNER only)
  Future<bool> deleteHome(int homeId) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _homeRepository.deleteHome(token, homeId);

      if (response.success) {
        _successMessage = 'Xóa nhà thành công';
        safeNotifyListeners();
        // Reload homes list only if not recently loaded
        final now = DateTime.now();
        if (_lastLoadTime == null || now.difference(_lastLoadTime!) > _debounceDelay) {
          await loadHomes();
        }
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi xóa nhà');

    return result ?? false;
  }

  /// Thêm device vào home (ADMIN/OWNER)
  Future<bool> addDeviceToHome({
    required int homeId,
    required int deviceId,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _homeRepository.addDeviceToHome(
        token,
        homeId,
        deviceId,
      );

      if (response.success) {
        _successMessage = 'Thêm thiết bị vào nhà thành công';
        safeNotifyListeners();

        // Reload data in background without blocking UI (with debounce protection)
        safeAsync(() async {
          await Future.wait([
            loadHomeDevices(homeId),
            // Skip loadHomeDetailWithStats to prevent continuous calls
          ]);
        });

        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi thêm thiết bị vào nhà');

    return result ?? false;
  }

  /// Xóa device khỏi home (ADMIN/OWNER)
  Future<bool> removeDeviceFromHome({
    required int homeId,
    required int deviceId,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _homeRepository.removeDeviceFromHome(
        token,
        homeId,
        deviceId,
      );

      if (response.success) {
        _successMessage = 'Xóa thiết bị khỏi nhà thành công';
        safeNotifyListeners();

        // Reload data in background without blocking UI (with debounce protection)
        safeAsync(() async {
          await Future.wait([
            loadHomeDevices(homeId),
            // Skip loadHomeDetailWithStats to prevent continuous calls
          ]);
        });

        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi xóa thiết bị khỏi nhà');

    return result ?? false;
  }

  /// Mark that statistics need to be refreshed
  void markStatsForRefresh() {
    _needsStatsRefresh = true;
    print('🔍 [DEBUG] Statistics marked for refresh');
  }

  /// Refresh statistics if needed (called from dashboard)
  Future<bool> refreshStatsIfNeeded(int homeId) async {
    if (!_needsStatsRefresh) {
      print('🔍 [DEBUG] Statistics refresh not needed, skipping...');
      return true;
    }

    print('🔍 [DEBUG] Refreshing statistics due to changes...');
    _needsStatsRefresh = false;
    _lastLoadTime = null; // Reset debounce to allow immediate refresh
    return await loadHomeDetailWithStats(homeId);
  }

  /// Clear current home data
  void clearCurrentHome() {
    _currentHome = null;
    _homeUsers = null;
    _homeAreas = null;
    _areaDevices.clear();
    _homeDevices.clear();
    _needsStatsRefresh = false;
    safeNotifyListeners();
  }
}
