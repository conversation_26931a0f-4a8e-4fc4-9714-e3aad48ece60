import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view_models/permission_view_model.dart';
import '../../../core/models/home_model.dart';
import '../../../core/models/permission_model.dart';
import '../../../core/models/device_model.dart';
import '../../../core/models/area_model.dart';

class AddPermissionPage extends StatefulWidget {
  final Home home;

  const AddPermissionPage({
    super.key,
    required this.home,
  });

  @override
  State<AddPermissionPage> createState() {
    print('🔍 [DEBUG] AddPermissionPage - createState called');
    return _AddPermissionPageState();
  }
}

class _AddPermissionPageState extends State<AddPermissionPage> {
  final _formKey = GlobalKey<FormState>();
  
  String _selectedResourceType = 'device';
  HomeUser? _selectedUser;
  dynamic _selectedResource;
  bool _canView = true;
  bool _canControl = false;
  bool _canConfigure = false;
  bool _isLoading = false;

  @override
  void initState() {
    print('🔍 [DEBUG] AddPermissionPage - initState called');
    super.initState();
    // Delay loading to see if UI renders first
    WidgetsBinding.instance.addPostFrameCallback((_) {
      print('🔍 [DEBUG] AddPermissionPage - PostFrameCallback triggered');
      // Add delay to see if UI renders
      Future.delayed(const Duration(seconds: 2), () {
        print('🔍 [DEBUG] AddPermissionPage - Starting delayed data load...');
        _loadData();
      });
    });
  }

  Future<void> _loadData() async {
    try {
      print('🔍 [DEBUG] AddPermissionPage - Loading data...');
      final viewModel = context.read<PermissionViewModel>();

      // Add timeout to loadAllData
      await viewModel.loadAllData(widget.home.homeId).timeout(
        const Duration(seconds: 15),
        onTimeout: () {
          print('⏰ [DEBUG] loadAllData timeout after 15 seconds');
          return false;
        },
      );

      print('✅ [DEBUG] AddPermissionPage - Data loaded successfully');
    } catch (e) {
      print('❌ [DEBUG] AddPermissionPage - Error loading data: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi tải dữ liệu: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    print('🔍 [DEBUG] AddPermissionPage - build method called');

    return Scaffold(
      appBar: AppBar(
        title: const Text('Thêm quyền'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_circle,
              size: 64,
              color: Colors.green,
            ),
            SizedBox(height: 16),
            Text(
              'Add Permission Page',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'Simplified version for debugging',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).pop(true);
        },
        child: const Icon(Icons.save),
      ),
    );
  }
}
            key: _formKey,
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Resource Type Card
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Loại tài nguyên',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: RadioListTile<String>(
                                  title: const Text('Thiết bị'),
                                  value: 'device',
                                  groupValue: _selectedResourceType,
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedResourceType = value!;
                                      _selectedResource = null;
                                    });
                                  },
                                ),
                              ),
                              Expanded(
                                child: RadioListTile<String>(
                                  title: const Text('Khu vực'),
                                  value: 'area',
                                  groupValue: _selectedResourceType,
                                  onChanged: (value) {
                                    setState(() {
                                      _selectedResourceType = value!;
                                      _selectedResource = null;
                                    });
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // User Selection Card
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Chọn người dùng',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 12),
                          DropdownButtonFormField<HomeUser?>(
                            value: _selectedUser,
                            decoration: const InputDecoration(
                              labelText: 'Người dùng',
                              border: OutlineInputBorder(),
                              prefixIcon: Icon(Icons.person),
                            ),
                            validator: (value) {
                              if (value == null) {
                                return 'Vui lòng chọn người dùng';
                              }
                              return null;
                            },
                            items: viewModel.users.map((user) {
                              return DropdownMenuItem<HomeUser?>(
                                value: user,
                                child: Row(
                                  children: [
                                    CircleAvatar(
                                      radius: 12,
                                      backgroundColor: Colors.blue[100],
                                      child: Text(
                                        user.email[0].toUpperCase(),
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: Colors.blue[800],
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Expanded(child: Text(user.email)),
                                  ],
                                ),
                              );
                            }).toList(),
                            onChanged: (value) {
                              setState(() {
                                _selectedUser = value;
                              });
                            },
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Resource Selection Card
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Chọn ${_selectedResourceType == 'device' ? 'thiết bị' : 'khu vực'}',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 12),
                          if (_selectedResourceType == 'device')
                            DropdownButtonFormField<Device?>(
                              value: _selectedResource as Device?,
                              decoration: const InputDecoration(
                                labelText: 'Thiết bị',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.devices),
                              ),
                              validator: (value) {
                                if (value == null) {
                                  return 'Vui lòng chọn thiết bị';
                                }
                                return null;
                              },
                              items: viewModel.devices.map((device) {
                                return DropdownMenuItem<Device?>(
                                  value: device,
                                  child: Row(
                                    children: [
                                      Icon(Icons.device_hub, 
                                           color: Colors.blue[600], size: 20),
                                      const SizedBox(width: 8),
                                      Expanded(child: Text(device.name)),
                                    ],
                                  ),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedResource = value;
                                });
                              },
                            )
                          else
                            DropdownButtonFormField<Area?>(
                              value: _selectedResource as Area?,
                              decoration: const InputDecoration(
                                labelText: 'Khu vực',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.room),
                              ),
                              validator: (value) {
                                if (value == null) {
                                  return 'Vui lòng chọn khu vực';
                                }
                                return null;
                              },
                              items: viewModel.areas.map((area) {
                                return DropdownMenuItem<Area?>(
                                  value: area,
                                  child: Row(
                                    children: [
                                      Icon(Icons.location_on, 
                                           color: Colors.green[600], size: 20),
                                      const SizedBox(width: 8),
                                      Expanded(child: Text(area.displayName)),
                                    ],
                                  ),
                                );
                              }).toList(),
                              onChanged: (value) {
                                setState(() {
                                  _selectedResource = value;
                                });
                              },
                            ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Permissions Card
                  Card(
                    elevation: 2,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Text(
                            'Phân quyền',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 8),
                          const Text(
                            'Chọn các quyền cho người dùng',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                          const SizedBox(height: 16),
                          
                          // View Permission
                          Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[300]!),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: CheckboxListTile(
                              title: const Text('Xem'),
                              subtitle: const Text('Có thể xem thông tin'),
                              value: _canView,
                              onChanged: (value) {
                                setState(() {
                                  _canView = value ?? false;
                                  if (!_canView) {
                                    _canControl = false;
                                    _canConfigure = false;
                                  }
                                });
                              },
                              secondary: Icon(Icons.visibility, 
                                           color: _canView ? Colors.blue : Colors.grey),
                            ),
                          ),
                          const SizedBox(height: 8),
                          
                          // Control Permission
                          Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[300]!),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: CheckboxListTile(
                              title: const Text('Điều khiển'),
                              subtitle: const Text('Có thể điều khiển thiết bị'),
                              value: _canControl,
                              onChanged: _canView ? (value) {
                                setState(() {
                                  _canControl = value ?? false;
                                  if (!_canControl) {
                                    _canConfigure = false;
                                  }
                                });
                              } : null,
                              secondary: Icon(Icons.control_camera, 
                                           color: _canControl ? Colors.orange : Colors.grey),
                            ),
                          ),
                          const SizedBox(height: 8),
                          
                          // Configure Permission
                          Container(
                            decoration: BoxDecoration(
                              border: Border.all(color: Colors.grey[300]!),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: CheckboxListTile(
                              title: const Text('Cấu hình'),
                              subtitle: const Text('Có thể cấu hình thiết bị'),
                              value: _canConfigure,
                              onChanged: _canControl ? (value) {
                                setState(() {
                                  _canConfigure = value ?? false;
                                });
                              } : null,
                              secondary: Icon(Icons.settings, 
                                           color: _canConfigure ? Colors.red : Colors.grey),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 24),

                  // Action Buttons
                  Row(
                    children: [
                      Expanded(
                        child: OutlinedButton(
                          onPressed: _isLoading ? null : () {
                            Navigator.of(context).pop();
                          },
                          style: OutlinedButton.styleFrom(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: const Text('Hủy'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: _isLoading ? null : _addPermission,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.blue,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: _isLoading
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                  ),
                                )
                              : const Text('Thêm quyền'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _addPermission() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final request = PermissionRequest(
      userId: _selectedUser!.userId,
      deviceId: _selectedResourceType == 'device' ? _selectedResource!.id : null,
      areaId: _selectedResourceType == 'area' ? _selectedResource!.id : null,
      canView: _canView,
      canControl: _canControl,
      canConfigure: _canConfigure,
    );

    bool success;
    if (_selectedResourceType == 'device') {
      success = await context.read<PermissionViewModel>().addDevicePermission(
        homeId: widget.home.homeId,
        request: request,
      );
    } else {
      success = await context.read<PermissionViewModel>().addAreaPermission(
        homeId: widget.home.homeId,
        request: request,
      );
    }

    setState(() {
      _isLoading = false;
    });

    if (mounted) {
      if (success) {
        Navigator.of(context).pop(true); // Return true to indicate success
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Thêm quyền thành công'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Thêm quyền thất bại'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}