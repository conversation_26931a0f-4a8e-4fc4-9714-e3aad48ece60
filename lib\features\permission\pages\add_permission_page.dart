import 'package:fe_flutter/core/models/area_model.dart';
import 'package:fe_flutter/core/models/device_model.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view_models/permission_view_model.dart';
import '../../../core/models/home_model.dart';
import '../../../core/models/permission_model.dart';

class WorkingAddPermissionPage extends StatefulWidget {
  final Home home;

  const WorkingAddPermissionPage({
    super.key,
    required this.home,
  });

  @override
  State<WorkingAddPermissionPage> createState() => _WorkingAddPermissionPageState();
}

class _WorkingAddPermissionPageState extends State<WorkingAddPermissionPage> {
  final _formKey = GlobalKey<FormState>();
  String _selectedResourceType = 'device';
  HomeUser? _selectedUser;
  dynamic _selectedResource;
  bool _canView = true;
  bool _canControl = false;
  bool _canConfigure = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    print('🔍 [DEBUG] WorkingAddPermissionPage - initState');
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      print('🔍 [DEBUG] WorkingAddPermissionPage - PostFrameCallback');
      _loadData();
    });
  }

  Future<void> _loadData() async {
    try {
      print('🔍 [DEBUG] WorkingAddPermissionPage - Loading data...');
      final viewModel = context.read<PermissionViewModel>();
      await viewModel.loadAllData(widget.home.homeId);
      print('✅ [DEBUG] WorkingAddPermissionPage - Data loaded successfully');
    } catch (e) {
      print('❌ [DEBUG] WorkingAddPermissionPage - Error: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    print('🔍 [DEBUG] WorkingAddPermissionPage - build called');
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Thêm quyền'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: Consumer<PermissionViewModel>(
        builder: (context, viewModel, child) {
          print('🔍 [DEBUG] WorkingAddPermissionPage - Consumer builder');
          
          if (viewModel.isLoading) {
            return const Center(
              child: CircularProgressIndicator(),
            );
          }

          return Form(
            key: _formKey,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // Resource Type
                  DropdownButtonFormField<String>(
                    value: _selectedResourceType,
                    decoration: const InputDecoration(
                      labelText: 'Loại tài nguyên',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'device', child: Text('Thiết bị')),
                      DropdownMenuItem(value: 'area', child: Text('Khu vực')),
                    ],
                    onChanged: (value) {
                      setState(() {
                        _selectedResourceType = value!;
                        _selectedResource = null;
                      });
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // User Selection
                  DropdownButtonFormField<HomeUser?>(
                    value: _selectedUser,
                    decoration: const InputDecoration(
                      labelText: 'Người dùng',
                      border: OutlineInputBorder(),
                    ),
                    items: viewModel.users.map((user) {
                      return DropdownMenuItem<HomeUser?>(
                        value: user,
                        child: Text(user.email),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedUser = value;
                      });
                    },
                  ),
                  
                  const SizedBox(height: 16),
                  
                  // Resource Selection
                  if (_selectedResourceType == 'device')
                    DropdownButtonFormField<Device?>(
                      value: _selectedResource as Device?,
                      decoration: const InputDecoration(
                        labelText: 'Thiết bị',
                        border: OutlineInputBorder(),
                      ),
                      items: viewModel.devices.map((device) {
                        return DropdownMenuItem<Device?>(
                          value: device,
                          child: Text(device.name),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedResource = value;
                        });
                      },
                    )
                  else
                    DropdownButtonFormField<Area?>(
                      value: _selectedResource as Area?,
                      decoration: const InputDecoration(
                        labelText: 'Khu vực',
                        border: OutlineInputBorder(),
                      ),
                      items: viewModel.areas.map((area) {
                        return DropdownMenuItem<Area?>(
                          value: area,
                          child: Text(area.displayName),
                        );
                      }).toList(),
                      onChanged: (value) {
                        setState(() {
                          _selectedResource = value;
                        });
                      },
                    ),
                  
                  const SizedBox(height: 16),
                  
                  // Permissions
                  CheckboxListTile(
                    title: const Text('Xem'),
                    value: _canView,
                    onChanged: (value) {
                      setState(() {
                        _canView = value ?? false;
                        if (!_canView) {
                          _canControl = false;
                          _canConfigure = false;
                        }
                      });
                    },
                  ),
                  CheckboxListTile(
                    title: const Text('Điều khiển'),
                    value: _canControl,
                    onChanged: _canView ? (value) {
                      setState(() {
                        _canControl = value ?? false;
                        if (!_canControl) {
                          _canConfigure = false;
                        }
                      });
                    } : null,
                  ),
                  CheckboxListTile(
                    title: const Text('Cấu hình'),
                    value: _canConfigure,
                    onChanged: _canControl ? (value) {
                      setState(() {
                        _canConfigure = value ?? false;
                      });
                    } : null,
                  ),
                  
                  const Spacer(),
                  
                  // Save Button
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: _isLoading ? null : _addPermission,
                      child: _isLoading
                          ? const CircularProgressIndicator()
                          : const Text('Thêm quyền'),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Future<void> _addPermission() async {
    print('🔍 [DEBUG] _addPermission called');
    print('🔍 [DEBUG] Selected user: ${_selectedUser?.email}');
    print('🔍 [DEBUG] Selected resource type: $_selectedResourceType');
    print('🔍 [DEBUG] Selected resource: ${_selectedResource?.name ?? _selectedResource?.displayName}');
    print('🔍 [DEBUG] Permissions - View: $_canView, Control: $_canControl, Configure: $_canConfigure');

    if (_selectedUser == null || _selectedResource == null) {
      print('❌ [DEBUG] Missing user or resource');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Vui lòng chọn đầy đủ thông tin'),
          backgroundColor: Colors.red,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final viewModel = context.read<PermissionViewModel>();
    final request = PermissionRequest(
      userId: _selectedUser!.userId,
      deviceId: _selectedResourceType == 'device' ? _selectedResource!.id : null,
      areaId: _selectedResourceType == 'area' ? _selectedResource!.id : null,
      canView: _canView,
      canControl: _canControl,
      canConfigure: _canConfigure,
    );

    print('🔍 [DEBUG] Permission request created:');
    print('  userId: ${request.userId}');
    print('  deviceId: ${request.deviceId}');
    print('  areaId: ${request.areaId}');
    print('  canView: ${request.canView}');
    print('  canControl: ${request.canControl}');
    print('  canConfigure: ${request.canConfigure}');

    bool success;
    try {
      if (_selectedResourceType == 'device') {
        print('🔍 [DEBUG] Calling addDevicePermission...');
        success = await viewModel.addDevicePermission(
          homeId: widget.home.homeId,
          request: request,
        ).timeout(
          const Duration(seconds: 15),
          onTimeout: () {
            print(' [DEBUG] addDevicePermission timeout');
            return false;
          },
        );
        print(' [DEBUG] addDevicePermission result: $success');
      } else {
        print(' [DEBUG] Calling addAreaPermission...');
        success = await viewModel.addAreaPermission(
          homeId: widget.home.homeId,
          request: request,
        ).timeout(
          const Duration(seconds: 15),
          onTimeout: () {
            print('⏰ [DEBUG] addAreaPermission timeout');
            return false;
          },
        );
        print('🔍 [DEBUG] addAreaPermission result: $success');
      }
    } catch (e) {
      print('❌ [DEBUG] Error calling API: $e');
      success = false;
    }

    setState(() {
      _isLoading = false;
    });

    if (mounted) {
      if (success) {
        print(' [DEBUG] Permission added successfully');
        Navigator.of(context).pop(true);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Thêm quyền thành công'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        print(' [DEBUG] Permission add failed');
        final errorMessage = viewModel.errorMessage ?? 'Thêm quyền thất bại';
        print(' [DEBUG] Error message: $errorMessage');
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}
