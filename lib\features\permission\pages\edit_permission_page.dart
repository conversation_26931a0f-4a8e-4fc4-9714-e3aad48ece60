import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view_models/permission_view_model.dart';
import '../../../core/models/home_model.dart';
import '../../../core/models/permission_model.dart';

class EditPermissionPage extends StatefulWidget {
  final Home home;
  final Permission permission;

  const EditPermissionPage({
    super.key,
    required this.home,
    required this.permission,
  });

  @override
  State<EditPermissionPage> createState() => _EditPermissionPageState();
}

class _EditPermissionPageState extends State<EditPermissionPage> {
  late bool _canView;
  late bool _canControl;
  late bool _canConfigure;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _canView = widget.permission.canView;
    _canControl = widget.permission.canControl;
    _canConfigure = widget.permission.canConfigure;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Sử<PERSON> quyền'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Consumer<PermissionViewModel>(
        builder: (context, viewModel, child) {
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Resource Info Card
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: Colors.blue[50],
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Icon(
                                widget.permission.resourceType == 'device' 
                                    ? Icons.devices 
                                    : Icons.room,
                                color: Colors.blue[600],
                              ),
                            ),
                            const SizedBox(width: 12),
                            const Text(
                              'Thông tin quyền',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        
                        // Resource Name
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[200]!),
                          ),
                          child: Row(
                            children: [
                              Icon(
                                widget.permission.resourceType == 'device' 
                                    ? Icons.device_hub 
                                    : Icons.location_on,
                                color: Colors.grey[600],
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    widget.permission.resourceType == 'device' 
                                        ? 'Thiết bị' 
                                        : 'Khu vực',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  Text(
                                    _getResourceName(viewModel),
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        
                        const SizedBox(height: 12),
                        
                        // User Info
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.grey[50],
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(color: Colors.grey[200]!),
                          ),
                          child: Row(
                            children: [
                              CircleAvatar(
                                radius: 16,
                                backgroundColor: Colors.blue[100],
                                child: Text(
                                  _getUserName(viewModel)[0].toUpperCase(),
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.blue[800],
                                  ),
                                ),
                              ),
                              const SizedBox(width: 8),
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Người dùng',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  Text(
                                    _getUserName(viewModel),
                                    style: const TextStyle(
                                      fontSize: 14,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Permissions Card
                Card(
                  elevation: 2,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Phân quyền',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const SizedBox(height: 8),
                        const Text(
                          'Chỉnh sửa các quyền cho người dùng',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey,
                          ),
                        ),
                        const SizedBox(height: 16),
                        
                        // View Permission
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: CheckboxListTile(
                            title: const Text('Xem'),
                            subtitle: const Text('Có thể xem thông tin'),
                            value: _canView,
                            onChanged: (value) {
                              setState(() {
                                _canView = value ?? false;
                                if (!_canView) {
                                  _canControl = false;
                                  _canConfigure = false;
                                }
                              });
                            },
                            secondary: Icon(Icons.visibility, 
                                         color: _canView ? Colors.blue : Colors.grey),
                          ),
                        ),
                        const SizedBox(height: 8),
                        
                        // Control Permission
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: CheckboxListTile(
                            title: const Text('Điều khiển'),
                            subtitle: const Text('Có thể điều khiển thiết bị'),
                            value: _canControl,
                            onChanged: _canView ? (value) {
                              setState(() {
                                _canControl = value ?? false;
                                if (!_canControl) {
                                  _canConfigure = false;
                                }
                              });
                            } : null,
                            secondary: Icon(Icons.control_camera, 
                                         color: _canControl ? Colors.orange : Colors.grey),
                          ),
                        ),
                        const SizedBox(height: 8),
                        
                        // Configure Permission
                        Container(
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey[300]!),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: CheckboxListTile(
                            title: const Text('Cấu hình'),
                            subtitle: const Text('Có thể cấu hình thiết bị'),
                            value: _canConfigure,
                            onChanged: _canControl ? (value) {
                              setState(() {
                                _canConfigure = value ?? false;
                              });
                            } : null,
                            secondary: Icon(Icons.settings, 
                                         color: _canConfigure ? Colors.red : Colors.grey),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                const SizedBox(height: 24),

                // Action Buttons
                Row(
                  children: [
                    Expanded(
                      child: OutlinedButton(
                        onPressed: _isLoading ? null : () {
                          Navigator.of(context).pop();
                        },
                        style: OutlinedButton.styleFrom(
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text('Hủy'),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: ElevatedButton(
                        onPressed: _isLoading ? null : _updatePermission,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue,
                          foregroundColor: Colors.white,
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: _isLoading
                            ? const SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                ),
                              )
                            : const Text('Cập nhật'),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  String _getResourceName(PermissionViewModel viewModel) {
    if (widget.permission.resourceType == 'device') {
      try {
        final device = viewModel.devices.firstWhere(
          (d) => d.id == widget.permission.resourceId,
        );
        return device.name;
      } catch (e) {
        return 'Unknown Device';
      }
    } else {
      try {
        final area = viewModel.areas.firstWhere(
          (a) => a.id == widget.permission.resourceId,
        );
        return area.displayName;
      } catch (e) {
        return 'Unknown Area';
      }
    }
  }

  String _getUserName(PermissionViewModel viewModel) {
    try {
      final user = viewModel.users.firstWhere(
        (u) => u.userId == widget.permission.userId,
      );
      return user.email;
    } catch (e) {
      return 'Unknown User';
    }
  }

  Future<void> _updatePermission() async {
    setState(() {
      _isLoading = true;
    });

    final request = PermissionRequest(
      userId: widget.permission.userId,
      deviceId: widget.permission.deviceId,
      areaId: widget.permission.areaId,
      canView: _canView,
      canControl: _canControl,
      canConfigure: _canConfigure,
    );

    final success = await context.read<PermissionViewModel>().updatePermission(
      homeId: widget.home.homeId,
      permissionId: widget.permission.permissionId,
      request: request,
    );

    setState(() {
      _isLoading = false;
    });

    if (mounted) {
      if (success) {
        Navigator.of(context).pop();
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cập nhật thành công'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Cập nhật thất bại'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}