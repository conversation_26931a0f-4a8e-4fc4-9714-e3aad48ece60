import 'package:flutter/material.dart';
import '../../../core/models/home_model.dart';

class MinimalAddPermissionPage extends StatefulWidget {
  final Home home;

  const MinimalAddPermissionPage({
    super.key,
    required this.home,
  });

  @override
  State<MinimalAddPermissionPage> createState() {
    print('🔍 [DEBUG] MinimalAddPermissionPage - createState called');
    return _MinimalAddPermissionPageState();
  }
}

class _MinimalAddPermissionPageState extends State<MinimalAddPermissionPage> {
  @override
  void initState() {
    print('🔍 [DEBUG] MinimalAddPermissionPage - initState called');
    super.initState();
    
    WidgetsBinding.instance.addPostFrameCallback((_) {
      print('🔍 [DEBUG] MinimalAddPermissionPage - PostFrameCallback triggered');
    });
  }

  @override
  Widget build(BuildContext context) {
    print('🔍 [DEBUG] MinimalAddPermissionPage - build called');
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Minimal Add Permission'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_circle,
              size: 64,
              color: Colors.green,
            ),
            SizedBox(height: 16),
            Text(
              'Minimal Add Permission Page',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'This is a minimal version without PermissionViewModel',
              style: TextStyle(color: Colors.grey),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          print('🔍 [DEBUG] MinimalAddPermissionPage - Save button pressed');
          Navigator.of(context).pop(true);
        },
        child: const Icon(Icons.save),
      ),
    );
  }
}
