import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view_models/permission_view_model.dart';
import '../../../core/models/home_model.dart';
import '../../../core/models/permission_model.dart';
import 'add_permission_page.dart';
import 'edit_permission_page.dart';
import 'test_add_permission_page.dart';
import 'simple_add_permission_page.dart';

class PermissionManagementPage extends StatefulWidget {
  final Home home;

  const PermissionManagementPage({
    super.key,
    required this.home,
  });

  @override
  State<PermissionManagementPage> createState() => _PermissionManagementPageState();
}

class _PermissionManagementPageState extends State<PermissionManagementPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(_onTabChanged);

    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<PermissionViewModel>().loadAllData(widget.home.homeId);
    });
  }

  void _onTabChanged() {
    if (!_tabController.indexIsChanging) {
      String filter = _tabController.index == 0 ? 'device' : 
                     _tabController.index == 1 ? 'area' : 'all';
      context.read<PermissionViewModel>().changeFilter(filter);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Quyền - ${widget.home.name}'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: 'Thiết bị'),
            Tab(text: 'Khu vực'),
            Tab(text: 'Tất cả'),
          ],
        ),
      ),
      body: Consumer<PermissionViewModel>(
        builder: (context, viewModel, child) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
      if (viewModel.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(viewModel.errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
        viewModel.clearError();
      }
    });
          if (viewModel.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }


          return TabBarView(
            controller: _tabController,
            children: [
              _buildPermissionsList(viewModel, 'device'),
              _buildPermissionsList(viewModel, 'area'),
              _buildPermissionsList(viewModel, 'all'),
            ],
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showAddPermissionDialog(),
        backgroundColor: Colors.blue,
        child: const Icon(Icons.add, color: Colors.white),
      ),
    );
  }

  Widget _buildPermissionsList(PermissionViewModel viewModel, String type) {
    List<Permission> permissions;
    String emptyMessage;

    switch (type) {
      case 'device':
        permissions = viewModel.permissions.where((p) => p.deviceId != null).toList();
        emptyMessage = 'Chưa có quyền thiết bị';
        break;
      case 'area':
        permissions = viewModel.permissions.where((p) => p.areaId != null).toList();
        emptyMessage = 'Chưa có quyền khu vực';
        break;
      default:
        permissions = viewModel.permissions;
        emptyMessage = 'Chưa có quyền nào';
    }

    return RefreshIndicator(
      onRefresh: () => viewModel.loadPermissions(widget.home.homeId, filter: type),
      child: permissions.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.security, size: 48, color: Colors.grey),
                  const SizedBox(height: 16),
                  Text(
                    emptyMessage,
                    style: const TextStyle(color: Colors.grey, fontSize: 16),
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => _showAddPermissionDialog(),
                    child: const Text('Thêm quyền'),
                  ),
                ],
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: permissions.length,
              itemBuilder: (context, index) {
                return _buildPermissionCard(permissions[index], viewModel);
              },
            ),
    );
  }

  Widget _buildPermissionCard(Permission permission, PermissionViewModel viewModel) {
    return Card(
  margin: const EdgeInsets.only(bottom: 8),
  child: Padding(
    padding: const EdgeInsets.all(12),
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          permission.resourceType == 'device' ? Icons.devices : Icons.room,
          color: Colors.blue,
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(_getResourceName(permission, viewModel),
                  style: const TextStyle(fontWeight: FontWeight.bold)),
              const SizedBox(height: 4),
              Text(_getUserName(permission, viewModel),
                  style: const TextStyle(fontSize: 12, color: Colors.grey)),
              const SizedBox(height: 4),
              Wrap(
                spacing: 4,
                runSpacing: 4,
                children: [
                  if (permission.canView)
                    _buildChip("Xem"),
                  if (permission.canControl)
                    _buildChip("Điều khiển"),
                  if (permission.canConfigure)
                    _buildChip("Cấu hình"),
                ],
              ),
            ],
          ),
        ),
        PopupMenuButton<String>(
          onSelected: (value) {
            if (value == 'edit') {
              _showEditPermissionDialog(permission);
            } else if (value == 'delete') {
              _deletePermission(permission, viewModel);
            }
          },
          itemBuilder: (context) => const [
            PopupMenuItem(
              value: 'edit',
              child: Row(
                children: [
                  Icon(Icons.edit),
                  SizedBox(width: 8),
                  Text('Sửa'),
                ],
              ),
            ),
            PopupMenuItem(
              value: 'delete',
              child: Row(
                children: [
                  Icon(Icons.delete, color: Colors.red),
                  SizedBox(width: 8),
                  Text('Xóa', style: TextStyle(color: Colors.red)),
                ],
              ),
            ),
          ],
        ),
      ],
    ),
  ),
);

}
Widget _buildChip(String label) {
  return Chip(
    label: Text(label),
    labelStyle: const TextStyle(fontSize: 10),
    visualDensity: VisualDensity.compact,
    materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
    padding: const EdgeInsets.symmetric(horizontal: 6),
  );
}


  String _getResourceName(Permission permission, PermissionViewModel viewModel) {
    if (permission.resourceType == 'device') {
      try {
        final device = viewModel.devices.firstWhere((d) => d.id == permission.resourceId);
        return device.name;
      } catch (e) {
        return 'Unknown Device';
      }
    } else {
      try {
        final area = viewModel.areas.firstWhere((a) => a.id == permission.resourceId);
        return area.name.toString();
      } catch (e) {
        return 'Unknown Area';
      }
    }
  }

  String _getUserName(Permission permission, PermissionViewModel viewModel) {
    try {
      final user = viewModel.users.firstWhere((u) => u.userId == permission.userId);
      return  user.email;
    } catch (e) {
      return 'Unknown User';
    }
  }

  void _showAddPermissionDialog() async {
    try {
      print('🔍 [DEBUG] Starting navigation to AddPermissionPage...');

      final viewModel = context.read<PermissionViewModel>();
      print('🔍 [DEBUG] Got PermissionViewModel: ${viewModel.runtimeType}');

      final result = await Navigator.of(context).push<bool>(
        MaterialPageRoute(
          builder: (context) {
            print('🔍 [DEBUG] Building AddPermissionPage...');
            return ChangeNotifierProvider.value(
              value: viewModel,
              child: AddPermissionPage(home: widget.home),
            );
          },
        ),
      ).timeout(
        const Duration(seconds: 30),
        onTimeout: () {
          print('⏰ [DEBUG] Navigation timeout - returning null');
          return null;
        },
      );

      print('🔍 [DEBUG] Navigation completed with result: $result');

      // Refresh data if permission was added successfully
      if (result == true && mounted) {
        context.read<PermissionViewModel>().loadAllData(widget.home.homeId);
      }
    } catch (e, stackTrace) {
      print('❌ [DEBUG] Error in _showAddPermissionDialog: $e');
      print('❌ [DEBUG] StackTrace: $stackTrace');

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi mở trang thêm quyền: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showEditPermissionDialog(Permission permission) async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => ChangeNotifierProvider.value(
          value: context.read<PermissionViewModel>(),
          child: EditPermissionPage(
            home: widget.home,
            permission: permission,
          ),
        ),
      ),
    );

    // Refresh data if permission was updated successfully
    if (result == true && mounted) {
      context.read<PermissionViewModel>().loadAllData(widget.home.homeId);
    }
  }

  Future<void> _deletePermission(Permission permission, PermissionViewModel viewModel) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xóa quyền?'),
        content: const Text('Bạn có chắc muốn xóa quyền này không?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Hủy'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await viewModel.removePermission(
        homeId: widget.home.id,
        permissionId: permission.id,
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(success ? 'Xóa thành công' : 'Xóa thất bại'),
            backgroundColor: success ? Colors.green : Colors.red,
          ),
        );
      }
    }
  }
}



