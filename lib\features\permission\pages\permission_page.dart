import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view_models/permission_view_model.dart';
import '../../home/<USER>/home_view_model.dart';
import 'permission_management_page.dart';

class PermissionPage extends StatelessWidget {
  const PermissionPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const PermissionView();
  }
}

class PermissionView extends StatefulWidget {
  const PermissionView({super.key});

  @override
  State<PermissionView> createState() => _PermissionViewState();
}

class _PermissionViewState extends State<PermissionView> {
  // Lưu reference để tránh lỗi context
  late ScaffoldMessengerState _scaffoldMessenger;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Lưu reference an toàn
    _scaffoldMessenger = ScaffoldMessenger.of(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Phân quyền'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        elevation: 0,
        automaticallyImplyLeading: false,
      ),
      body: Consumer<PermissionViewModel>(
        builder: (context, permissionViewModel, child) {
          // Handle success and error messages
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (permissionViewModel.successMessage != null) {
              _showSnackBar(
                permissionViewModel.successMessage!,
                Colors.green,
              );
              permissionViewModel.clearSuccessMessage();
            } else if (permissionViewModel.errorMessage != null) {
              _showSnackBarWithAction(
                permissionViewModel.errorMessage!,
                Colors.red,
                'Đóng',
                () => permissionViewModel.clearError(),
              );
            }
          });

          if (permissionViewModel.isLoading) {
            return const Center(child: CircularProgressIndicator());
          } else if (permissionViewModel.permissions.isNotEmpty) {
            return _buildPermissionContent(context, permissionViewModel);
          } else if (permissionViewModel.errorMessage != null) {
            return _buildErrorState(context, permissionViewModel.errorMessage!);
          }
          return _buildEmptyState(context);
        },
      ),
    );
  }

  Widget _buildPermissionContent(BuildContext context, PermissionViewModel viewModel) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Removed "My Permissions" card section
          
          // Admin Functions (if user is owner/admin)
          Card(
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.orange[50],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          Icons.admin_panel_settings,
                          color: Colors.orange[600],
                          size: 24,
                        ),
                      ),
                      const SizedBox(width: 12),
                      const Text(
                        'Quản lý phân quyền',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  
                  // Manage Device Permissions
                  ListTile(
                    leading: Icon(Icons.devices, color: Colors.blue[600]),
                    title: const Text('Phân quyền thiết bị'),
                    subtitle: const Text('Quản lý quyền truy cập thiết bị'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      // Navigate to permission management
                      final homeViewModel = context.read<HomeViewModel>();
                      if (homeViewModel.currentHome != null) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => PermissionManagementPage(
                              home: homeViewModel.currentHome!,
                            ),
                          ),
                        );
                      } else {
                        _showSnackBar(
                          'Vui lòng chọn nhà trước',
                          Colors.red,
                        );
                      }
                    },
                  ),
                  const Divider(),
                  
                  // Manage Area Permissions
                  ListTile(
                    leading: Icon(Icons.room, color: Colors.green[600]),
                    title: const Text('Phân quyền khu vực'),
                    subtitle: const Text('Quản lý quyền truy cập khu vực'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      // Navigate to permission management (area tab)
                      final homeViewModel = context.read<HomeViewModel>();
                      if (homeViewModel.currentHome != null) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => PermissionManagementPage(
                              home: homeViewModel.currentHome!,
                            ),
                          ),
                        ).then((_) {
                          // Switch to area tab after navigation
                          // This will be handled by the tab controller in the page
                        });
                      } else {
                        _showSnackBar(
                          'Vui lòng chọn nhà trước',
                          Colors.red,
                        );
                      }
                    },
                  ),
                  const Divider(),
                  
                  // Detailed Permission Management
                  ListTile(
                    leading: Icon(Icons.admin_panel_settings, color: Colors.purple[600]),
                    title: const Text('Quản lý phân quyền chi tiết'),
                    subtitle: const Text('Phân quyền khu vực và thiết bị'),
                    trailing: const Icon(Icons.arrow_forward_ios, size: 16),
                    onTap: () {
                      // Navigate to permission management (all permissions tab)
                      final homeViewModel = context.read<HomeViewModel>();
                      if (homeViewModel.currentHome != null) {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => PermissionManagementPage(
                              home: homeViewModel.currentHome!,
                            ),
                          ),
                        );
                      } else {
                        _showSnackBar(
                          'Vui lòng chọn nhà trước',
                          Colors.red,
                        );
                      }
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.security,
            size: 100,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Chưa có quyền nào',
            style: TextStyle(
              fontSize: 18,
              color: Colors.grey[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Liên hệ quản trị viên để được cấp quyền',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[500],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 100,
            color: Colors.red[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Có lỗi xảy ra',
            style: TextStyle(
              fontSize: 18,
              color: Colors.red[600],
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              final homeViewModel = context.read<HomeViewModel>();
              if (homeViewModel.currentHome != null) {
                context.read<PermissionViewModel>().loadAllData(homeViewModel.currentHome!.homeId);
              }
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Thử lại'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _showSnackBar(String message, Color backgroundColor) {
    if (mounted) {
      _scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
        ),
      );
    }
  }

  void _showSnackBarWithAction(String message, Color backgroundColor, String actionLabel, VoidCallback onPressed) {
    if (mounted) {
      _scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
          action: SnackBarAction(
            label: actionLabel,
            textColor: Colors.white,
            onPressed: onPressed,
          ),
        ),
      );
    }
  }
}
