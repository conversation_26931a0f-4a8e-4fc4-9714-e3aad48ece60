import 'package:flutter/material.dart';
import '../../../core/models/home_model.dart';

class SimpleAddPermissionPage extends StatelessWidget {
  final Home home;

  const SimpleAddPermissionPage({
    super.key,
    required this.home,
  });

  @override
  Widget build(BuildContext context) {
    print('🔍 [DEBUG] SimpleAddPermissionPage - build called');
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Simple Add Permission'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.security,
              size: 64,
              color: Colors.blue,
            ),
            SizedBox(height: 16),
            Text(
              'Simple Add Permission Page',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            SizedBox(height: 8),
            Text(
              'This page loads without PermissionViewModel',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          print('🔍 [DEBUG] SimpleAddPermissionPage - FAB pressed');
          Navigator.of(context).pop(true);
        },
        child: const Icon(Icons.check),
      ),
    );
  }
}
