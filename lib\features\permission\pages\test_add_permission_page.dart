import 'package:flutter/material.dart';
import '../../../core/models/home_model.dart';

class TestAddPermissionPage extends StatelessWidget {
  final Home home;

  const TestAddPermissionPage({
    super.key,
    required this.home,
  });

  @override
  Widget build(BuildContext context) {
    print('🔍 [DEBUG] TestAddPermissionPage - build called');
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Test Add Permission'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle,
              size: 64,
              color: Colors.green,
            ),
            Sized<PERSON><PERSON>(height: 16),
            Text(
              'Test page loaded successfully!',
              style: TextStyle(fontSize: 18),
            ),
            Sized<PERSON><PERSON>(height: 8),
            Text(
              'Navigation is working.',
              style: TextStyle(color: Colors.grey),
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).pop(true);
        },
        child: const Icon(Icons.check),
      ),
    );
  }
}
