import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/base/base_view_model.dart';
import '../../../core/repositories/permission_repository.dart';
import '../../../core/models/permission_model.dart';
import '../../../core/models/home_model.dart';
import '../../../core/models/device_model.dart';
import '../../../core/models/area_model.dart';

/// ViewModel cho Permission module
/// Thay thế PermissionBloc trong MVVM pattern
class PermissionViewModel extends BaseViewModel {
  final PermissionRepository _permissionRepository;

  PermissionViewModel({required PermissionRepository permissionRepository})
      : _permissionRepository = permissionRepository;

  // States
  List<Permission> _permissions = [];
  List<Permission> _allPermissions = []; // Store all permissions for filtering
  List<HomeUser> _users = [];
  List<Device> _devices = [];
  List<Area> _areas = [];
  String? _successMessage;
  String _currentFilter = 'all';

  // Getters
  List<Permission> get permissions => _permissions;
  List<HomeUser> get users => _users;
  List<Device> get devices => _devices;
  List<Area> get areas => _areas;
  String? get successMessage => _successMessage;

  /// Load permissions cho home với filter
  Future<bool> loadPermissions(int homeId, {String? filter}) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      print('🔍 Loading permissions with filter: $filter');

      final response = await _permissionRepository.getUserPermissions(
        token,
        homeId,
        filter: filter,
      );

      if (response.success) {
        _allPermissions = response.data!;
        _currentFilter = filter ?? 'all';

        // Apply client-side filtering
        _applyFilter();

        print('🔍 Loaded ${_allPermissions.length} total permissions');
        print('🔍 Filtered to ${_permissions.length} permissions for filter: $_currentFilter');

        safeNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi tải danh sách quyền');

    return result ?? false;
  }

  /// Apply filter to permissions
  void _applyFilter() {
    switch (_currentFilter) {
      case 'device':
        _permissions = _allPermissions.where((p) => p.deviceId != null).toList();
        break;
      case 'area':
        _permissions = _allPermissions.where((p) => p.areaId != null).toList();
        break;
      case 'all':
      default:
        _permissions = List.from(_allPermissions);
        break;
    }

    print('🔍 Applied filter $_currentFilter: ${_permissions.length} permissions');
    for (final p in _permissions) {
      print('🔍 Permission: deviceId=${p.deviceId}, areaId=${p.areaId}, resourceType=${p.resourceType}');
    }
  }

  /// Load users của home
  Future<bool> loadUsers(int homeId) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _permissionRepository.getHomeUsers(token, homeId);
      
      if (response.success) {
        _users = response.data!;
        safeNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi tải danh sách users');

    return result ?? false;
  }

  /// Load devices của home
  Future<bool> loadDevices(int homeId) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _permissionRepository.getHomeDevices(token, homeId);
      
      if (response.success) {
        _devices = response.data!;
        safeNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi tải danh sách devices');

    return result ?? false;
  }

  /// Load areas của home
  Future<bool> loadAreas(int homeId) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _permissionRepository.getHomeAreas(token, homeId);
      
      if (response.success) {
        _areas = response.data!;
        safeNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi tải danh sách areas');

    return result ?? false;
  }

  /// Load tất cả data cần thiết cho permission management
  Future<bool> loadAllData(int homeId) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      // Load all data in parallel
      final futures = await Future.wait([
        _permissionRepository.getUserPermissions(token, homeId, filter: 'all'),
        _permissionRepository.getHomeUsers(token, homeId),
        _permissionRepository.getHomeDevices(token, homeId),
        _permissionRepository.getHomeAreas(token, homeId),
      ]);

      final permissionsResponse = futures[0];
      final usersResponse = futures[1];
      final devicesResponse = futures[2];
      final areasResponse = futures[3];

      if (permissionsResponse.success) {
        _allPermissions = permissionsResponse.data! as List<Permission>;
        _currentFilter = 'all';
        _applyFilter();
        print('🔍 PermissionViewModel - Loaded ${_allPermissions.length} permissions');
      }
      if (usersResponse.success) {
        _users = (usersResponse.data! as List).cast<HomeUser>();
      }
      if (devicesResponse.success) {
        _devices = (devicesResponse.data! as List).cast<Device>();
      }
      if (areasResponse.success) {
        _areas = (areasResponse.data! as List).cast<Area>();
      }

      safeNotifyListeners();
      return true;
    }, errorPrefix: 'Lỗi khi tải dữ liệu');

    return result ?? false;
  }

  /// Thêm device permission
  Future<bool> addDevicePermission({
    required int homeId,
    required PermissionRequest request,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _permissionRepository.addDevicePermission(
        token,
        homeId,
        request,
      );
      
      if (response.success) {
        _successMessage = 'Thêm quyền thiết bị thành công';
        safeNotifyListeners();
        // Reload permissions
        await loadPermissions(homeId);
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi thêm quyền thiết bị');

    return result ?? false;
  }

  /// Thêm area permission
  Future<bool> addAreaPermission({
    required int homeId,
    required PermissionRequest request,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _permissionRepository.addAreaPermission(
        token,
        homeId,
        request,
      );
      
      if (response.success) {
        _successMessage = 'Thêm quyền khu vực thành công';
        safeNotifyListeners();
        // Reload permissions
        await loadPermissions(homeId);
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi thêm quyền khu vực');

    return result ?? false;
  }

  /// Cập nhật permission
  Future<bool> updatePermission({
    required int homeId,
    required int permissionId,
    required PermissionRequest request,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _permissionRepository.updatePermission(
        token,
        homeId,
        permissionId,
        request,
      );
      
      if (response.success) {
        _successMessage = 'Cập nhật quyền thành công';
        safeNotifyListeners();
        // Reload permissions
        await loadPermissions(homeId);
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi cập nhật quyền');

    return result ?? false;
  }

  /// Xóa permission
  Future<bool> removePermission({
    required int homeId,
    required int permissionId,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _permissionRepository.removePermission(
        token,
        homeId,
        permissionId,
      );

      if (response.success) {
        _successMessage = 'Xóa quyền thành công';
        safeNotifyListeners();
        // Reload permissions
        await loadPermissions(homeId);
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi xóa quyền');

    return result ?? false;
  }

  /// Alias method for deletePermission (compatibility)
  Future<bool> deletePermission(int permissionId) async {
    // Note: This method needs homeId, but for compatibility we'll try to get it from current context
    // In a real app, you should pass homeId as parameter
    throw UnimplementedError('deletePermission needs homeId parameter. Use removePermission instead.');
  }

  /// Clear success message
  void clearSuccessMessage() {
    _successMessage = null;
    delayedNotifyListeners();
  }

  /// Change filter without reloading from API
  void changeFilter(String filter) {
    _currentFilter = filter;
    _applyFilter();
    safeNotifyListeners();
  }

  /// Clear all data
  void clearData() {
    _permissions.clear();
    _allPermissions.clear();
    _users.clear();
    _devices.clear();
    _areas.clear();
    _successMessage = null;
    _currentFilter = 'all';
    safeNotifyListeners();
  }
}
