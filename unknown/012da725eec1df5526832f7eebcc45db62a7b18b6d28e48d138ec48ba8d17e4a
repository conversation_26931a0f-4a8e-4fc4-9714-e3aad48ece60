import 'package:json_annotation/json_annotation.dart';

part 'home_invitation_model.g.dart';

/// Model for home invitation
@JsonSerializable()
class HomeInvitation {
  @<PERSON><PERSON><PERSON><PERSON>(name: 'invitation_id')
  final int invitationId;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'home_id')
  final int homeId;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'inviter_id')
  final int inviterId;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'email')
  final String email;
  
  @<PERSON>son<PERSON>ey(name: 'role_id')
  final int roleId;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'token')
  final String token;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'status')
  final String status; // pending, accepted, rejected, expired
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'expires_at')
  final DateTime expiresAt;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  
  @Json<PERSON>ey(name: 'accepted_at')
  final DateTime? acceptedAt;
  
  // Additional fields from joins
  @<PERSON>sonKey(name: 'inviter_name')
  final String? inviterName;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'home_name')
  final String? homeName;
  
  @<PERSON><PERSON><PERSON><PERSON>(name: 'role_name')
  final String? roleName;

  HomeInvitation({
    required this.invitationId,
    required this.homeId,
    required this.inviterId,
    required this.email,
    required this.roleId,
    required this.token,
    required this.status,
    required this.expiresAt,
    required this.createdAt,
    this.acceptedAt,
    this.inviterName,
    this.homeName,
    this.roleName,
  });

  /// Check if invitation is still valid
  bool get isValid => status == 'pending' && DateTime.now().isBefore(expiresAt);
  
  /// Check if invitation is expired
  bool get isExpired => DateTime.now().isAfter(expiresAt);
  
  /// Get status display text
  String get statusDisplay {
    switch (status) {
      case 'pending':
        return isExpired ? 'Đã hết hạn' : 'Đang chờ';
      case 'accepted':
        return 'Đã chấp nhận';
      case 'rejected':
        return 'Đã từ chối';
      case 'expired':
        return 'Đã hết hạn';
      default:
        return status;
    }
  }
  
  /// Get status color
  String get statusColor {
    switch (status) {
      case 'pending':
        return isExpired ? 'red' : 'orange';
      case 'accepted':
        return 'green';
      case 'rejected':
        return 'red';
      case 'expired':
        return 'red';
      default:
        return 'grey';
    }
  }
  
  /// Get time remaining until expiration
  String get timeRemaining {
    if (status != 'pending') return '';
    
    final now = DateTime.now();
    if (now.isAfter(expiresAt)) return 'Đã hết hạn';
    
    final diff = expiresAt.difference(now);
    
    if (diff.inDays > 0) {
      return 'Còn ${diff.inDays} ngày';
    } else if (diff.inHours > 0) {
      return 'Còn ${diff.inHours} giờ';
    } else if (diff.inMinutes > 0) {
      return 'Còn ${diff.inMinutes} phút';
    } else {
      return 'Sắp hết hạn';
    }
  }

  factory HomeInvitation.fromJson(Map<String, dynamic> json) => _$HomeInvitationFromJson(json);
  Map<String, dynamic> toJson() => _$HomeInvitationToJson(this);
  
  @override
  String toString() {
    return 'HomeInvitation(id: $invitationId, email: $email, status: $status)';
  }
}

/// Request model for sending invitation
@JsonSerializable()
class InvitationRequest {
  @JsonKey(name: 'email')
  final String email;
  
  @JsonKey(name: 'role_id')
  final int? roleId; // Optional, defaults to 3 (MEMBER)

  InvitationRequest({
    required this.email,
    this.roleId,
  });

  factory InvitationRequest.fromJson(Map<String, dynamic> json) => _$InvitationRequestFromJson(json);
  Map<String, dynamic> toJson() => _$InvitationRequestToJson(this);
}

/// Response model for invitation operations
@JsonSerializable()
class InvitationResponse {
  @JsonKey(name: 'success')
  final bool success;
  
  @JsonKey(name: 'message')
  final String message;
  
  @JsonKey(name: 'data')
  final HomeInvitation? invitation;

  InvitationResponse({
    required this.success,
    required this.message,
    this.invitation,
  });

  factory InvitationResponse.fromJson(Map<String, dynamic> json) => _$InvitationResponseFromJson(json);
  Map<String, dynamic> toJson() => _$InvitationResponseToJson(this);
}

/// Model for invitation list response
@JsonSerializable()
class InvitationListResponse {
  @JsonKey(name: 'success')
  final bool success;
  
  @JsonKey(name: 'message')
  final String message;
  
  @JsonKey(name: 'data')
  final List<HomeInvitation> invitations;

  InvitationListResponse({
    required this.success,
    required this.message,
    required this.invitations,
  });

  factory InvitationListResponse.fromJson(Map<String, dynamic> json) => _$InvitationListResponseFromJson(json);
  Map<String, dynamic> toJson() => _$InvitationListResponseToJson(this);
}

/// Model for accept invitation response
@JsonSerializable()
class AcceptInvitationResponse {
  @JsonKey(name: 'success')
  final bool success;
  
  @JsonKey(name: 'message')
  final String message;
  
  @JsonKey(name: 'data')
  final Map<String, dynamic>? data;

  AcceptInvitationResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory AcceptInvitationResponse.fromJson(Map<String, dynamic> json) => _$AcceptInvitationResponseFromJson(json);
  Map<String, dynamic> toJson() => _$AcceptInvitationResponseToJson(this);
}

/// Constants for invitation system
class InvitationConstants {
  static const String statusPending = 'pending';
  static const String statusAccepted = 'accepted';
  static const String statusRejected = 'rejected';
  static const String statusExpired = 'expired';
  
  static const int roleOwner = 1;
  static const int roleAdmin = 2;
  static const int roleMember = 3;
  
  static const Map<int, String> roleNames = {
    roleOwner: 'OWNER',
    roleAdmin: 'ADMIN',
    roleMember: 'MEMBER',
  };
  
  static const Map<int, String> roleDisplayNames = {
    roleOwner: 'Chủ nhà',
    roleAdmin: 'Quản trị viên',
    roleMember: 'Thành viên',
  };
  
  static const List<String> validStatuses = [
    statusPending,
    statusAccepted,
    statusRejected,
    statusExpired,
  ];
  
  static const List<int> validRoles = [
    roleOwner,
    roleAdmin,
    roleMember,
  ];
  
  /// Get role display name
  static String getRoleDisplayName(int roleId) {
    return roleDisplayNames[roleId] ?? 'Unknown';
  }
  
  /// Get role name
  static String getRoleName(int roleId) {
    return roleNames[roleId] ?? 'UNKNOWN';
  }
  
  /// Check if role is valid
  static bool isValidRole(int roleId) {
    return validRoles.contains(roleId);
  }
  
  /// Check if status is valid
  static bool isValidStatus(String status) {
    return validStatuses.contains(status);
  }
}
