import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/models/automation_models.dart';
import '../../../core/models/device_model.dart';
import '../../../core/models/device_type_model.dart';
import '../../../core/repositories/home_repository.dart';
import '../../../core/repositories/device_repository.dart';
import 'condition_type_selection_widget.dart';

/// Giao diện tạo kịch bản tự động dựa vào cảm biến (chỉ 1 điều kiện)
class SensorAutomationCreatePage extends StatefulWidget {
  final int homeId;
  final String ruleName;
  final String ruleDescription;
  final AutomationCondition? condition;
  final ValueChanged<String> onNameChanged;
  final ValueChanged<String> onDescriptionChanged;
  final ValueChanged<AutomationCondition?> onConditionChanged;

  const SensorAutomationCreatePage({
    super.key,
    required this.homeId,
    required this.ruleName,
    required this.ruleDescription,
    this.condition,
    required this.onNameChanged,
    required this.onDescriptionChanged,
    required this.onConditionChanged,
  });

  @override
  State<SensorAutomationCreatePage> createState() => _SensorAutomationCreatePageState();
}

class _SensorAutomationCreatePageState extends State<SensorAutomationCreatePage> {
  late final TextEditingController _nameController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _valueController;

  List<Device> _availableDevices = [];
  List<DeviceType> _availableDeviceTypes = [];

  bool _isLoadingDevices = false;
  bool _isLoadingDeviceTypes = false;

  DeviceType? _selectedDeviceTypeFilter;
  ConditionType? _selectedConditionType;
  Device? _selectedDevice;
  String _currentOperator = '>';

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.ruleName);
    _descriptionController = TextEditingController(text: widget.ruleDescription);
    _valueController = TextEditingController();

    // Load existing condition if available
    if (widget.condition != null) {
      _currentOperator = widget.condition!.operator;
      _valueController.text = widget.condition!.value;
      _selectedConditionType = _getConditionTypeFromPropertyName(widget.condition!.propertyName);
      // Note: _selectedDevice will be set when devices are loaded
    }

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await Future.wait([
        _loadAvailableDeviceTypes(),
        _loadAvailableDevices(),
      ]);
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _valueController.dispose();
    super.dispose();
  }

  Future<void> _loadAvailableDeviceTypes() async {
    if (!mounted) return;
    setState(() => _isLoadingDeviceTypes = true);
    try {
      final token = (await SharedPreferences.getInstance()).getString('auth_token') ?? '';
      if (token.isEmpty) return;
      final res = await DeviceRepository().getAllDeviceTypes(token);
      if (res.success && res.data != null && mounted) {
        setState(() => _availableDeviceTypes = res.data!);
      }
    } finally {
      if (mounted) setState(() => _isLoadingDeviceTypes = false);
    }
  }

  Future<void> _loadAvailableDevices() async {
    if (!mounted) return;
    setState(() => _isLoadingDevices = true);
    try {
      final token = (await SharedPreferences.getInstance()).getString('auth_token') ?? '';
      if (token.isEmpty) return;
      List<Device> devices;
      if (_selectedDeviceTypeFilter != null) {
        final res = await DeviceRepository().getDevicesByTypeInHome(
          token,
          widget.homeId,
          _selectedDeviceTypeFilter!.deviceTypeId!,
        );
        devices = res.data ?? [];
      } else {
        final res = await HomeRepository().getHomeDevices(token, widget.homeId);
        devices = res.data ?? [];
      }
      if (mounted) {
        setState(() => _availableDevices = devices);

        // Set selected device if we have an existing condition
        if (widget.condition != null && _selectedDevice == null) {
          final existingDevice = devices.firstWhere(
            (d) => d.deviceId == widget.condition!.deviceId,
            orElse: () => devices.isNotEmpty ? devices.first : throw StateError('No devices available'),
          );
          setState(() => _selectedDevice = existingDevice);
        }
      }
    } finally {
      if (mounted) setState(() => _isLoadingDevices = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _header('Kịch bản cảm biến'),
          const SizedBox(height: 20),
          _buildTextField(
            controller: _nameController,
            label: 'Tên Rule',
            hint: 'Ví dụ: Cảm biến chuyển động bật đèn',
            onChanged: widget.onNameChanged,
            requiredField: true,
          ),
          const SizedBox(height: 20),
          _buildTextField(
            controller: _descriptionController,
            label: 'Mô tả (Tùy chọn)',
            hint: 'Mô tả chi tiết về automation rule này',
            onChanged: widget.onDescriptionChanged,
            maxLines: 3,
          ),
          const SizedBox(height: 32),
          _buildSensorConditionForm(),
        ],
      ),
    );
  }

  Widget _header(String text) => Text(
        text,
        style: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: AppTheme.textPrimary,
        ),
      );

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required ValueChanged<String> onChanged,
    int maxLines = 1,
    bool requiredField = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label${requiredField ? ' *' : ''}',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          decoration: _inputDecoration(hint),
          onChanged: onChanged,
        ),
      ],
    );
  }

  InputDecoration _inputDecoration(String hint) => InputDecoration(
        hintText: hint,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.dividerGrey),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.dividerGrey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.primaryBlue),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      );

  Widget _buildDropdownField({
    required String label,
    required String? value,
    required List<Map<String, String>> items,
    required ValueChanged<String?> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: items.any((e) => e['value'] == value) ? value : null,
          decoration: _inputDecoration('Chọn $label'),
          items: items
              .map((item) => DropdownMenuItem<String>(
                    value: item['value'],
                    child: Text(item['label']!),
                  ))
              .toList(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildSensorConditionForm() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.dividerGrey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _header('Điều kiện cảm biến'),
          const SizedBox(height: 16),
          _buildDropdownField(
            label: 'Loại cảm biến',
            value: _selectedConditionType?.name,
            items: [
              {'value': 'temperature', 'label': 'Nhiệt độ'},
              {'value': 'humidity', 'label': 'Độ ẩm'},
              {'value': 'motion', 'label': 'Chuyển động'},
              {'value': 'light', 'label': 'Ánh sáng'},
              {'value': 'device', 'label': 'Trạng thái thiết bị'},
            ],
            onChanged: (val) {
              setState(() {
                _selectedConditionType = val == null
                    ? null
                    : ConditionType.values.firstWhere((e) => e.name == val);
              });
            },
          ),
          const SizedBox(height: 16),
          Builder(
            builder: (context) {
              final filteredDevices = _availableDevices.where((d) {
                if (_selectedConditionType == null) return true;
                // Filter devices based on sensor type if needed
                return true;
              }).toList();
              return _buildDropdownField(
                label: 'Thiết bị cảm biến',
                value: _selectedDevice?.deviceId?.toString(),
                items: filteredDevices.map((d) => {
                  'value': d.deviceId.toString(),
                  'label': '${d.name} (ID: ${d.id})',
                }).toList(),
                onChanged: (val) {
                  setState(() {
                    _selectedDevice = filteredDevices.firstWhere((d) => d.deviceId.toString() == val);
                    _updateCondition();
                  });
                },
              );
            },
          ),
          const SizedBox(height: 16),
          _buildDropdownField(
            label: 'Toán tử',
            value: _currentOperator,
            items: [
              {'value': '>', 'label': 'Lớn hơn (>)'},
              {'value': '<', 'label': 'Nhỏ hơn (<)'},
              {'value': '==', 'label': 'Bằng (==)'},
              {'value': '!=', 'label': 'Khác (!=)'},
            ],
            onChanged: (val) {
              setState(() {
                _currentOperator = val ?? '>';
                _updateCondition();
              });
            },
          ),
          const SizedBox(height: 16),
          _buildTextField(
            controller: _valueController,
            label: 'Giá trị ngưỡng',
            hint: 'Ví dụ: 30 (°C), 70 (%), true/false',
            onChanged: (_) => _updateCondition(),
          ),
          const SizedBox(height: 16),
          if (widget.condition != null)
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.primaryBlue.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppTheme.primaryBlue.withOpacity(0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.sensors, color: AppTheme.primaryBlue),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _buildConditionDescription(),
                      style: const TextStyle(
                        fontWeight: FontWeight.w500,
                        color: AppTheme.textPrimary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  void _updateCondition() {
    if (_selectedConditionType != null &&
        _selectedDevice != null &&
        _valueController.text.isNotEmpty) {
      final condition = AutomationCondition(
        deviceId: _selectedDevice!.deviceId,
        propertyId: _getPropertyIdFromConditionType(_selectedConditionType!),
        propertyName: _getPropertyNameFromConditionType(_selectedConditionType!),
        operator: _currentOperator,
        value: _valueController.text.trim(),
      );
      widget.onConditionChanged(condition);
    } else {
      widget.onConditionChanged(null);
    }
  }

  String _buildConditionDescription() {
    if (widget.condition == null) return '';
    final typeLabel = _getConditionTypeLabel(widget.condition!.propertyName);
    return '$typeLabel ${widget.condition!.operator} ${widget.condition!.value}';
  }

  ConditionType? _getConditionTypeFromPropertyName(String propertyName) {
    switch (propertyName) {
      case 'temperature':
        return ConditionType.temperature;
      case 'humidity':
        return ConditionType.humidity;
      case 'motion_detected':
        return ConditionType.motion;
      case 'light_intensity':
        return ConditionType.light;
      case 'device_state':
        return ConditionType.device;
      default:
        return null;
    }
  }

  int _getPropertyIdFromConditionType(ConditionType type) {
    switch (type) {
      case ConditionType.temperature:
        return 3;
      case ConditionType.humidity:
        return 4;
      case ConditionType.motion:
        return 5;
      case ConditionType.light:
        return 8;
      case ConditionType.device:
        return 7;
      case ConditionType.time:
        return 6;
    }
  }

  String _getPropertyNameFromConditionType(ConditionType type) {
    switch (type) {
      case ConditionType.temperature:
        return 'temperature';
      case ConditionType.humidity:
        return 'humidity';
      case ConditionType.motion:
        return 'motion_detected';
      case ConditionType.light:
        return 'light_intensity';
      case ConditionType.device:
        return 'device_state';
      case ConditionType.time:
        return 'time';
    }
  }

  String _getConditionTypeLabel(String propertyName) {
    switch (propertyName) {
      case 'temperature':
        return 'Nhiệt độ';
      case 'humidity':
        return 'Độ ẩm';
      case 'motion_detected':
        return 'Chuyển động';
      case 'light_intensity':
        return 'Ánh sáng';
      case 'device_state':
        return 'Trạng thái thiết bị';
      case 'time':
        return 'Thời gian';
      default:
        return propertyName;
    }
  }
}