import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/models/automation_models.dart';
import '../../../core/models/device_model.dart';
import '../../../core/models/device_type_model.dart';
import '../../../core/repositories/home_repository.dart';
import '../../../core/repositories/device_repository.dart';
import 'condition_type_selection_widget.dart';

/// Giao diện tạo IF-THEN Rule Engine (nhiều điều kiện với AND/OR logic)
class IfThenRuleCreatePage extends StatefulWidget {
  final int homeId;
  final String ruleName;
  final String ruleDescription;
  final List<AutomationCondition> conditions;
  final String conditionLogic;
  final ValueChanged<String> onNameChanged;
  final ValueChanged<String> onDescriptionChanged;
  final ValueChanged<List<AutomationCondition>> onConditionsChanged;
  final ValueChanged<String> onLogicChanged;

  const IfThenRuleCreatePage({
    super.key,
    required this.homeId,
    required this.ruleName,
    required this.ruleDescription,
    required this.conditions,
    required this.conditionLogic,
    required this.onNameChanged,
    required this.onDescriptionChanged,
    required this.onConditionsChanged,
    required this.onLogicChanged,
  });

  @override
  State<IfThenRuleCreatePage> createState() => _IfThenRuleCreatePageState();
}

class _IfThenRuleCreatePageState extends State<IfThenRuleCreatePage> {
  late final TextEditingController _nameController;
  late final TextEditingController _descriptionController;
  late final TextEditingController _valueController;

  late List<AutomationCondition> _conditions;
  late String _conditionLogic;

  List<Device> _availableDevices = [];
  List<DeviceType> _availableDeviceTypes = [];

  bool _isLoadingDevices = false;
  bool _isLoadingDeviceTypes = false;

  DeviceType? _selectedDeviceTypeFilter;
  ConditionType? _selectedConditionType;
  Device? _selectedDevice;
  String _currentOperator = '>';

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.ruleName);
    _descriptionController = TextEditingController(text: widget.ruleDescription);
    _valueController = TextEditingController();

    _conditions = List<AutomationCondition>.from(widget.conditions);
    _conditionLogic = widget.conditionLogic;

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      await Future.wait([
        _loadAvailableDeviceTypes(),
        _loadAvailableDevices(),
      ]);
    });
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    _valueController.dispose();
    super.dispose();
  }

  Future<void> _loadAvailableDeviceTypes() async {
    if (!mounted) return;
    setState(() => _isLoadingDeviceTypes = true);
    try {
      final token = (await SharedPreferences.getInstance()).getString('auth_token') ?? '';
      if (token.isEmpty) return;
      final res = await DeviceRepository().getAllDeviceTypes(token);
      if (res.success && res.data != null && mounted) {
        setState(() => _availableDeviceTypes = res.data!);
      }
    } finally {
      if (mounted) setState(() => _isLoadingDeviceTypes = false);
    }
  }

  Future<void> _loadAvailableDevices() async {
    if (!mounted) return;
    setState(() => _isLoadingDevices = true);
    try {
      final token = (await SharedPreferences.getInstance()).getString('auth_token') ?? '';
      if (token.isEmpty) return;
      List<Device> devices;
      if (_selectedDeviceTypeFilter != null) {
        final res = await DeviceRepository().getDevicesByTypeInHome(
          token,
          widget.homeId,
          _selectedDeviceTypeFilter!.deviceTypeId!,
        );
        devices = res.data ?? [];
      } else {
        final res = await HomeRepository().getHomeDevices(token, widget.homeId);
        devices = res.data ?? [];
      }
      if (mounted) setState(() => _availableDevices = devices);
    } finally {
      if (mounted) setState(() => _isLoadingDevices = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _header('⚙️ IF-THEN Rule Engine'),
          const SizedBox(height: 20),
          _buildTextField(
            controller: _nameController,
            label: 'Tên Rule',
            hint: 'Ví dụ: Điều kiện phức tạp bật đèn',
            onChanged: widget.onNameChanged,
            requiredField: true,
          ),
          const SizedBox(height: 20),
          _buildTextField(
            controller: _descriptionController,
            label: 'Mô tả (Tùy chọn)',
            hint: 'Mô tả chi tiết về automation rule này',
            onChanged: widget.onDescriptionChanged,
            maxLines: 3,
          ),
          const SizedBox(height: 32),
          _buildConditionForm(),
          const SizedBox(height: 20),
          _buildConditionsList(),
        ],
      ),
    );
  }

  Widget _header(String text) => Text(
        text,
        style: const TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: AppTheme.textPrimary,
        ),
      );

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required ValueChanged<String> onChanged,
    int maxLines = 1,
    bool requiredField = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '$label${requiredField ? ' *' : ''}',
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          maxLines: maxLines,
          decoration: _inputDecoration(hint),
          onChanged: onChanged,
        ),
      ],
    );
  }

  InputDecoration _inputDecoration(String hint) => InputDecoration(
        hintText: hint,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.dividerGrey),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.dividerGrey),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppTheme.primaryBlue),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      );

  Widget _buildDropdownField({
    required String label,
    required String? value,
    required List<Map<String, String>> items,
    required ValueChanged<String?> onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppTheme.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: items.any((e) => e['value'] == value) ? value : null,
          decoration: _inputDecoration('Chọn $label'),
          items: items
              .map((item) => DropdownMenuItem<String>(
                    value: item['value'],
                    child: Text(item['label']!),
                  ))
              .toList(),
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildConditionForm() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.dividerGrey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(child: _header('Thêm điều kiện mới')),
              if (_conditions.isNotEmpty)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryBlue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    'Logic: $_conditionLogic',
                    style: const TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryBlue,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          _buildDropdownField(
            label: 'Loại điều kiện',
            value: _selectedConditionType?.name,
            items: [
              {'value': 'temperature', 'label': 'Nhiệt độ'},
              {'value': 'humidity', 'label': 'Độ ẩm'},
              {'value': 'motion', 'label': 'Chuyển động'},
              {'value': 'light', 'label': 'Ánh sáng'},
              {'value': 'device', 'label': 'Trạng thái thiết bị'},
              {'value': 'time', 'label': 'Thời gian'},
            ],
            onChanged: (val) {
              setState(() {
                _selectedConditionType = val == null
                    ? null
                    : ConditionType.values.firstWhere((e) => e.name == val);
              });
            },
          ),
          const SizedBox(height: 16),
          Builder(
            builder: (context) {
              final filteredDevices = _availableDevices.where((d) {
                if (_selectedConditionType == null) return true;
                return true;
              }).toList();
              return _buildDropdownField(
                label: 'Thiết bị',
                value: _selectedDevice?.deviceId?.toString(),
                items: filteredDevices.map((d) => {
                  'value': d.deviceId.toString(),
                  'label': '${d.name} (ID: ${d.deviceId})',
                }).toList(),
                onChanged: (val) {
                  setState(() {
                    _selectedDevice = filteredDevices.firstWhere((d) => d.deviceId.toString() == val);
                  });
                },
              );
            },
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                flex: 2,
                child: _buildDropdownField(
                  label: 'Toán tử',
                  value: _currentOperator,
                  items: [
                    {'value': '>', 'label': 'Lớn hơn (>)'},
                    {'value': '<', 'label': 'Nhỏ hơn (<)'},
                    {'value': '==', 'label': 'Bằng (==)'},
                    {'value': '!=', 'label': 'Khác (!=)'},
                  ],
                  onChanged: (val) => setState(() => _currentOperator = val ?? '>'),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                flex: 3,
                child: _buildTextField(
                  controller: _valueController,
                  label: 'Giá trị',
                  hint: 'Ví dụ: 30, true, false',
                  onChanged: (_) {},
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ElevatedButton.icon(
            icon: const Icon(Icons.add),
            label: const Text('Thêm điều kiện'),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryBlue,
              foregroundColor: Colors.white,
            ),
            onPressed: _canAddCondition() ? _addConditionToList : null,
          ),
        ],
      ),
    );
  }

  Widget _buildConditionsList() {
    if (_conditions.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: AppTheme.surfaceWhite,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: AppTheme.dividerGrey),
        ),
        child: const Center(
          child: Column(
            children: [
              Icon(Icons.rule, size: 48, color: AppTheme.dividerGrey),
              SizedBox(height: 8),
              Text(
                'Chưa có điều kiện nào',
                style: TextStyle(
                  fontSize: 16,
                  color: AppTheme.textSecondary,
                ),
              ),
              SizedBox(height: 4),
              Text(
                'Thêm ít nhất 1 điều kiện để tạo rule',
                style: TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondary,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.surfaceWhite,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppTheme.dividerGrey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(child: _header('Danh sách điều kiện')),
              if (_conditions.length > 1)
                DropdownButton<String>(
                  value: _conditionLogic,
                  items: const [
                    DropdownMenuItem(value: 'AND', child: Text('AND')),
                    DropdownMenuItem(value: 'OR', child: Text('OR')),
                  ],
                  onChanged: (val) {
                    setState(() {
                      _conditionLogic = val!;
                      // Update all conditions except the first one
                      for (int i = 1; i < _conditions.length; i++) {
                        _conditions[i].logicalOperator = _conditionLogic;
                      }
                    });
                    widget.onLogicChanged(_conditionLogic);
                    widget.onConditionsChanged(_conditions);
                  },
                  underline: Container(),
                  style: const TextStyle(fontWeight: FontWeight.bold, color: AppTheme.primaryBlue),
                ),
            ],
          ),
          const SizedBox(height: 16),
          ..._conditions.asMap().entries.map((entry) {
            final idx = entry.key;
            final condition = entry.value;
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (idx > 0)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 8),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryBlue,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            condition.logicalOperator ?? 'AND',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                _conditionTile(condition, idx),
                const SizedBox(height: 8),
              ],
            );
          }),
        ],
      ),
    );
  }

  Widget _conditionTile(AutomationCondition condition, int index) => Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppTheme.primaryBlue.withOpacity(0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppTheme.primaryBlue.withOpacity(0.3)),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${_getConditionTypeLabel(condition.propertyName)} ${condition.operator} ${condition.value}',
                    style: const TextStyle(
                      fontWeight: FontWeight.w500,
                      color: AppTheme.textPrimary,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Device ID: ${condition.deviceId}',
                    style: const TextStyle(
                      fontSize: 12,
                      color: AppTheme.textSecondary,
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              icon: const Icon(Icons.delete, color: AppTheme.errorRed, size: 20),
              onPressed: () => _removeCondition(condition),
            ),
          ],
        ),
      );

  bool _canAddCondition() =>
      _selectedConditionType != null &&
      _selectedDevice != null &&
      _valueController.text.isNotEmpty;

  void _addConditionToList() {
    final condition = AutomationCondition(
      deviceId: _selectedDevice!.deviceId,
      propertyId: _getPropertyIdFromConditionType(_selectedConditionType!),
      propertyName: _getPropertyNameFromConditionType(_selectedConditionType!),
      operator: _currentOperator,
      value: _valueController.text.trim(),
      logicalOperator: _conditions.isEmpty ? null : _conditionLogic,
    );

    setState(() {
      _conditions.add(condition);
      _selectedConditionType = null;
      _selectedDevice = null;
      _currentOperator = '>';
      _valueController.clear();
    });

    widget.onConditionsChanged(_conditions);
  }

  void _removeCondition(AutomationCondition condition) {
    setState(() {
      _conditions.remove(condition);
      // Reset logical operator for the first condition
      if (_conditions.isNotEmpty) {
        _conditions.first.logicalOperator = null;
      }
    });
    widget.onConditionsChanged(_conditions);
  }

  int _getPropertyIdFromConditionType(ConditionType type) {
    switch (type) {
      case ConditionType.temperature:
        return 3;
      case ConditionType.humidity:
        return 4;
      case ConditionType.motion:
        return 5;
      case ConditionType.light:
        return 8;
      case ConditionType.device:
        return 7;
      case ConditionType.time:
        return 6;
    }
  }

  String _getPropertyNameFromConditionType(ConditionType type) {
    switch (type) {
      case ConditionType.temperature:
        return 'temperature';
      case ConditionType.humidity:
        return 'humidity';
      case ConditionType.motion:
        return 'motion_detected';
      case ConditionType.light:
        return 'light_intensity';
      case ConditionType.device:
        return 'device_state';
      case ConditionType.time:
        return 'time';
    }
  }

  String _getConditionTypeLabel(String propertyName) {
    switch (propertyName) {
      case 'temperature':
        return 'Nhiệt độ';
      case 'humidity':
        return 'Độ ẩm';
      case 'motion_detected':
        return 'Chuyển động';
      case 'light_intensity':
        return 'Ánh sáng';
      case 'device_state':
        return 'Trạng thái thiết bị';
      case 'time':
        return 'Thời gian';
      default:
        return propertyName;
    }
  }
}