// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_invitation_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

HomeInvitation _$HomeInvitationFromJson(Map<String, dynamic> json) =>
    HomeInvitation(
      invitationId: (json['invitation_id'] as num).toInt(),
      homeId: (json['home_id'] as num).toInt(),
      inviterId: (json['inviter_id'] as num).toInt(),
      email: json['email'] as String,
      roleId: (json['role_id'] as num).toInt(),
      token: json['token'] as String,
      status: json['status'] as String,
      expiresAt: DateTime.parse(json['expires_at'] as String),
      createdAt: DateTime.parse(json['created_at'] as String),
      acceptedAt: json['accepted_at'] == null
          ? null
          : DateTime.parse(json['accepted_at'] as String),
      inviterName: json['inviter_name'] as String?,
      homeName: json['home_name'] as String?,
      roleName: json['role_name'] as String?,
    );

Map<String, dynamic> _$HomeInvitationToJson(HomeInvitation instance) =>
    <String, dynamic>{
      'invitation_id': instance.invitationId,
      'home_id': instance.homeId,
      'inviter_id': instance.inviterId,
      'email': instance.email,
      'role_id': instance.roleId,
      'token': instance.token,
      'status': instance.status,
      'expires_at': instance.expiresAt.toIso8601String(),
      'created_at': instance.createdAt.toIso8601String(),
      'accepted_at': instance.acceptedAt?.toIso8601String(),
      'inviter_name': instance.inviterName,
      'home_name': instance.homeName,
      'role_name': instance.roleName,
    };

InvitationRequest _$InvitationRequestFromJson(Map<String, dynamic> json) =>
    InvitationRequest(
      email: json['email'] as String,
      roleId: (json['role_id'] as num?)?.toInt(),
    );

Map<String, dynamic> _$InvitationRequestToJson(InvitationRequest instance) =>
    <String, dynamic>{
      'email': instance.email,
      'role_id': instance.roleId,
    };

InvitationResponse _$InvitationResponseFromJson(Map<String, dynamic> json) =>
    InvitationResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      invitation: json['data'] == null
          ? null
          : HomeInvitation.fromJson(json['data'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$InvitationResponseToJson(InvitationResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.invitation,
    };

InvitationListResponse _$InvitationListResponseFromJson(
        Map<String, dynamic> json) =>
    InvitationListResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      invitations: (json['data'] as List<dynamic>)
          .map((e) => HomeInvitation.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$InvitationListResponseToJson(
        InvitationListResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.invitations,
    };

AcceptInvitationResponse _$AcceptInvitationResponseFromJson(
        Map<String, dynamic> json) =>
    AcceptInvitationResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: json['data'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$AcceptInvitationResponseToJson(
        AcceptInvitationResponse instance) =>
    <String, dynamic>{
      'success': instance.success,
      'message': instance.message,
      'data': instance.data,
    };
