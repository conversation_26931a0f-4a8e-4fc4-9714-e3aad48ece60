// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'home_model.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Home _$HomeFromJson(Map<String, dynamic> json) => Home(
      homeId: (json['home_id'] as num).toInt(),
      name: json['name'] as String,
      address: json['address'] as String,
      createdAt: json['created_at'] as String,
      updatedAt: json['updated_at'] as String,
      areas: (json['areas'] as List<dynamic>?)
          ?.map((e) => Area.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$HomeToJson(Home instance) => <String, dynamic>{
      'home_id': instance.homeId,
      'name': instance.name,
      'address': instance.address,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'areas': instance.areas,
    };

HomeRequest _$HomeRequestFromJson(Map<String, dynamic> json) => HomeRequest(
      name: json['name'] as String,
      address: json['address'] as String,
    );

Map<String, dynamic> _$HomeRequestToJson(HomeRequest instance) =>
    <String, dynamic>{
      'name': instance.name,
      'address': instance.address,
    };

HomeUserRequest _$HomeUserRequestFromJson(Map<String, dynamic> json) =>
    HomeUserRequest(
      userEmail: json['email'] as String,
    );

Map<String, dynamic> _$HomeUserRequestToJson(HomeUserRequest instance) =>
    <String, dynamic>{
      'email': instance.userEmail,
    };

HomeUser _$HomeUserFromJson(Map<String, dynamic> json) => HomeUser(
      userId: (json['user_id'] as num).toInt(),
      email: json['email'] as String,
      fullName: json['full_name'] as String,
      phoneNumber: json['phone_number'] as String?,
      avatarUrl: json['avatar_url'] as String?,
      isVerified: json['is_verified'] as bool?,
      createdAt: json['created_at'] as String?,
      updatedAt: json['updated_at'] as String?,
      lastLogin: json['last_login'] as String?,
      role: json['role'] as String?,
      roleName: json['role_name'] as String?,
    );

Map<String, dynamic> _$HomeUserToJson(HomeUser instance) => <String, dynamic>{
      'user_id': instance.userId,
      'email': instance.email,
      'full_name': instance.fullName,
      'phone_number': instance.phoneNumber,
      'avatar_url': instance.avatarUrl,
      'is_verified': instance.isVerified,
      'created_at': instance.createdAt,
      'updated_at': instance.updatedAt,
      'last_login': instance.lastLogin,
      'role': instance.role,
      'role_name': instance.roleName,
    };
