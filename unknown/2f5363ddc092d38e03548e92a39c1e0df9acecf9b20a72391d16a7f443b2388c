import 'package:equatable/equatable.dart';

class ApiResponse<T> extends Equatable {
  final bool status;
  final String message;
  final T? data;
  final String? error;

  const ApiResponse({
    required this.status,
    required this.message,
    this.data,
    this.error,
  });

  // Getter for backward compatibility
  bool get success => status;

  factory ApiResponse.success({
    T? data,
    required String message,
  }) {
    return ApiResponse<T>(
      status: true,
      message: message,
      data: data,
    );
  }

  factory ApiResponse.error({
    required String message,
    String? error,
  }) {
    return ApiResponse<T>(
      status: false,
      message: message,
      error: error,
    );
  }

  @override
  List<Object?> get props => [status, message, data, error];
}
