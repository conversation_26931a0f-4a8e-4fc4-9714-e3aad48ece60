import 'package:flutter/widgets.dart';

/// T<PERSON>t cả ViewModels sẽ kế thừa từ class này
abstract class BaseViewModel extends ChangeNotifier {
  bool _isLoading = false;
  String? _errorMessage;
  String? _message;
  bool _isDisposed = false;

 
  bool get isLoading => _isLoading;

 
  String? get errorMessage => _errorMessage;

 
  String? get message => _message;

  
  bool get isDisposed => _isDisposed;

  
  void setLoading(bool loading) {
    if (_isDisposed) return;
    print('🔍 [DEBUG] setLoading called: $loading (from: ${StackTrace.current.toString().split('\n')[1]})');
    _isLoading = loading;
    delayedNotifyListeners();
  }

  
  void setError(String? error) {
    if (_isDisposed) return;
    _errorMessage = error;
    _message = null; 
    delayedNotifyListeners();
  }


  void setMessage(String? message) {
    if (_isDisposed) return;
    _message = message;
    _errorMessage = null; 
    delayedNotifyListeners();
  }

  
  void clearError() {
    if (_isDisposed) return;
    _errorMessage = null;
    delayedNotifyListeners();
  }

  
  void clearMessage() {
    if (_isDisposed) return;
    _message = null;
    delayedNotifyListeners();
  }

  
  Future<T?> handleAsync<T>(
    Future<T> Function() operation, {
    bool showLoading = true,
    String? errorPrefix,
  }) async {
    try {
      if (showLoading) setLoading(true);
      clearError();
      
      final result = await operation();
      return result;
    } catch (e) {
      final errorMsg = errorPrefix != null 
          ? '$errorPrefix: ${e.toString()}'
          : e.toString();
      setError(errorMsg);
      return null;
    } finally {
      if (showLoading) setLoading(false);
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    super.dispose();
  }

  void safeNotifyListeners() {
    if (!_isDisposed) {
      try {
        notifyListeners();
      } catch (e) {
        print(' Error in notifyListeners: $e');
      }
    }
  }

  void delayedNotifyListeners() {
    if (!_isDisposed) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        safeNotifyListeners();
      });
    }
  }

  Future<T?> safeAsync<T>(Future<T> Function() operation) async {
    if (_isDisposed) {
      print('Skipping async operation - ViewModel disposed');
      return null;
    }

    try {
      return await operation();
    } catch (e) {
      if (!_isDisposed) {
        print(' Error in async operation: $e');
      }
      return null;
    }
  }
}
