import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import '../models/home_model.dart';
import '../models/area_model.dart';
import '../models/device_model.dart';
import '../models/api_response.dart';
import '../config/api_config.dart';

class HomeRepository {
   final String baseUrl = ApiConfig.baseUrl;
  
  // HTTP Status Codes
  static const int _statusOk = 200;
  static const int _statusCreated = 201;
  
  // Timeout duration
  static const Duration _timeoutDuration = Duration(seconds: 60);

  // Helper method để tạo headers
  Map<String, String> _getHeaders(String token) {
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $token',
    };
  }

  // Helper method để xử lý response chung
  Future<ApiResponse<T>> _handleResponse<T>(
    Future<http.Response> responseFeature,
    T Function(Map<String, dynamic> data)? dataParser,
    String errorMessage,
  ) async {
    try {
      final response = await responseFeature.timeout(_timeoutDuration);

      print(' DEBUG - API Response:');
      print('Status Code: ${response.statusCode}');
      print('Response Body: ${response.body}');

      // Kiểm tra response body có null không
      if (response.body.isEmpty) {
        return ApiResponse<T>.error(message: 'Server trả về dữ liệu trống');
      }

      final data = jsonDecode(response.body) as Map<String, dynamic>;

      // Parse and check status field specifically
      print(' DEBUG - Parsed JSON:');
      print('  status: ${data['status']}');
      print('  message: ${data['message']}');
      print('  data type: ${data['data'].runtimeType}');
      if (data['data'] is Map) {
        print('  data keys: ${(data['data'] as Map).keys}');
      } else if (data['data'] is List) {
        print('  data length: ${(data['data'] as List).length}');
      }

      // Check for backend response format: {"status": true/false, "message": "...", "data": {...}}
      final status = data['status'] as bool? ?? data['success'] as bool?;

      if (response.statusCode == _statusOk || response.statusCode == _statusCreated) {
        // If status field exists, use it. If not, consider HTTP 200/201 as success
        if (status == true || (status == null && data.containsKey('message'))) {
          print(' [DEBUG] _handleResponse - Success (status: $status, has message: ${data.containsKey('message')})');
          return ApiResponse<T>.success(
            data: dataParser != null ? dataParser(data) : null,
            message: data['message'] as String? ?? 'Success',
          );
        } else {
          print(' [DEBUG] _handleResponse - Failed (status: $status)');
          return ApiResponse<T>.error(
            message: data['message'] as String? ?? errorMessage,
          );
        }
      } else {
        print(' [DEBUG] _handleResponse - HTTP Error (code: ${response.statusCode})');
        return ApiResponse<T>.error(
          message: data['message'] as String? ?? errorMessage,
        );
      }
    } on SocketException {
      return ApiResponse<T>.error(
        message: 'Không có kết nối internet',
      );
    } on HttpException {
      return ApiResponse<T>.error(
        message: 'Lỗi kết nối server',
      );
    } on FormatException catch (e) {
      return ApiResponse<T>.error(
        message: 'Lỗi định dạng dữ liệu: ${e.message}',
      );
    } catch (e) {
      return ApiResponse<T>.error(
        message: '$errorMessage: ${e.toString()}',
      );
    }
  }

  // Tạo nhà mới
  Future<ApiResponse<Map<String, dynamic>>> createHome(
    String token,
    HomeRequest request,
  ) async {
    return _handleResponse<Map<String, dynamic>>(
      http.post(
        Uri.parse('$baseUrl/api/admin/homes'),
        headers: _getHeaders(token),
        body: jsonEncode(request.toJson()),
      ),
      (data) => data['data'] as Map<String, dynamic>,
      'Tạo nhà thất bại',
    );
  }

  // Lấy danh sách nhà của user
  Future<ApiResponse<List<Home>>> getUserHomes(String token) async {
    return _handleResponse<List<Home>>(
      http.get(
        Uri.parse('$baseUrl/api/admin/homes'),
        headers: _getHeaders(token),
      ),
      (data) {
        final List<dynamic> homesData = data['data'] as List<dynamic>? ?? [];
        return homesData
            .where((json) => json != null)
            .map((json) {
              try {
                return Home.fromJson(json as Map<String, dynamic>);
              } catch (e) {
                print('Error parsing home data: $e');
                return null;
              }
            })
            .where((home) => home != null)
            .cast<Home>()
            .toList();
      },
      'Lấy danh sách nhà thất bại',
    );
  }

  // [ADMIN] Lấy tất cả nhà trong hệ thống (cho admin)
  Future<ApiResponse<List<Home>>> getAllHomes(String token) async {
    return _handleResponse<List<Home>>(
      http.get(
        Uri.parse('$baseUrl/api/admin/homes'),
        headers: _getHeaders(token),
      ),
      (data) {
        final List<dynamic> homesData = data['data'] as List<dynamic>? ?? [];
        return homesData
            .where((json) => json != null)
            .map((json) {
              try {
                return Home.fromJson(json as Map<String, dynamic>);
              } catch (e) {
                print('Error parsing home data: $e');
                return null;
              }
            })
            .where((home) => home != null)
            .cast<Home>()
            .toList();
      },
      'Lấy danh sách tất cả nhà thất bại',
    );
  }

  // Lấy thông tin chi tiết nhà
  Future<ApiResponse<Home>> getHome(String token, int homeId) async {
    return _handleResponse<Home>(
      http.get(
        Uri.parse('$baseUrl/api/admin/homes/$homeId'),
        headers: _getHeaders(token),
      ),
      (data) => Home.fromJson(data['data'] as Map<String, dynamic>),
      'Lấy thông tin nhà thất bại',
    );
  }

  // Lấy thông tin chi tiết nhà với statistics
  Future<ApiResponse<HomeDetail>> getHomeDetail(String token, int homeId) async {
    print(' [DEBUG] getHomeDetail - URL: $baseUrl/api/admin/homes/$homeId/detail');
    return _handleResponse<HomeDetail>(
      http.get(
        Uri.parse('$baseUrl/api/admin/homes/$homeId/detail'),
        headers: _getHeaders(token),
      ),
      (data) {
        print(' [DEBUG] getHomeDetail - Response data type: ${data.runtimeType}');
        print(' [DEBUG] getHomeDetail - Response data keys: ${data.keys}');
        return HomeDetail.fromJson(data['data'] as Map<String, dynamic>);
      },
      'Lấy thông tin chi tiết nhà thất bại',
    );
  }

  // Cập nhật thông tin nhà
  Future<ApiResponse<void>> updateHome(
    String token,
    int homeId,
    HomeRequest request,
  ) async {
    return _handleResponse<void>(
      http.put(
        Uri.parse('$baseUrl/api/admin/homes/$homeId'),
        headers: _getHeaders(token),
        body: jsonEncode(request.toJson()),
      ),
      null,
      'Cập nhật nhà thất bại',
    );
  }

  // Xóa nhà
  Future<ApiResponse<void>> deleteHome(String token, int homeId) async {
    return _handleResponse<void>(
      http.delete(
        Uri.parse('$baseUrl/api/admin/homes/$homeId'),
        headers: _getHeaders(token),
      ),
      null,
      'Xóa nhà thất bại',
    );
  }

  // Lấy danh sách thành viên trong nhà
  Future<ApiResponse<List<HomeUser>>> getHomeUsers(
    String token, 
    int homeId,
  ) async {
    return _handleResponse<List<HomeUser>>(
      http.get(
        Uri.parse('$baseUrl/api/admin/homes/$homeId/users'),
        headers: _getHeaders(token),
      ),
      (data) {
        final List<dynamic> usersData = data['data'] as List<dynamic>? ?? [];
        return usersData.map((json) => HomeUser.fromJson(json as Map<String, dynamic>)).toList();
      },
      'Lấy danh sách thành viên thất bại',
    );
  }

  // Lấy danh sách khu vực trong nhà
  Future<ApiResponse<List<Area>>> getHomeAreas(
    String token,
    int homeId,
  ) async {
    return _handleResponse<List<Area>>(
      http.get(
        Uri.parse('$baseUrl/api/admin/homes/$homeId/areas'),
        headers: _getHeaders(token),
      ),
      (data) {
        final List<dynamic> areasData = data['data'] as List<dynamic>? ?? [];
        return areasData.map((json) => Area.fromJson(json as Map<String, dynamic>)).toList();
      },
      'Lấy danh sách khu vực thất bại',
    );
  }

Future<ApiResponse<void>> addUserToHome(
  String token,
  int homeId,
  HomeUserRequest request,
) async {
  final requestBody = request.toJson();
  
  // LOG CHI TIẾT ĐỂ SO SÁNH VỚI BE
  print(' DETAILED DEBUG - Add User Request:');
  print('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  print('URL: $baseUrl/api/admin/homes/$homeId/users');
  print('Method: POST');
  print('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  print('Headers:');
  _getHeaders(token).forEach((key, value) {
    print('  $key: $value');
  });
  print('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  print('Request Body Object: $requestBody');
  print('Request Body JSON: ${jsonEncode(requestBody)}');
  print('JSON Length: ${jsonEncode(requestBody).length}');
  print('JSON Bytes: ${utf8.encode(jsonEncode(requestBody))}');
  print('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
  
  // Kiểm tra encoding
  final jsonString = jsonEncode(requestBody);
  print('JSON String: "$jsonString"');
  print('Contains special chars: ${jsonString.contains(RegExp(r'[^\x00-\x7F]'))}');
  
  try {
    final response = await http.post(
      Uri.parse('$baseUrl/api/admin/homes/$homeId/users'),
      headers: _getHeaders(token),
      body: jsonString, // Sử dụng string đã encode
    ).timeout(_timeoutDuration);
    
    print('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    print('RESPONSE:');
    print('Status Code: ${response.statusCode}');
    print('Response Headers: ${response.headers}');
    print('Response Body: ${response.body}');
    print('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
    
    return _handleResponse<void>(
      Future.value(response),
      null,
      'Thêm thành viên thất bại',
    );
  } catch (e) {
    print(' Request Error: $e');
    return ApiResponse<void>.error(message: 'Lỗi kết nối: $e');
  }
}
  // Xóa thành viên khỏi nhà
  Future<ApiResponse<void>> removeUserFromHome(
    String token,
    int homeId,
    int userId,
  ) async {
    return _handleResponse<void>(
      http.delete(
        Uri.parse('$baseUrl/api/admin/homes/$homeId/users/$userId'),
        headers: _getHeaders(token),
      ),
      null,
      'Xóa thành viên thất bại',
    );
  }

  // Promote user role
  Future<ApiResponse<void>> promoteUser(
    String token,
    int homeId,
    PromoteUserRequest request,
  ) async {
    final url = '$baseUrl/api/admin/homes/$homeId/users/promote';
    final requestBody = request.toJson();

    print(' [DEBUG] promoteUser - URL: $url');
    print(' [DEBUG] promoteUser - homeId: $homeId');
    print(' [DEBUG] promoteUser - Request Body: ${jsonEncode(requestBody)}');
    print(' [DEBUG] promoteUser - userId: ${request.userId}, roleId: ${request.roleId}');

    return _handleResponse<void>(
      http.post(
        Uri.parse(url),
        headers: _getHeaders(token),
        body: jsonEncode(requestBody),
      ),
      null,
      'Nâng cấp quyền thất bại',
    );
  }

  // Transfer ownership
  Future<ApiResponse<void>> transferOwnership(
    String token,
    int homeId,
    TransferOwnershipRequest request,
  ) async {
    return _handleResponse<void>(
      http.post(
        Uri.parse('$baseUrl/api/admin/homes/$homeId/transfer-ownership'),
        headers: _getHeaders(token),
        body: jsonEncode(request.toJson()),
      ),
      null,
      'Chuyển quyền sở hữu thất bại',
    );
  }

  // Thêm các method bổ sung cho Area management
  
  // Tạo khu vực mới
  Future<ApiResponse<Area>> createArea(
    String token,
    int homeId,
    Map<String, dynamic> areaData,
  ) async {
    return _handleResponse<Area>(
      http.post(
        Uri.parse('$baseUrl/api/admin/homes/$homeId/area'),
        headers: _getHeaders(token),
        body: jsonEncode(areaData),
      ),
      (data) => Area.fromJson(data['data'] as Map<String, dynamic>),
      'Tạo khu vực thất bại',
    );
  }

  // Cập nhật khu vực
  Future<ApiResponse<void>> updateArea(
    String token, 
    int homeId, 
    int areaId, 
    Map<String, dynamic> areaData,
  ) async {
    return _handleResponse<void>(
      http.put(
        Uri.parse('$baseUrl/api/admin/homes/$homeId/area/$areaId'),
        headers: _getHeaders(token),
        body: jsonEncode(areaData),
      ),
      null,
      'Cập nhật khu vực thất bại',
    );
  }

  // Xóa khu vực
  Future<ApiResponse<void>> deleteArea(
    String token, 
    int homeId, 
    int areaId,
  ) async {
    return _handleResponse<void>(
      http.delete(
        Uri.parse('$baseUrl/api/admin/homes/$homeId/area/$areaId'),
        headers: _getHeaders(token),
      ),
      null,
      'Xóa khu vực thất bại',
    );
  }

  // Lấy thông tin chi tiết khu vực
  Future<ApiResponse<Area>> getArea(
    String token,
    int homeId,
    int areaId,
  ) async {
    return _handleResponse<Area>(
      http.get(
        Uri.parse('$baseUrl/api/admin/homes/$homeId/area/$areaId'),
        headers: _getHeaders(token),
      ),
      (data) => Area.fromJson(data['data'] as Map<String, dynamic>),
      'Lấy thông tin khu vực thất bại',
    );
  }

  // Device Management Methods

  // Lấy tất cả thiết bị trong nhà
  Future<ApiResponse<List<Device>>> getHomeDevices(
    String token,
    int homeId,
  ) async {
    return _handleResponse<List<Device>>(
      http.get(
        Uri.parse('$baseUrl/api/admin/homes/$homeId/devices'),
        headers: _getHeaders(token),
      ),
      (data) {
        print(' DEBUG - Home Devices API Response:');
        print('Full data: $data');

        // Kiểm tra cấu trúc response
        List<dynamic> devicesData;
        if (data.containsKey('data')) {
          devicesData = data['data'] as List<dynamic>? ?? [];
        } else {
          // Nếu response không có wrapper 'data', có thể là array trực tiếp
          devicesData = [];
        }

        print('Devices data: $devicesData');

        return devicesData
            .where((json) => json != null)
            .map((json) {
              try {
                print('Parsing device: $json');
                final device = Device.fromJson(json as Map<String, dynamic>);
                print('Parsed device: ${device.name}, type: ${device.type}, status: ${device.status}');
                return device;
              } catch (e) {
                print(' Error parsing device data: $e');
                print('Device JSON: $json');
                return null;
              }
            })
            .where((device) => device != null)
            .cast<Device>()
            .toList();
      },
      'Lấy danh sách thiết bị thất bại',
    );
  }

  // Lấy thiết bị chưa gán khu vực
  Future<ApiResponse<List<Device>>> getUnassignedDevices(
    String token,
    int homeId,
  ) async {
    return _handleResponse<List<Device>>(
      http.get(
        Uri.parse('$baseUrl/api/homes/$homeId/devices/unassigned'),
        headers: _getHeaders(token),
      ),
      (data) {
        print(' DEBUG - Unassigned Devices API Response:');
        print('Full data: $data');

        List<dynamic> devicesData;
        if (data.containsKey('data')) {
          devicesData = data['data'] as List<dynamic>? ?? [];
        } else {
          devicesData = [];
        }

        return devicesData
            .where((json) => json != null)
            .map((json) {
              try {
                return Device.fromJson(json as Map<String, dynamic>);
              } catch (e) {
                print(' Error parsing unassigned device data: $e');
                return null;
              }
            })
            .where((device) => device != null)
            .cast<Device>()
            .toList();
      },
      'Lấy danh sách thiết bị chưa gán khu vực thất bại',
    );
  }

  // Lấy thiết bị trong khu vực cụ thể
  Future<ApiResponse<List<Device>>> getAreaDevices(
    String token,
    int homeId,
    int areaId,
  ) async {
    final url = '$baseUrl/api/admin/homes/$homeId/areas/$areaId/devices';
    print(' [DEBUG] getAreaDevices - URL: $url');
    print(' [DEBUG] getAreaDevices - homeId: $homeId, areaId: $areaId');

    return _handleResponse<List<Device>>(
      http.get(
        Uri.parse(url),
        headers: _getHeaders(token),
      ),
      (data) {
        print(' DEBUG - Area API Response:');
        print('Full data: $data');
        print('Data type: ${data.runtimeType}');

        try {
          print(' DEBUG - Processing devices list response...');

          // Handle response structure for devices endpoint
          if (data.containsKey('data')) {
            final responseData = data['data'];

            if (responseData is List) {
              // Direct list of devices
              print(' Found devices list, length: ${responseData.length}');

              final devices = responseData
                  .map((json) {
                    try {
                      return Device.fromJson(json as Map<String, dynamic>);
                    } catch (e) {
                      print('Error parsing device: $e');
                      return null;
                    }
                  })
                  .where((device) => device != null)
                  .cast<Device>()
                  .toList();

              print(' Successfully parsed ${devices.length} devices');
              return devices;

            } else if (responseData is Map<String, dynamic>) {
              // Single area object with devices
              print(' Found area object with devices');
              final area = Area.fromJson(responseData);
              print(' Successfully parsed area: ${area.displayName}');
              print(' Devices count: ${area.devicesList.length}');
              return area.devicesList;

            } else {
              print(' Unexpected data type: ${responseData.runtimeType}');
              return <Device>[];
            }
          } else if (data is List) {
            // Direct devices list without wrapper
            print(' Direct devices list without data wrapper');

            final devices = (data as List)
                .map((json) {
                  try {
                    return Device.fromJson(json as Map<String, dynamic>);
                  } catch (e) {
                    print('Error parsing device: $e');
                    return null;
                  }
                })
                .where((device) => device != null)
                .cast<Device>()
                .toList();

            print(' Successfully parsed ${devices.length} devices');
            return devices;
          } else {
            print(' Unexpected response structure');
            return <Device>[];
          }
        } catch (e, stackTrace) {
          print(' Error parsing devices: $e');
          print('Stack trace: $stackTrace');
          print('Response data: $data');

          // Even if parsing fails, return empty list as success
          // The API call was successful, just no devices or parsing issue
          print('⚠️ Returning empty list due to parsing error, but API call was successful');
          return <Device>[];
        }
      },
      'Lấy danh sách thiết bị khu vực thất bại',
    );
  }

  // Thêm thiết bị vào khu vực
  Future<ApiResponse<void>> addDeviceToArea(
    String token,
    int homeId,
    int areaId,
    int deviceId,
  ) async {
    return _handleResponse<void>(
      http.put(
        Uri.parse('$baseUrl/api/admin/homes/$homeId/areas/$areaId/devices/$deviceId'),
        headers: _getHeaders(token),
      ),
      null,
      'Thêm thiết bị vào khu vực thất bại',
    );
  }

  // Xóa thiết bị khỏi khu vực
  Future<ApiResponse<void>> removeDeviceFromArea(
    String token,
    int homeId,
    int areaId,
    int deviceId,
  ) async {
    return _handleResponse<void>(
      http.delete(
        Uri.parse('$baseUrl/api/admin/homes/$homeId/areas/$areaId/devices/$deviceId'),
        headers: _getHeaders(token),
      ),
      null,
      'Xóa thiết bị khỏi khu vực thất bại',
    );
  }

  // Thêm thiết bị vào nhà (ADMIN/OWNER)
  Future<ApiResponse<void>> addDeviceToHome(
    String token,
    int homeId,
    int deviceId,
  ) async {
    return _handleResponse<void>(
      http.post(
        Uri.parse('$baseUrl/api/admin/homes/$homeId/devices'),
        headers: _getHeaders(token),
        body: jsonEncode({'device_id': deviceId}),
      ),
      null,
      'Thêm thiết bị vào nhà thất bại',
    );
  }

  // Xóa thiết bị khỏi nhà (ADMIN/OWNER) - Using DELETE method
  Future<ApiResponse<void>> removeDeviceFromHome(
    String token,
    int homeId,
    int deviceId,
  ) async {
    // Try both possible endpoints to debug
    final url1 = '$baseUrl/api/admin/homes/$homeId/devices/$deviceId';
    final url2 = '$baseUrl/api/admin/homes/$homeId/devices';

    print(' [DEBUG] removeDeviceFromHome - homeId: $homeId, deviceId: $deviceId');
    print(' [DEBUG] removeDeviceFromHome - Trying URL1: $url1');
    print(' [DEBUG] removeDeviceFromHome - Method: DELETE');

    try {
      // First try with device_id in URL path
      var response = await http.delete(
        Uri.parse(url1),
        headers: _getHeaders(token),
      ).timeout(_timeoutDuration);

      print(' [DEBUG] removeDeviceFromHome - URL1 Response Status: ${response.statusCode}');
      print(' [DEBUG] removeDeviceFromHome - URL1 Response Body: ${response.body}');

      // If 404, try alternative approach with device_id in body
      if (response.statusCode == 404) {
        print(' [DEBUG] removeDeviceFromHome - URL1 failed, trying URL2: $url2');
        print(' [DEBUG] removeDeviceFromHome - With body: {"device_id": $deviceId}');

        response = await http.delete(
          Uri.parse(url2),
          headers: _getHeaders(token),
          body: jsonEncode({'device_id': deviceId}),
        ).timeout(_timeoutDuration);

        print(' [DEBUG] removeDeviceFromHome - URL2 Response Status: ${response.statusCode}');
        print(' [DEBUG] removeDeviceFromHome - URL2 Response Body: ${response.body}');
      }

      return _handleResponse<void>(
        Future.value(response),
        null,
        'Xóa thiết bị khỏi nhà thất bại',
      );
    } catch (e) {
      print(' [ERROR] removeDeviceFromHome - Exception: $e');
      return ApiResponse<void>.error(message: 'Lỗi kết nối: $e');
    }
  }
}