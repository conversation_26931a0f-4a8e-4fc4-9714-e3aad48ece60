import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/base/base_view_model.dart';
import '../../../core/repositories/auth_repository.dart';
import '../../../core/models/user_model.dart';

/// ViewModel cho User module
/// Thay thế UserBloc trong MVVM pattern
class UserViewModel extends BaseViewModel {
  final AuthRepository _authRepository;

  UserViewModel({required AuthRepository authRepository})
      : _authRepository = authRepository;

  // States
  User? _user;
  String? _successMessage;

  // Getters
  User? get user => _user;
  String? get successMessage => _successMessage;

  /// Load user profile
  Future<bool> loadProfile() async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final user = await _authRepository.getProfileLegacy(token);
      _user = user;
      safeNotifyListeners();
      return true;
    }, errorPrefix: 'Lỗi khi tải thông tin người dùng');

    return result ?? false;
  }

  /// Update user profile
  Future<bool> updateProfile({
    String? fullName,
    String? phoneNumber,
    String? avatarUrl,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _authRepository.updateProfile(
        token: token,
        fullName: fullName,
        phoneNumber: phoneNumber,
        avatarUrl: avatarUrl,
      );

      if (response.success && response.data != null) {
        _user = response.data!;
      } else {
        throw Exception(response.message);
      }
      _successMessage = 'Cập nhật thông tin thành công!';
      safeNotifyListeners();
      return true;
    }, errorPrefix: 'Lỗi khi cập nhật thông tin');

    return result ?? false;
  }

  /// Clear success message
  void clearSuccessMessage() {
    _successMessage = null;
    delayedNotifyListeners();
  }

  /// Set user data (used by AuthViewModel when user logs in)
  void setUser(User user) {
    _user = user;
    safeNotifyListeners();
  }

  /// Clear user data (used when user logs out)
  void clearUser() {
    _user = null;
    _successMessage = null;
    safeNotifyListeners();
  }
}
