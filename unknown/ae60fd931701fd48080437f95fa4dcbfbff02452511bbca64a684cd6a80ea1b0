import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/models/device_type_model.dart';
import '../view_models/device_view_model.dart';

class DeviceTypesManagementPage extends StatefulWidget {
  const DeviceTypesManagementPage({super.key});

  @override
  State<DeviceTypesManagementPage> createState() => _DeviceTypesManagementPageState();
}

class _DeviceTypesManagementPageState extends State<DeviceTypesManagementPage> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadDeviceTypes();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadDeviceTypes() async {
    if (!mounted) return;
    try {
      final deviceViewModel = context.read<DeviceViewModel>();
      await deviceViewModel.loadManagedDeviceTypes();
    } catch (e) {
      print('Error loading device types: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: const Text(
          'Loại thiết bị',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadDeviceTypes,
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          Expanded(
            child: Consumer<DeviceViewModel>(
              builder: (context, deviceViewModel, child) {
                if (deviceViewModel.isLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                final filteredDeviceTypes = _getFilteredDeviceTypes(deviceViewModel.managedDeviceTypes);

                if (filteredDeviceTypes.isEmpty) {
                  return _buildEmptyState();
                }

                return RefreshIndicator(
                  onRefresh: _loadDeviceTypes,
                  child: ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: filteredDeviceTypes.length,
                    itemBuilder: (context, index) {
                      final deviceType = filteredDeviceTypes[index];
                      return _buildSimpleDeviceTypeCard(deviceType);
                    },
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextField(
        controller: _searchController,
        decoration: InputDecoration(
          hintText: 'Tìm kiếm loại thiết bị...',
          prefixIcon: const Icon(Icons.search),
          suffixIcon: _searchQuery.isNotEmpty
              ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    _searchController.clear();
                    setState(() {
                      _searchQuery = '';
                    });
                  },
                )
              : null,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.grey[300]!),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: BorderRadius.circular(12),
            borderSide: BorderSide(color: Colors.blue[600]!),
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value;
          });
        },
      ),
    );
  }

  Widget _buildSimpleDeviceTypeCard(DeviceType deviceType) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          // Icon
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: deviceType.color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              deviceType.icon,
              color: deviceType.color,
              size: 28,
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Name
          Expanded(
            child: Text(
              deviceType.name,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.device_hub,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            'Không tìm thấy loại thiết bị',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w500,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  List<DeviceType> _getFilteredDeviceTypes(List<DeviceType> deviceTypes) {
    return deviceTypes.where((deviceType) {
      if (_searchQuery.isNotEmpty) {
        final query = _searchQuery.toLowerCase();
        if (!deviceType.name.toLowerCase().contains(query)) {
          return false;
        }
      }
      return true;
    }).toList();
  }
}