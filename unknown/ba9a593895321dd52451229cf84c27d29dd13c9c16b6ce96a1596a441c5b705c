import 'package:fe_flutter/core/models/area_model.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../view_models/home_view_model.dart';
import '../../../core/models/home_model.dart' as home_models;

class HomeDetailPage extends StatefulWidget {
  final home_models.Home home;

  const HomeDetailPage({
    super.key,
    required this.home,
  });

  @override
  State<HomeDetailPage> createState() => _HomeDetailPageState();
}

class _HomeDetailPageState extends State<HomeDetailPage> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<HomeViewModel>().loadHomeDetailWithStats(widget.home.id);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        title: Text('Chi tiết - ${widget.home.name}'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: Consumer<HomeViewModel>(
        builder: (context, viewModel, child) {
          if (viewModel.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          if (viewModel.errorMessage != null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.error_outline, size: 64, color: Colors.red[300]),
                  const SizedBox(height: 16),
                  Text(
                    viewModel.errorMessage!,
                    style: TextStyle(color: Colors.red[600]),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => viewModel.loadHomeDetailWithStats(widget.home.id),
                    child: const Text('Thử lại'),
                  ),
                ],
              ),
            );
          }

          final homeDetail = viewModel.homeDetail;
          if (homeDetail == null) {
            return const Center(
              child: Text('Không có dữ liệu'),
            );
          }

          return RefreshIndicator(
            onRefresh: () => viewModel.loadHomeDetailWithStats(widget.home.id),
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Home Info Card
                  _buildHomeInfoCard(homeDetail),
                  const SizedBox(height: 16),
                  
                  // Statistics Cards
                  _buildStatisticsCards(homeDetail.statistics),
                  const SizedBox(height: 16),
                  
                  // Areas List
                  _buildAreasList(homeDetail.areas),
                  const SizedBox(height: 16),
                  
                  // Devices by Area Chart
                  if (homeDetail.statistics.devicesByArea?.isNotEmpty == true)
                    _buildDevicesByAreaChart(homeDetail.statistics.devicesByArea!),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHomeInfoCard(home_models.HomeDetail homeDetail) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.home, color: Colors.blue[600], size: 28),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        homeDetail.name,
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        homeDetail.address,
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: _getRoleColor(homeDetail.userRoleName),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    homeDetail.userRoleName ?? 'UNKNOWN',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Icon(Icons.access_time, size: 16, color: Colors.grey[500]),
                const SizedBox(width: 4),
                Text(
                  'Tạo: ${_formatDate(homeDetail.createdAt)}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
                const SizedBox(width: 16),
                Icon(Icons.update, size: 16, color: Colors.grey[500]),
                const SizedBox(width: 4),
                Text(
                  'Cập nhật: ${_formatDate(homeDetail.updatedAt)}',
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatisticsCards(home_models.HomeStatistics stats) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Thống kê tổng quan',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Khu vực',
                stats.totalAreas.toString(),
                Icons.room,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Thiết bị',
                stats.totalDevices.toString(),
                Icons.devices,
                Colors.green,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Online',
                stats.onlineDevices.toString(),
                Icons.wifi,
                Colors.green,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildStatCard(
                'Offline',
                stats.offlineDevices.toString(),
                Icons.wifi_off,
                Colors.red,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        _buildStatCard(
          'Thành viên',
          stats.totalUsers.toString(),
          Icons.people,
          Colors.purple,
        ),
      ],
    );
  }

  Widget _buildStatCard(String title, String value, IconData icon, Color color) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    value,
                    style: const TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    title,
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAreasList(List<Area> areas) {
    return Consumer<HomeViewModel>(
      builder: (context, viewModel, child) {
        final homeDetail = viewModel.homeDetail;
        final devicesByArea = homeDetail?.statistics.devicesByArea ?? [];

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Danh sách khu vực',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 12),
            ...areas.map((area) {
              // Tìm thống kê thiết bị cho area này từ statistics
              final areaStats = devicesByArea.firstWhere(
                (stat) => stat.areaId == area.areaId,
                orElse: () => home_models.DeviceAreaStatistic(
                  areaId: area.areaId,
                  areaName: area.displayName,
                  totalCount: area.deviceCount, // fallback to area.deviceCount
                  onlineCount: 0,
                ),
              );

              return Card(
                margin: const EdgeInsets.only(bottom: 8),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: Colors.blue[100],
                    child: Icon(Icons.room, color: Colors.blue[600]),
                  ),
                  title: Text(area.displayName),
                  subtitle: Text('${areaStats.totalCount} thiết bị'),
                  trailing: Icon(Icons.arrow_forward_ios, size: 16, color: Colors.grey[400]),
                  onTap: () {
                    // Navigate to area devices page
                    Navigator.pushNamed(
                      context,
                      '/area-devices',
                      arguments: {
                        'home': widget.home,
                        'area': area,
                      },
                    );
                  },
                ),
              );
            }),
          ],
        );
      },
    );
  }

  Widget _buildDevicesByAreaChart(List<home_models.DeviceAreaStatistic> devicesByArea) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Thiết bị theo khu vực',
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 12),
        ...devicesByArea.map((stat) => Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  flex: 2,
                  child: Text(
                    stat.areaName,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
                Expanded(
                  child: Text('${stat.totalCount} thiết bị'),
                ),
                Expanded(
                  child: Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: Colors.green,
                          borderRadius: BorderRadius.circular(4),
                        ),
                      ),
                      const SizedBox(width: 4),
                      Text('${stat.onlineCount}'),
                    ],
                  ),
                ),
              ],
            ),
          ),
        )),
      ],
    );
  }

  Color _getRoleColor(String? role) {
    switch (role) {
      case 'OWNER':
        return Colors.purple;
      case 'ADMIN':
        return Colors.orange;
      case 'MEMBER':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(String dateStr) {
    try {
      final date = DateTime.parse(dateStr);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateStr;
    }
  }
}
