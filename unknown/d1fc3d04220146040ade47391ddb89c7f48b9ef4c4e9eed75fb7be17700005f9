import 'dart:async';
import 'dart:io';
import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/esp32_device_model.dart';

/// Service for scanning local network to discover ESP32 devices
class LanScannerService {
  static const int _defaultTimeout = 3000;
  static const int _udpBroadcastPort = 8266;

  Future<List<ESP32Device>> scanForESP32DevicesUDPOnly({
    int timeout = _defaultTimeout,
    Function(String)? onProgress,
    List<String>? existingUniqueIdentifiers,
  }) async {
    onProgress?.call('Bắt đầu quét thiết bị ESP32 qua UDP...');

    final udpDevices = await _listenForUDPBroadcasts(timeout, onProgress);

    List<ESP32Device> filteredDevices = udpDevices;
    if (existingUniqueIdentifiers != null && existingUniqueIdentifiers.isNotEmpty) {
      filteredDevices = _filterNewDevices(udpDevices, existingUniqueIdentifiers);
      onProgress?.call('<PERSON><PERSON><PERSON> thiết bị: ${udpDevices.length} tìm thấy, ${filteredDevices.length} thiết bị mới');
    }

    onProgress?.call('Hoàn thành! Tìm thấy ${filteredDevices.length} thiết bị ESP32 mới');

    return filteredDevices;
  }

  Future<List<ESP32Device>> _listenForUDPBroadcasts(int timeout, Function(String)? onProgress) async {
    final List<ESP32Device> devices = [];
    final Set<String> seenUniqueIds = {}; 

    try {
      onProgress?.call('Đang lắng nghe UDP broadcasts trên port $_udpBroadcastPort...');

      final socket = await RawDatagramSocket.bind(InternetAddress.anyIPv4, _udpBroadcastPort);

      socket.broadcastEnabled = true;

      print('UDP Socket bound to port $_udpBroadcastPort');
      print('Socket address: ${socket.address}:${socket.port}');
      print('Listening for broadcasts from ESP32 devices...');

      final completer = Completer<List<ESP32Device>>();
      late StreamSubscription subscription;
      int receivedPackets = 0;

      // Set timeout
      Timer(Duration(milliseconds: timeout), () {
        if (!completer.isCompleted) {
          print('UDP timeout reached. Received $receivedPackets packets, found ${devices.length} unique devices');
          print('Make sure ESP32 is on same network and broadcasting to port $_udpBroadcastPort');
          subscription.cancel();
          socket.close();
          completer.complete(devices);
        }
      });

      subscription = socket.listen((RawSocketEvent event) {
        if (event == RawSocketEvent.read) {
          final datagram = socket.receive();
          if (datagram != null) {
            receivedPackets++;
            print(' [DEBUG] Received UDP packet #$receivedPackets from ${datagram.address.address}:${datagram.port}');

            try {
              final message = String.fromCharCodes(datagram.data);
              print(' [DEBUG] UDP Message: ${message.substring(0, message.length > 200 ? 200 : message.length)}...');

              final device = _parseUDPBroadcast(message, datagram.address.address);

              if (device != null) {
                final uniqueId = device.additionalInfo?['unique_identifier'] as String?;

                if (uniqueId != null && !seenUniqueIds.contains(uniqueId)) {
                  seenUniqueIds.add(uniqueId);
                  devices.add(device);
                  print('Added new device: ${device.deviceName} (${uniqueId}) at ${device.ipAddress}');
                  onProgress?.call('Tìm thấy ${device.deviceName} (${uniqueId}) tại ${device.ipAddress}');
                } else if (uniqueId != null) {
                  print('Duplicate device ignored: ${uniqueId}');
                } else {
                  print('Device without unique_identifier ignored');
                }
              } else {
                print('Failed to parse UDP message');
              }
            } catch (e) {
              print('Error parsing UDP packet: $e');
            }
          }
        }
      });

      return await completer.future;

    } catch (e) {
      print('UDP listening error: $e');
      onProgress?.call('Lỗi khi lắng nghe UDP: $e');
      return devices;
    }
  }

  ESP32Device? _parseUDPBroadcast(String message, String ipAddress) {
    try {
      print('Parsing UDP message from $ipAddress');
      final json = jsonDecode(message);
      print('Parsed JSON keys: ${json.keys.toList()}');

      final name = json['name'] as String?;
      final uniqueId = json['unique_identifier'] as String?;

      if (name == null || uniqueId == null) {
        print('Missing required fields: name=$name, unique_identifier=$uniqueId');
        return null;
      }

      final deviceTypeId = json['device_type_id'] as int?;
      final port = json['network_info']?['port'] as int? ?? 80;
      final isOnline = json['state']?['is_online'] as bool? ?? false;

      String deviceType = 'Unknown Device';
      if (deviceTypeId != null) {
        switch (deviceTypeId) {
          case 1:
            deviceType = 'Smart Light';
            break;
          case 2:
            deviceType = 'Light Sensor';
            break;
          case 3:
            deviceType = 'Camera';
            break;
          case 4:
            deviceType = 'Temperature Sensor';
            break;
          case 5:
            deviceType = 'Smart AC';
            break;
          case 24:
            deviceType = 'Smart Switch';
            break;
          default:
            deviceType = json['device_type']?.toString() ?? 'Unknown Device';
        }
      }

      final device = ESP32Device(
        ipAddress: ipAddress,
        port: port,
        deviceName: name,
        deviceType: deviceType,
        macAddress: json['network_info']?['mac_address'] as String?,
        isOnline: isOnline,
        lastSeen: DateTime.now(),
        capabilities: (json['capabilities'] as List?)?.cast<String>(),
        additionalInfo: {
          'unique_identifier': uniqueId,
          'model': json['model'],
          'device_type_id': json['device_type_id'],
          'connection_type_id': json['connection_type_id'],
          'state': json['state'],
          'network_info': json['network_info'],
        },
      );

      print('Successfully parsed device: $name ($uniqueId)');
      return device;

    } catch (e) {
      print('Error parsing UDP message: $e');
      print('Raw message: $message');
      return null;
    }
  }


  

  

  

  

  
  /// Test connection to a specific ESP32 device
  Future<bool> testConnection(String ip, int port, {int timeout = 5000}) async {
    try {
      final socket = await Socket.connect(ip, port, timeout: Duration(milliseconds: timeout));
      await socket.close();
      return true;
    } catch (e) {
      return false;
    }
  }
  
  /// Send HTTP request to ESP32 device
  Future<Map<String, dynamic>?> sendRequest(
    String ip, 
    int port, 
    String endpoint, {
    String method = 'GET',
    Map<String, dynamic>? body,
    Map<String, String>? headers,
    int timeout = 5000,
  }) async {
    try {
      final uri = Uri.parse('http://$ip:$port$endpoint');
      final defaultHeaders = {
        'Content-Type': 'application/json',
        'User-Agent': 'SmartHome-App',
        ...?headers,
      };
      
      http.Response response;
      
      switch (method.toUpperCase()) {
        case 'GET':
          response = await http.get(uri, headers: defaultHeaders)
              .timeout(Duration(milliseconds: timeout));
          break;
        case 'POST':
          response = await http.post(
            uri, 
            headers: defaultHeaders,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(Duration(milliseconds: timeout));
          break;
        case 'PUT':
          response = await http.put(
            uri, 
            headers: defaultHeaders,
            body: body != null ? jsonEncode(body) : null,
          ).timeout(Duration(milliseconds: timeout));
          break;
        default:
          throw Exception('Unsupported HTTP method: $method');
      }
      
      if (response.statusCode >= 200 && response.statusCode < 300) {
        try {
          return jsonDecode(response.body);
        } catch (e) {
          // Return raw response if not JSON
          return {'raw_response': response.body};
        }
      } else {
        throw Exception('HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      print('Error sending request to $ip:$port$endpoint - $e');
      return null;
    }
  }

  List<ESP32Device> _filterNewDevices(List<ESP32Device> scannedDevices, List<String> existingUniqueIdentifiers) {
    return scannedDevices.where((device) {
      final uniqueId = device.additionalInfo?['unique_identifier'] as String?;
      if (uniqueId == null) return false;

      final isNew = !existingUniqueIdentifiers.contains(uniqueId);
      if (!isNew) {
        print('Filtered out existing device: ${device.deviceName} ($uniqueId)');
      }
      return isNew;
    }).toList();
  }
}
