import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/base/base_view_model.dart';
import '../../../core/repositories/device_repository.dart';
import '../../../core/models/device_model.dart';
import '../../../core/models/device_type_model.dart';

/// ViewModel cho Device module
/// Thay thế DeviceBloc trong MVVM pattern
class DeviceViewModel extends BaseViewModel {
  final DeviceRepository _deviceRepository;

  DeviceViewModel({required DeviceRepository deviceRepository})
      : _deviceRepository = deviceRepository;

  // States
  List<Device> _availableDevices = [];
  List<Device> _allDevices = [];
  List<String> _deviceTypes = [];
  Device? _selectedDevice;
  String? _successMessage;

  // Device Types Management
  List<DeviceType> _managedDeviceTypes = [];
  List<DeviceType> _deviceTypeTemplates = [];
  DeviceType? _selectedDeviceType;

  // Getters
  List<Device> get availableDevices => _availableDevices;
  List<Device> get allDevices => _allDevices;
  List<String> get deviceTypes => _deviceTypes;
  Device? get selectedDevice => _selectedDevice;
  String? get successMessage => _successMessage;

  // Device Types Management Getters
  List<DeviceType> get managedDeviceTypes => _managedDeviceTypes;
  List<DeviceType> get deviceTypeTemplates => _deviceTypeTemplates;
  DeviceType? get selectedDeviceType => _selectedDeviceType;

  /// Load available devices (chưa thuộc nhà nào)
  Future<bool> loadAvailableDevices() async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.getAvailableDevices(token);
      
      if (response.success) {
        _availableDevices = response.data!;
        delayedNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi tải danh sách thiết bị có sẵn');

    return result ?? false;
  }

  /// Load all devices in system
  Future<bool> loadAllDevices() async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.getAllDevices(token);
      
      if (response.success) {
        _allDevices = response.data!;
        delayedNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi tải danh sách thiết bị');

    return result ?? false;
  }

  /// Get device detail
  Future<bool> loadDeviceDetail(int deviceId) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.getDevice(token, deviceId);
      
      if (response.success) {
        _selectedDevice = response.data!;
        delayedNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi tải chi tiết thiết bị');

    return result ?? false;
  }

  /// Register new device to system
  Future<bool> registerDevice(Device device) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.registerDevice(token, device);
      
      if (response.success) {
        _successMessage = 'Đăng ký thiết bị thành công';
        delayedNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi đăng ký thiết bị');

    return result ?? false;
  }

  /// Update device information
  Future<bool> updateDevice({
    required int deviceId,
    required String name,
    String? type,
    Map<String, dynamic>? properties,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final updateData = {
        'name': name,
        if (type != null) 'type': type,
        if (properties != null) 'properties': properties,
      };

      final response = await _deviceRepository.updateDevice(token, deviceId, updateData);
      
      if (response.success) {
        _successMessage = 'Cập nhật thiết bị thành công';
        delayedNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi cập nhật thiết bị');

    return result ?? false;
  }

  /// Delete device from system
  Future<bool> deleteDevice(int deviceId) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.deleteDevice(token, deviceId);
      
      if (response.success) {
        _successMessage = 'Xóa thiết bị thành công';
        delayedNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi xóa thiết bị');

    return result ?? false;
  }

  /// Control device (turn on/off, change properties)
  Future<bool> controlDevice({
    required int homeId,
    required int deviceId,
    required String command,
    required String value,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.controlDevice(
        token,
        homeId,
        deviceId,
        command,
        value,
      );

      if (response.success) {
        _successMessage = 'Điều khiển thiết bị thành công';
        delayedNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi điều khiển thiết bị');

    return result ?? false;
  }

  /// Batch control multiple devices
  Future<bool> batchControlDevices({
    required int homeId,
    required List<Map<String, dynamic>> deviceCommands,
    bool executeSequentially = false,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.batchControlDevices(
        token,
        homeId,
        deviceCommands,
        executeSequentially,
      );

      if (response.success) {
        _successMessage = 'Điều khiển nhiều thiết bị thành công';
        delayedNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi điều khiển nhiều thiết bị');

    return result ?? false;
  }

  /// Load device types
  Future<bool> loadDeviceTypes() async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');
      
      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.getDeviceTypes(token);
      
      if (response.success) {
        _deviceTypes = List<String>.from(response.data!);
        delayedNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi tải danh sách loại thiết bị');

    return result ?? false;
  }

  /// Clear success message
  void clearSuccessMessage() {
    _successMessage = null;
    delayedNotifyListeners();
  }

  /// Clear selected device
  void clearSelectedDevice() {
    _selectedDevice = null;
    delayedNotifyListeners();
  }

  /// Get device status and update current device info
  Future<bool> getDeviceStatus({
    required int homeId,
    required int deviceId,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.getDeviceStatus(
        token,
        homeId,
        deviceId,
      );

      if (response.success && response.data != null) {
        // Parse device data from API response - now returns complete device object
        final deviceData = response.data! as Map<String, dynamic>;

        try {
          // API trả về đầy đủ thông tin thiết bị, chỉ cần parse trực tiếp
          final updatedDevice = Device.fromJson(deviceData);

          // Cập nhật selectedDevice để UI tự động refresh
          _selectedDevice = updatedDevice;
          delayedNotifyListeners();

          print('✅ Device updated successfully: ${updatedDevice.name} - Status: ${updatedDevice.status}');
        } catch (e) {
          print('⚠️ Warning: Could not parse device from status API: $e');
          print('Device data: $deviceData');
        }

        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi lấy trạng thái thiết bị');

    return result ?? false;
  }

  /// Get device history
  Future<List<Map<String, dynamic>>?> getDeviceHistory({
    required int homeId,
    required int deviceId,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.getDeviceHistory(
        token,
        homeId,
        deviceId,
      );

      if (response.success) {
        return response.data;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi lấy lịch sử thiết bị');

    return result;
  }

  /// Get device properties
  Future<Map<String, dynamic>?> getDeviceProperties({
    required int homeId,
    required int deviceId,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.getDeviceProperties(
        token,
        homeId,
        deviceId,
      );

      if (response.success) {
        return response.data;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi lấy thuộc tính thiết bị');

    return result;
  }

  /// Get device commands
  Future<List<Map<String, dynamic>>?> getDeviceCommands({
    required int homeId,
    required int deviceId,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.getDeviceCommands(
        token,
        homeId,
        deviceId,
      );

      if (response.success) {
        return response.data;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi lấy lệnh thiết bị');

    return result;
  }

  /// Update device property
  Future<bool> updateDeviceProperty({
    required int homeId,
    required int deviceId,
    required String property,
    required dynamic value,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.updateDeviceProperty(
        token,
        homeId,
        deviceId,
        property,
        value,
      );

      if (response.success) {
        _successMessage = 'Cập nhật thuộc tính thiết bị thành công';
        delayedNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi cập nhật thuộc tính thiết bị');

    return result ?? false;
  }

  /// Start device monitoring
  Future<bool> startDeviceMonitoring({
    required int homeId,
    required int deviceId,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.startDeviceMonitoring(
        token,
        homeId,
        deviceId,
      );

      if (response.success) {
        _successMessage = 'Bắt đầu giám sát thiết bị thành công';
        delayedNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi bắt đầu giám sát thiết bị');

    return result ?? false;
  }

  /// Clear all data
  void clearAll() {
    _availableDevices.clear();
    _allDevices.clear();
    _deviceTypes.clear();
    _selectedDevice = null;
    _successMessage = null;
    delayedNotifyListeners();
  }

  /// Get device types in home
  Future<List<Map<String, dynamic>>?> getHomeDeviceTypes({
    required int homeId,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.getHomeDeviceTypes(
        token,
        homeId,
      );

      if (response.success) {
        return response.data;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi lấy loại thiết bị trong nhà');

    return result;
  }

  /// Update device info (name, area_id, status)
  Future<bool> updateDeviceInfo({
    required int deviceId,
    String? name,
    int? areaId,
    String? status,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.updateDeviceInfo(
        token,
        deviceId,
        name: name,
        areaId: areaId,
        status: status,
      );

      if (response.success) {
        _successMessage = response.message ?? 'Cập nhật thông tin thiết bị thành công';
        delayedNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi cập nhật thông tin thiết bị');

    return result ?? false;
  }

  // Device Discovery Methods

  /// Scan for new devices
  Future<List<Map<String, dynamic>>?> scanForDevices({
    required List<int> connectionTypes,
    int? scanDuration,
  }) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.scanForDevices(
        token,
        connectionTypes: connectionTypes,
        scanDuration: scanDuration,
      );

      if (response.success) {
        _successMessage = 'Quét thiết bị thành công';
        delayedNotifyListeners();
        return response.data;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi quét thiết bị');

    return result;
  }

  /// Register a discovered device
  Future<Device?> registerDiscoveredDevice(Map<String, dynamic> deviceData) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.registerDiscoveredDevice(token, deviceData);

      if (response.success) {
        _successMessage = 'Đăng ký thiết bị thành công';
        delayedNotifyListeners();
        return response.data;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi đăng ký thiết bị');

    return result;
  }

  /// Get device templates
  Future<List<Map<String, dynamic>>?> getDeviceTemplates() async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.getDeviceTemplates(token);

      if (response.success) {
        _successMessage = 'Lấy templates thành công';
        delayedNotifyListeners();
        return response.data;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi lấy templates');

    return result;
  }

  /// Get connection types
  Future<List<Map<String, dynamic>>?> getConnectionTypes() async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.getConnectionTypes(token);

      if (response.success) {
        _successMessage = 'Lấy connection types thành công';
        delayedNotifyListeners();
        return response.data;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi lấy connection types');

    return result;
  }

  // ==================== DEVICE TYPES MANAGEMENT ====================

  /// Load all managed device types
  Future<bool> loadManagedDeviceTypes() async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.getAllDeviceTypes(token);

      if (response.success) {
        _managedDeviceTypes = response.data ?? [];
        delayedNotifyListeners();
        return true;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi tải danh sách loại thiết bị');

    return result ?? false;
  }

  // Templates loading removed - using local predefined templates only

  /// Get device type detail by ID
  Future<DeviceType?> getDeviceTypeDetail(int deviceTypeId) async {
    final result = await handleAsync(() async {
      final prefs = await SharedPreferences.getInstance();
      final token = prefs.getString('auth_token');

      if (token == null) {
        throw Exception('Token không tồn tại');
      }

      final response = await _deviceRepository.getDeviceTypeDetail(token, deviceTypeId);

      if (response.success) {
        return response.data!;
      } else {
        throw Exception(response.message);
      }
    }, errorPrefix: 'Lỗi khi tải chi tiết loại thiết bị');

    return result;
  }

  // CRUD operations removed - only view operations available

  /// Select device type
  void selectDeviceType(DeviceType? deviceType) {
    _selectedDeviceType = deviceType;
    delayedNotifyListeners();
  }

  /// Clear selected device type
  void clearSelectedDeviceType() {
    _selectedDeviceType = null;
    delayedNotifyListeners();
  }

  /// Get predefined templates (fallback)
  List<DeviceType> getPredefinedTemplates() {
    return DeviceTypeTemplates.getTemplateDeviceTypes();
  }
}
