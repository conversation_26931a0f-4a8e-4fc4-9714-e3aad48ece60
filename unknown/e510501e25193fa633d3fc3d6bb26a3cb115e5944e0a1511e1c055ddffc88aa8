import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../core/models/device_model.dart';
import '../../../core/models/home_model.dart';
import '../view_models/device_view_model.dart';

class DeviceInfoPage extends StatefulWidget {
  final Device device;
  final Home home;

  const DeviceInfoPage({
    super.key,
    required this.device,
    required this.home,
  });

  @override
  State<DeviceInfoPage> createState() => _DeviceInfoPageState();
}

class _DeviceInfoPageState extends State<DeviceInfoPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<Map<String, dynamic>>? _deviceCommands;
  bool _isLoading = false;

  // Lưu reference để tránh lỗi context
  late ScaffoldMessengerState _scaffoldMessenger;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // Lưu reference an toàn
    _scaffoldMessenger = ScaffoldMessenger.of(context);
  }

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _tabController.addListener(_onTabChanged);
  }

  void _onTabChanged() {
    if (_tabController.index == 1 && _deviceCommands == null) {
      // Tab "Lệnh" được chọn và chưa load commands
      _loadDeviceCommands();
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadDeviceCommands() async {
    if (_isLoading) return; // Tránh load nhiều lần

    setState(() => _isLoading = true);

    try {
      final deviceViewModel = context.read<DeviceViewModel>();

      // Load device commands
      final commands = await deviceViewModel.getDeviceCommands(
        homeId: widget.home.id,
        deviceId: widget.device.id,
      );

      if (mounted) {
        setState(() {
          _deviceCommands = commands;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi tải lệnh: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _loadDeviceInfo() async {
    setState(() => _isLoading = true);

    try {
      final deviceViewModel = context.read<DeviceViewModel>();

      // Load device commands
      final commands = await deviceViewModel.getDeviceCommands(
        homeId: widget.home.id,
        deviceId: widget.device.id,
      );

      if (mounted) {
        setState(() {
          _deviceCommands = commands;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Lỗi khi tải thông tin: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Chi tiết - ${widget.device.name}'),
        backgroundColor: Colors.blue[600],
        foregroundColor: Colors.white,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.white,
          labelColor: Colors.white,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(icon: Icon(Icons.info_outline), text: 'Thông tin'),
            Tab(icon: Icon(Icons.history), text: 'Lịch sử lệnh'),
          ],
        ),
        actions: [
          IconButton(
            onPressed: _loadDeviceInfo,
            icon: const Icon(Icons.refresh),
            tooltip: 'Làm mới',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  _showEditDeviceDialog();
                  break;
                case 'move':
                  _showMoveDeviceDialog();
                  break;
                case 'delete':
                  _showDeleteDeviceDialog();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'edit',
                child: Row(
                  children: [
                    Icon(Icons.edit, size: 20),
                    SizedBox(width: 8),
                    Text('Chỉnh sửa'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'move',
                child: Row(
                  children: [
                    Icon(Icons.move_to_inbox, size: 20, color: Colors.blue),
                    SizedBox(width: 8),
                    Text('Chuyển khu vực'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, size: 20, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Xóa thiết bị', style: TextStyle(color: Colors.red)),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildDeviceInfoTab(),
                _buildCommandsTab(_deviceCommands),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _loadDeviceInfo,
        tooltip: 'Tải thông tin thiết bị',
        child: const Icon(Icons.info),
      ),
    );
  }

  Widget _buildDeviceInfoTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Device header card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Container(
                    width: 60,
                    height: 60,
                    decoration: BoxDecoration(
                      color: _getDeviceColor().withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      _getDeviceIcon(),
                      color: _getDeviceColor(),
                      size: 30,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.device.name,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          widget.device.type ?? 'Unknown',
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: widget.device.isOnline ? Colors.green[50] : Colors.red[50],
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: widget.device.isOnline ? Colors.green[200]! : Colors.red[200]!,
                      ),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: widget.device.isOnline ? Colors.green[600] : Colors.red[600],
                            shape: BoxShape.circle,
                          ),
                        ),
                        const SizedBox(width: 6),
                        Text(
                          widget.device.isOnline ? 'Online' : 'Offline',
                          style: TextStyle(
                            color: widget.device.isOnline ? Colors.green[700] : Colors.red[700],
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          
          // Device details
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Thông tin chi tiết',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow('ID thiết bị', widget.device.id.toString()),
                  _buildDetailRow('Loại thiết bị', widget.device.type ?? 'Unknown'),
                  _buildDetailRow('Mô tả', widget.device.description ?? 'Không có mô tả'),
                  _buildDetailRow('Địa chỉ IP', widget.device.ipAddress ?? 'Không có'),
                  _buildDetailRow('Trạng thái', widget.device.status ?? 'Unknown'),
                  _buildDetailRow('Nhà', widget.home.name),
                  _buildDetailRow('Ngày tạo', _formatDate(widget.device.createdAt)),
                  _buildDetailRow('Cập nhật lần cuối', _formatDate(widget.device.updatedAt)),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }



  Widget _buildCommandsTab(List<Map<String, dynamic>>? deviceCommands) {
    if (deviceCommands == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'Đang tải lịch sử lệnh...',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (deviceCommands.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.history, size: 64, color: Colors.orange[400]),
            const SizedBox(height: 16),
            Text(
              'Chưa có lệnh nào được thực thi',
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Header with stats
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.blue[600]!, Colors.blue[400]!],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Icon(Icons.history, color: Colors.white, size: 32),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Lịch sử điều khiển',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${deviceCommands.length} lệnh đã thực thi',
                      style: const TextStyle(
                        color: Colors.white70,
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Text(
                  'ID: ${widget.device.id}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ],
          ),
        ),

        // Command list
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: deviceCommands.length,
            itemBuilder: (context, index) {
              final command = deviceCommands[index];
              final commandName = command['Command'] ?? 'Unknown';
              final value = command['Value'] ?? '';
              final status = command['Status'] ?? 'unknown';
              final createdAt = command['CreatedAt'] ?? '';
              final executedAt = command['ExecutedAt'] ?? '';
              final commandId = command['CommandID'] ?? 0;

              return Card(
                margin: const EdgeInsets.only(bottom: 12),
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header row
                      Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: _getCommandColor(commandName).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Icon(
                              _getCommandIcon(commandName),
                              color: _getCommandColor(commandName),
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  _getCommandDisplayName(commandName),
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                if (value.isNotEmpty) ...[
                                  const SizedBox(height: 4),
                                  Text(
                                    'Giá trị: $value',
                                    style: TextStyle(
                                      color: Colors.grey[600],
                                      fontSize: 14,
                                    ),
                                  ),
                                ],
                              ],
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: _getStatusColor(status).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: _getStatusColor(status).withOpacity(0.3),
                              ),
                            ),
                            child: Text(
                              _getStatusDisplayName(status),
                              style: TextStyle(
                                color: _getStatusColor(status),
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 12),

                      // Time info
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[50],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          children: [
                            Row(
                              children: [
                                Icon(Icons.access_time, size: 16, color: Colors.grey[600]),
                                const SizedBox(width: 8),
                                Text(
                                  'Tạo lệnh: ${_formatDateTime(createdAt)}',
                                  style: TextStyle(
                                    color: Colors.grey[700],
                                    fontSize: 12,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Row(
                              children: [
                                Icon(Icons.check_circle, size: 16, color: Colors.grey[600]),
                                const SizedBox(width: 8),
                                Text(
                                  'Thực thi: ${_formatDateTime(executedAt)}',
                                  style: TextStyle(
                                    color: Colors.grey[700],
                                    fontSize: 12,
                                  ),
                                ),
                                const Spacer(),
                                Text(
                                  'ID: $commandId',
                                  style: TextStyle(
                                    color: Colors.grey[500],
                                    fontSize: 10,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }



  Color _getDeviceColor() {
    switch (widget.device.type?.toLowerCase()) {
      case 'smart light':
      case 'light':
      case 'đèn':
        return Colors.amber;
      case 'light sensor':
      case 'sensor':
      case 'cảm biến':
        return Colors.green;
      case 'camera':
        return Colors.purple;
      case 'temperature sensor':
        return Colors.orange;
      case 'smart ac':
      case 'ac':
      case 'điều hòa':
        return Colors.blue;
      case 'smart switch':
      case 'switch':
      case 'công tắc':
        return Colors.teal;
      case 'fan':
      case 'quạt':
        return Colors.cyan;
      default:
        return Colors.grey;
    }
  }

  IconData _getDeviceIcon() {
    switch (widget.device.type?.toLowerCase()) {
      case 'smart light':
      case 'light':
      case 'đèn':
        return Icons.lightbulb_outline;
      case 'light sensor':
      case 'sensor':
      case 'cảm biến':
        return Icons.sensors;
      case 'camera':
        return Icons.camera_alt_outlined;
      case 'temperature sensor':
        return Icons.thermostat;
      case 'smart ac':
      case 'ac':
      case 'điều hòa':
        return Icons.ac_unit;
      case 'smart switch':
      case 'switch':
      case 'công tắc':
        return Icons.toggle_on_outlined;
      case 'fan':
      case 'quạt':
        return Icons.air;
      default:
        return Icons.device_unknown;
    }
  }

  IconData _getTypeIcon(String? typeName) {
    switch (typeName?.toLowerCase()) {
      case 'light':
        return Icons.lightbulb_outline;
      case 'fan':
        return Icons.air;
      case 'camera':
        return Icons.camera_alt_outlined;
      case 'sensor':
        return Icons.sensors;
      case 'switch':
        return Icons.toggle_on_outlined;
      default:
        return Icons.category;
    }
  }

  Color _getTypeColor(String? typeName) {
    switch (typeName?.toLowerCase()) {
      case 'light':
        return Colors.amber;
      case 'fan':
        return Colors.blue;
      case 'camera':
        return Colors.purple;
      case 'sensor':
        return Colors.green;
      case 'switch':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return dateString;
    }
  }

  String _formatDateTime(String dateString) {
    try {
      final date = DateTime.parse(dateString);
      final now = DateTime.now();
      final difference = now.difference(date);

      if (difference.inDays > 0) {
        return '${difference.inDays} ngày trước';
      } else if (difference.inHours > 0) {
        return '${difference.inHours} giờ trước';
      } else if (difference.inMinutes > 0) {
        return '${difference.inMinutes} phút trước';
      } else {
        return 'Vừa xong';
      }
    } catch (e) {
      return dateString;
    }
  }

  Color _getCommandColor(String command) {
    switch (command.toLowerCase()) {
      case 'turn_on':
        return Colors.green;
      case 'turn_off':
        return Colors.red;
      case 'toggle':
        return Colors.orange;
      case 'set_brightness':
        return Colors.amber;
      case 'set_temperature':
        return Colors.blue;
      default:
        return Colors.grey;
    }
  }

  IconData _getCommandIcon(String command) {
    switch (command.toLowerCase()) {
      case 'turn_on':
        return Icons.power_settings_new;
      case 'turn_off':
        return Icons.power_off;
      case 'toggle':
        return Icons.toggle_on;
      case 'set_brightness':
        return Icons.brightness_6;
      case 'set_temperature':
        return Icons.thermostat;
      default:
        return Icons.settings;
    }
  }

  String _getCommandDisplayName(String command) {
    switch (command.toLowerCase()) {
      case 'turn_on':
        return 'Bật thiết bị';
      case 'turn_off':
        return 'Tắt thiết bị';
      case 'toggle':
        return 'Chuyển đổi';
      case 'set_brightness':
        return 'Điều chỉnh độ sáng';
      case 'set_temperature':
        return 'Điều chỉnh nhiệt độ';
      default:
        return command;
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'success':
        return Colors.green;
      case 'failed':
      case 'error':
        return Colors.red;
      case 'pending':
        return Colors.orange;
      case 'cancelled':
        return Colors.grey;
      default:
        return Colors.blue;
    }
  }

  String _getStatusDisplayName(String status) {
    switch (status.toLowerCase()) {
      case 'success':
        return 'Thành công';
      case 'failed':
        return 'Thất bại';
      case 'error':
        return 'Lỗi';
      case 'pending':
        return 'Đang xử lý';
      case 'cancelled':
        return 'Đã hủy';
      default:
        return status;
    }
  }

  String _formatPropertyValue(dynamic value) {
    if (value == null) return 'null';
    if (value is bool) return value ? 'true' : 'false';
    if (value is num) return value.toString();
    if (value is String) return value;
    if (value is List) return '[${value.length} items]';
    if (value is Map) return '{${value.length} properties}';
    return value.toString();
  }

  void _showEditDeviceDialog() {
    final nameController = TextEditingController(text: widget.device.name);
    String selectedStatus = widget.device.status ?? 'active';

    showDialog(
      context: context,
      builder: (context) => StatefulBuilder(
        builder: (context, setState) => AlertDialog(
          title: const Text('Chỉnh sửa thiết bị'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(
                  labelText: 'Tên thiết bị',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.device_hub),
                ),
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: selectedStatus,
                decoration: const InputDecoration(
                  labelText: 'Trạng thái',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.info),
                ),
                items: const [
                  DropdownMenuItem(value: 'active', child: Text('Hoạt động')),
                  DropdownMenuItem(value: 'inactive', child: Text('Không hoạt động')),
                  DropdownMenuItem(value: 'maintenance', child: Text('Bảo trì')),
                  DropdownMenuItem(value: 'offline', child: Text('Ngoại tuyến')),
                ],
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      selectedStatus = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue[50],
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.blue[200]!),
                ),
                child: Row(
                  children: [
                    Icon(Icons.info, color: Colors.blue[600], size: 20),
                    const SizedBox(width: 8),
                    const Expanded(
                      child: Text(
                        'Lưu ý: Chỉ có thể cập nhật tên và trạng thái thiết bị',
                        style: TextStyle(fontSize: 12),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('Hủy'),
            ),
            ElevatedButton(
              onPressed: () async {
                Navigator.of(context).pop();
                await _updateDeviceInfo(nameController.text, selectedStatus);
              },
              child: const Text('Lưu'),
            ),
          ],
        ),
      ),
    );
  }

  void _showMoveDeviceDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Chuyển thiết bị sang khu vực khác'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.move_to_inbox, size: 48, color: Colors.blue[300]),
            const SizedBox(height: 16),
            const Text(
              'Chọn khu vực mới cho thiết bị:',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            // NOTE: Area selection feature is planned for future release
            // This would require loading areas from HomeViewModel
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange[50],
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange[200]!),
              ),
              child: Row(
                children: [
                  Icon(Icons.info, color: Colors.orange[600], size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      'Tính năng chuyển khu vực đang được phát triển',
                      style: TextStyle(fontSize: 12),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // NOTE: Move device functionality planned for future release
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Tính năng chuyển khu vực đang được phát triển'),
                  backgroundColor: Colors.orange,
                ),
              );
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('Chuyển'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDeviceDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Xác nhận xóa'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.warning, size: 48, color: Colors.red[300]),
            const SizedBox(height: 16),
            Text(
              'Bạn có chắc chắn muốn xóa thiết bị "${widget.device.name}" khỏi hệ thống?',
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            const Text(
              'Hành động này không thể hoàn tác!',
              style: TextStyle(
                color: Colors.red,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _deleteDevice();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red[600],
              foregroundColor: Colors.white,
            ),
            child: const Text('Xóa'),
          ),
        ],
      ),
    );
  }

  void _showEditPropertyDialog(String key, dynamic value) {
    final controller = TextEditingController(text: value.toString());

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Chỉnh sửa thuộc tính: $key'),
        content: TextField(
          controller: controller,
          decoration: const InputDecoration(
            labelText: 'Giá trị',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _updateProperty(key, controller.text);
            },
            child: const Text('Cập nhật'),
          ),
        ],
      ),
    );
  }

  void _executeCommand(Map<String, dynamic> command) {
    final commandName = command['name'] ?? '';
    final commandValue = command['value'] ?? '';

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Thực thi lệnh: $commandName'),
        content: Text('Bạn có muốn thực thi lệnh "$commandName" không?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Hủy'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _runCommand(commandName, commandValue);
            },
            child: const Text('Thực thi'),
          ),
        ],
      ),
    );
  }

  Future<void> _updateDeviceInfo(String name, String status) async {
    final deviceViewModel = context.read<DeviceViewModel>();

    print('🔍 [DEBUG] Updating device info:');
    print('  - Device ID: ${widget.device.id}');
    print('  - Name: $name');
    print('  - Status: $status');

    final success = await deviceViewModel.updateDeviceInfo(
      deviceId: widget.device.id,
      name: name.isNotEmpty ? name : null,
      status: status,
    );

    if (mounted) {
      if (success) {
        _showSnackBar(
          deviceViewModel.successMessage ?? 'Đã cập nhật thông tin thiết bị',
          Colors.green,
        );
        // Reload device info to show updated data
        _loadDeviceInfo();
      } else if (deviceViewModel.errorMessage != null) {
        _showSnackBar(
          deviceViewModel.errorMessage!,
          Colors.red,
        );
      }
    }
  }

  Future<void> _deleteDevice() async {
    final deviceViewModel = context.read<DeviceViewModel>();

    final success = await deviceViewModel.deleteDevice(widget.device.id);

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Đã xóa thiết bị "${widget.device.name}" khỏi hệ thống'),
            backgroundColor: Colors.green,
          ),
        );
        // Return true to indicate device was deleted successfully
        Navigator.of(context).pop(true);
      } else if (deviceViewModel.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(deviceViewModel.errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _updateProperty(String key, String value) async {
    final deviceViewModel = context.read<DeviceViewModel>();

    final success = await deviceViewModel.updateDeviceProperty(
      homeId: widget.home.id,
      deviceId: widget.device.id,
      property: key,
      value: value,
    );

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Đã cập nhật thuộc tính "$key"'),
            backgroundColor: Colors.green,
          ),
        );
        // Reload properties
        _loadDeviceInfo();
      } else if (deviceViewModel.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(deviceViewModel.errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _runCommand(String command, String value) async {
    final deviceViewModel = context.read<DeviceViewModel>();

    final success = await deviceViewModel.controlDevice(
      homeId: widget.home.id,
      deviceId: widget.device.id,
      command: command,
      value: value,
    );

    if (mounted) {
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Đã thực thi lệnh "$command"'),
            backgroundColor: Colors.green,
          ),
        );
      } else if (deviceViewModel.errorMessage != null) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(deviceViewModel.errorMessage!),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showSnackBar(String message, Color backgroundColor) {
    if (mounted) {
      _scaffoldMessenger.showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: backgroundColor,
        ),
      );
    }
  }
}
