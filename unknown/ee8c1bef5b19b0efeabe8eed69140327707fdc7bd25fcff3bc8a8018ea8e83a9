import 'package:fe_flutter/auth_wrapper.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'core/utils/view_model_locator.dart';
import 'features/auth/pages/login_page.dart';
import 'features/auth/pages/register_page.dart';
import 'features/auth/pages/forgot_password_page.dart';
import 'features/auth/pages/reset_password_page.dart';
import 'features/auth/pages/otp_verification_page.dart';
import 'features/auth/pages/email_verification_page.dart';
import 'features/home/<USER>/home_page.dart';
import 'features/home/<USER>/area_devices_page.dart';
import 'core/models/home_model.dart';
import 'core/models/area_model.dart';

void main() {
  runApp(const SmartHomeApp());
}

class SmartHomeApp extends StatelessWidget {
  const SmartHomeApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: ViewModelLocator.instance.providers,
      child: MaterialApp(
        title: 'Smart Home',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          fontFamily: 'Roboto',
          visualDensity: VisualDensity.adaptivePlatformDensity,
        ),
        debugShowCheckedModeBanner: false,
        home: const AuthWrapper(),
        routes: {
          '/login': (context) => const LoginPage(),
          '/register': (context) => const RegisterPage(),
          '/forgot-password': (context) => const ForgotPasswordPage(),
          '/home': (context) => const HomePage(),
        },
        onGenerateRoute: (settings) {
          if (settings.name == '/otp-verification') {
            final args = settings.arguments as Map<String, dynamic>?;
            return MaterialPageRoute(
              builder: (context) => OTPVerificationPage(
                email: args?['email'] ?? '',
              ),
            );
          } else if (settings.name == '/email-verification') {
            final args = settings.arguments as Map<String, dynamic>?;
            return MaterialPageRoute(
              builder: (context) => EmailVerificationPage(
                email: args?['email'] ?? '',
              ),
            );
          } else if (settings.name == '/reset-password') {
            final email = settings.arguments as String;
            return MaterialPageRoute(
              builder: (context) => ResetPasswordPage(email: email),
            );
          } else if (settings.name == '/area-devices') {
            final args = settings.arguments as Map<String, dynamic>?;
            final home = args?['home'] as Home?;
            final area = args?['area'] as Area?;

            if (home != null && area != null) {
              return MaterialPageRoute(
                builder: (context) => AreaDevicesPage(
                  home: home,
                  area: area,
                ),
              );
            }
          }
          return null;
        },
      ),
    );
  }
}