import 'package:flutter/material.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/models/automation_models.dart';

class RuleTypeSelectionStep extends StatelessWidget {
  final String selectedType;
  final Function(String) onTypeSelected;

  const RuleTypeSelectionStep({
    super.key,
    required this.selectedType,
    required this.onTypeSelected,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          
          const Text(
            'Chọn loại tự động hóa',
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppTheme.textPrimary,
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppTheme.infoBlue.withOpacity(0.1),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: AppTheme.infoBlue.withOpacity(0.3)),
            ),
            child: const Row(
              children: [
                Icon(Icons.lightbulb_outline, color: AppTheme.infoBlue),
                SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Chọn cách bạn muốn thiết bị hoạt động tự động',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppTheme.textSecondary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 32),
          
          // Schedule-based Rule
          _buildRuleTypeCard(
            type: AutomationRuleType.schedule,
            title: 'Lịch trình tự động',
            subtitle: 'Thiết bị hoạt động theo giờ đã định',
            description: 'Thiết lập thời gian cụ thể để bật/tắt thiết bị hàng ngày',
            icon: Icons.schedule,
            color: AppTheme.infoBlue,
            examples: [
              'Bật đèn phòng khách lúc 6:00 sáng',
              'Tắt tất cả thiết bị lúc 23:00',
              'Bật điều hòa trước khi về nhà',
            ],
          ),
          
          const SizedBox(height: 20),
          
          // Condition-based Rule
          _buildRuleTypeCard(
            type: AutomationRuleType.condition,
            title: 'Cảm biến tự động',
            subtitle: 'Thiết bị phản ứng với cảm biến',
            description: 'Tạo automation dựa trên dữ liệu từ các cảm biến trong nhà',
            icon: Icons.sensors,
            color: AppTheme.warningOrange,
            examples: [
              'Có người → Bật đèn phòng khách',
              'Nhiệt độ > 30°C → Bật điều hòa',
              'Độ ẩm > 70% → Bật máy hút ẩm',
            ],
          ),
          
          const SizedBox(height: 20),
          
          // IF-THEN Rule (Advanced)
          _buildRuleTypeCard(
            type: AutomationRuleType.ifThen,
            title: 'Logic thông minh',
            subtitle: 'NẾU điều kiện THÌ hành động',
            description: 'Kết hợp nhiều điều kiện với logic AND/OR để tạo quy tắc phức tạp',
            icon: Icons.psychology,
            color: AppTheme.secondaryPurple,
            isAdvanced: true,
            examples: [
              'NẾU trời tối VÀ có người → THÌ bật đèn',
              'NẾU không có người HOẶC sau 22:00 → THÌ tắt thiết bị',
              'NẾU mưa VÀ cửa sổ mở → THÌ gửi cảnh báo',
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRuleTypeCard({
    required String type,
    required String title,
    required String subtitle,
    required String description,
    required IconData icon,
    required Color color,
    required List<String> examples,
    bool isAdvanced = false,
  }) {
    final isSelected = selectedType == type;
    
    return Card(
      elevation: isSelected ? 8 : 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: isSelected ? color : Colors.transparent,
          width: 2,
        ),
      ),
      child: InkWell(
        onTap: () => onTypeSelected(type),
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: color.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      icon,
                      color: color,
                      size: 24,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Text(
                              title,
                              style: const TextStyle(
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                                color: AppTheme.textPrimary,
                              ),
                            ),
                            if (isAdvanced) ...[
                              const SizedBox(width: 8),
                              
                            ],
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          subtitle,
                          style: const TextStyle(
                            fontSize: 14,
                            color: AppTheme.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (isSelected)
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.check,
                        color: AppTheme.surfaceWhite,
                        size: 16,
                      ),
                    ),
                ],
              ),
              
              const SizedBox(height: 16),
              
              // Description
              Text(
                description,
                style: const TextStyle(
                  fontSize: 14,
                  color: AppTheme.textSecondary,
                  height: 1.4,
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Examples
              const Text(
                'Ví dụ:',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textPrimary,
                ),
              ),
              const SizedBox(height: 8),
              ...examples.map((example) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: 4,
                      height: 4,
                      margin: const EdgeInsets.only(top: 8, right: 8),
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                      ),
                    ),
                    Expanded(
                      child: Text(
                        example,
                        style: const TextStyle(
                          fontSize: 13,
                          color: AppTheme.textSecondary,
                          height: 1.3,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
            ],
          ),
        ),
      ),
    );
  }
}
