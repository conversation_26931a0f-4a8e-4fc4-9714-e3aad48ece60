import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'otp_model.g.dart';

@JsonSerializable()
class VerifyOTPRequest extends Equatable {
  final String email;
  final String otp;

  const VerifyOTPRequest({
    required this.email,
    required this.otp,
  });

  factory VerifyOTPRequest.fromJson(Map<String, dynamic> json) => _$VerifyOTPRequestFromJson(json);
  Map<String, dynamic> toJson() => _$VerifyOTPRequestToJson(this);

  @override
  List<Object> get props => [email, otp];
}

@JsonSerializable()
class ResendOTPRequest extends Equatable {
  final String email;

  const ResendOTPRequest({
    required this.email,
  });

  factory ResendOTPRequest.fromJson(Map<String, dynamic> json) => _$ResendOTPRequestFromJson(json);
  Map<String, dynamic> toJson() => _$ResendOTPRequestToJson(this);

  @override
  List<Object> get props => [email];
}

@JsonSerializable()
class ForgotPasswordRequest extends Equatable {
  final String email;

  const ForgotPasswordRequest({
    required this.email,
  });

  factory ForgotPasswordRequest.fromJson(Map<String, dynamic> json) => _$ForgotPasswordRequestFromJson(json);
  Map<String, dynamic> toJson() => _$ForgotPasswordRequestToJson(this);

  @override
  List<Object> get props => [email];
}

@JsonSerializable()
class ResetPasswordRequest extends Equatable {
  final String email;
  final String otp;
  @JsonKey(name: 'new_password')
  final String newPassword;

  const ResetPasswordRequest({
    required this.email,
    required this.otp,
    required this.newPassword,
  });

  factory ResetPasswordRequest.fromJson(Map<String, dynamic> json) => _$ResetPasswordRequestFromJson(json);
  Map<String, dynamic> toJson() => _$ResetPasswordRequestToJson(this);

  @override
  List<Object> get props => [email, otp, newPassword];
}

@JsonSerializable()
class VerifyOTPResponse extends Equatable {
  final String message;
  final bool success;
  final String token;
  final Map<String, dynamic> user;

  const VerifyOTPResponse({
    required this.message,
    required this.success,
    required this.token,
    required this.user,
  });

  factory VerifyOTPResponse.fromJson(Map<String, dynamic> json) => _$VerifyOTPResponseFromJson(json);
  Map<String, dynamic> toJson() => _$VerifyOTPResponseToJson(this);

  @override
  List<Object> get props => [message, success, token, user];
}
