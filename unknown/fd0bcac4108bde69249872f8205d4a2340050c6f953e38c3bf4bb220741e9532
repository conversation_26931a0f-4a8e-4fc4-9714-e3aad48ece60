import 'package:flutter/widgets.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../../core/base/base_view_model.dart';
import '../../../core/models/automation_models.dart';
import '../../../core/repositories/automation_repository.dart';
import '../../../core/repositories/home_repository.dart';

class AutomationViewModel extends BaseViewModel {
  final AutomationRepository _automationRepository = AutomationRepository();

  // State variables
  List<AutomationRule> _automationRules = [];
  String _selectedFilter = 'all';
  int? _currentHomeId;
  bool _isLoadingRules = false;
  DateTime? _lastLoadTime;
  static const Duration _debounceDelay = Duration(seconds: 2);

  // Helper method to get token
  Future<String?> _getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString('auth_token');
  }

  // Getters
  List<AutomationRule> get automationRules => _automationRules;
  String get selectedFilter => _selectedFilter;

  // Filtered rules based on selected filter
  List<AutomationRule> get filteredRules {
    switch (_selectedFilter) {
      case 'active':
        return _automationRules.where((rule) => rule.isActive).toList();
      case 'inactive':
        return _automationRules.where((rule) => !rule.isActive).toList();
      case 'schedule':
        return _automationRules.where((rule) => rule.ruleType == AutomationRuleType.schedule).toList();
      case 'condition':
        return _automationRules.where((rule) => rule.ruleType == AutomationRuleType.condition).toList();
      default:
        return _automationRules;
    }
  }

  // Set current home ID
  void setCurrentHomeId(int homeId) {
    if (homeId <= 0) {
      print('🔍 [DEBUG] AutomationViewModel - Invalid homeId: $homeId, not setting');
      return;
    }

    // Chỉ set và notify nếu homeId thay đổi
    if (_currentHomeId != homeId) {
      print('🔍 [DEBUG] AutomationViewModel - Setting homeId from ${_currentHomeId} to: $homeId');
      _currentHomeId = homeId;
      delayedNotifyListeners(); // Sử dụng delayed để tránh loop

      // Auto load rules khi homeId thay đổi
      WidgetsBinding.instance.addPostFrameCallback((_) {
        loadAutomationRules();
      });
    } else {
      print('🔍 [DEBUG] AutomationViewModel - homeId $homeId already set, skipping...');
    }
  }

  // Auto-load homeId from user's single home
  Future<void> autoLoadHomeId() async {
    if (_currentHomeId != null && _currentHomeId! > 0) {
      print('🔍 [DEBUG] AutomationViewModel - homeId already set: $_currentHomeId');
      return;
    }

    try {
      final token = await _getToken();
      if (token == null) {
        print('[DEBUG] AutomationViewModel - No token found');
        return;
      }

      // Import HomeRepository to get user's homes
      final homeRepository = HomeRepository();
      final response = await homeRepository.getUserHomes(token);

      if (response.success && response.data != null && response.data!.isNotEmpty) {
        final homeId = response.data!.first.homeId;
        print('[DEBUG] AutomationViewModel - Auto-loaded homeId: $homeId');
        _currentHomeId = homeId;
        notifyListeners();
      } else {
        print('[DEBUG] AutomationViewModel - No homes found');
      }
    } catch (e) {
      print('[DEBUG] AutomationViewModel - Error auto-loading homeId: $e');
    }
  }

  // Set filter
  void setFilter(String filter) {
    _selectedFilter = filter;
    notifyListeners();
  }

  // Load automation rules
  Future<void> loadAutomationRules() async {
    if (_currentHomeId == null) return;

    // Debounce mechanism
    if (_isLoadingRules) {
      print('🔍 [DEBUG] loadAutomationRules already in progress, skipping...');
      return;
    }

    final now = DateTime.now();
    if (_lastLoadTime != null && now.difference(_lastLoadTime!) < _debounceDelay) {
      print('🔍 [DEBUG] loadAutomationRules - Debounced, skipping...');
      return;
    }

    _isLoadingRules = true;
    _lastLoadTime = now;
    setLoading(true);

    try {
      final token = await _getToken();
      if (token == null) {
        setError('Không tìm thấy token xác thực');
        return;
      }

      final response = await _automationRepository.getAutomationRules(_currentHomeId!, token);

      if (response.success && response.data != null) {
        _automationRules = response.data!;
        setMessage(response.message);
      } else {
        setError(response.message);
      }
    } catch (e) {
      setError('Lỗi khi tải danh sách automation rules: $e');
    } finally {
      _isLoadingRules = false;
      setLoading(false);
    }
  }

  // Create schedule rule
  Future<bool> createScheduleRule(AutomationRule rule) async {
    setLoading(true);
    try {
      final token = await _getToken();
      if (token == null) {
        setError('Không tìm thấy token xác thực');
        return false;
      }

      final response = await _automationRepository.createScheduleRule(rule, token);

      if (response.success && response.data != null) {
        _automationRules.add(response.data!);
        setMessage(response.message);
        notifyListeners();
        return true;
      } else {
        setError(response.message);
        return false;
      }
    } catch (e) {
      setError('Lỗi khi tạo schedule rule: $e');
      return false;
    } finally {
      setLoading(false);
    }
  }

  // Create condition rule
  Future<bool> createConditionRule(AutomationRule rule) async {
    setLoading(true);
    try {
      final token = await _getToken();
      if (token == null) {
        setError('Không tìm thấy token xác thực');
        return false;
      }

      final response = await _automationRepository.createConditionRule(rule, token);

      if (response.success && response.data != null) {
        _automationRules.add(response.data!);
        setMessage(response.message);
        notifyListeners();
        return true;
      } else {
        setError(response.message);
        return false;
      }
    } catch (e) {
      setError('Lỗi khi tạo condition rule: $e');
      return false;
    } finally {
      setLoading(false);
    }
  }

  // Update automation rule
  Future<bool> updateAutomationRule(AutomationRule rule) async {
    if (_currentHomeId == null || rule.ruleId == null) return false;

    setLoading(true);
    try {
      final token = await _getToken();
      if (token == null) {
        setError('Không tìm thấy token xác thực');
        return false;
      }

      final response = await _automationRepository.updateAutomationRule(
        _currentHomeId!,
        rule.ruleId!,
        rule,
        token
      );

      if (response.success && response.data != null) {
        final index = _automationRules.indexWhere((r) => r.ruleId == rule.ruleId);
        if (index != -1) {
          _automationRules[index] = response.data!;
        }
        setMessage(response.message);
        notifyListeners();
        return true;
      } else {
        setError(response.message);
        return false;
      }
    } catch (e) {
      setError('Lỗi khi cập nhật automation rule: $e');
      return false;
    } finally {
      setLoading(false);
    }
  }

  // Toggle rule status
  Future<bool> toggleRuleStatus(int ruleId, bool isActive) async {
    if (_currentHomeId == null) return false;

    try {
      final token = await _getToken();
      if (token == null) {
        setError('Không tìm thấy token xác thực');
        return false;
      }

      final response = await _automationRepository.toggleRuleStatus(
        _currentHomeId!,
        ruleId,
        isActive,
        token
      );

      if (response.success) {
        final index = _automationRules.indexWhere((r) => r.ruleId == ruleId);
        if (index != -1) {
          _automationRules[index] = _automationRules[index].copyWith(isActive: isActive);
        }
        setMessage(response.message);
        notifyListeners();
        return true;
      } else {
        setError(response.message);
        return false;
      }
    } catch (e) {
      setError('Lỗi khi thay đổi trạng thái rule: $e');
      return false;
    }
  }

  // Execute rule manually
  Future<bool> executeRule(int ruleId) async {
    if (_currentHomeId == null) return false;

    setLoading(true);
    try {
      final token = await _getToken();
      if (token == null) {
        setError('Không tìm thấy token xác thực');
        return false;
      }

      final response = await _automationRepository.executeRule(_currentHomeId!, ruleId, token);

      if (response.success) {
        setMessage(response.message);
        // Reload automation rules to update last executed time
        await loadAutomationRules();
        return true;
      } else {
        setError(response.message);
        return false;
      }
    } catch (e) {
      setError('Lỗi khi thực thi rule: $e');
      return false;
    } finally {
      setLoading(false);
    }
  }



  // Delete automation rule
  Future<bool> deleteAutomationRule(int ruleId) async {
    print('🔍 [DEBUG] AutomationViewModel - deleteAutomationRule called with ruleId: $ruleId');
    print('🔍 [DEBUG] AutomationViewModel - _currentHomeId: $_currentHomeId');

    if (_currentHomeId == null || _currentHomeId! <= 0) {
      print('🔍 [DEBUG] AutomationViewModel - _currentHomeId is null/invalid, trying to auto-load');
      await autoLoadHomeId();

      if (_currentHomeId == null || _currentHomeId! <= 0) {
        print('🔍 [DEBUG] AutomationViewModel - Still no valid homeId after auto-load, returning false');
        setError('Không thể xác định home ID');
        return false;
      }
    }

    setLoading(true);
    try {
      final token = await _getToken();
      if (token == null) {
        setError('Không tìm thấy token xác thực');
        return false;
      }

      print('🔍 [DEBUG] AutomationViewModel - Calling deleteAutomationRule with homeId: $_currentHomeId, ruleId: $ruleId');
      final response = await _automationRepository.deleteAutomationRule(_currentHomeId!, ruleId, token);

      if (response.success) {
        _automationRules.removeWhere((rule) => rule.ruleId == ruleId);
        setMessage(response.message);
        notifyListeners();
        return true;
      } else {
        setError(response.message);
        return false;
      }
    } catch (e) {
      setError('Lỗi khi xóa automation rule: $e');
      return false;
    } finally {
      setLoading(false);
    }
  }

  // Refresh data
  Future<void> refresh() async {
    await loadAutomationRules();
  }

  // Clear data
  void clearData() {
    _automationRules.clear();
    _selectedFilter = 'all';
    _currentHomeId = null;
    notifyListeners();
  }

  // Get rule by ID
  AutomationRule? getRuleById(int ruleId) {
    try {
      return _automationRules.firstWhere((rule) => rule.ruleId == ruleId);
    } catch (e) {
      return null;
    }
  }

  // Get rules count by type
  int getRulesCountByType(String type) {
    switch (type) {
      case 'active':
        return _automationRules.where((rule) => rule.isActive).length;
      case 'inactive':
        return _automationRules.where((rule) => !rule.isActive).length;
      case 'schedule':
        return _automationRules.where((rule) => rule.ruleType == AutomationRuleType.schedule).length;
      case 'condition':
        return _automationRules.where((rule) => rule.ruleType == AutomationRuleType.condition).length;
      default:
        return _automationRules.length;
    }
  }


}
